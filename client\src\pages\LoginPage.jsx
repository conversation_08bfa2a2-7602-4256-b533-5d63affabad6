import React, { useState } from 'react'
import { createTheme, ThemeProvider } from '@mui/material/styles'
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Container,
  Avatar,
  ToggleButton,
  ToggleButtonGroup,
  IconButton,
  Tooltip
} from '@mui/material'
import {
  LockOutlined,
  Person,
  Business,
  ContentCopy,
  CheckCircle
} from '@mui/icons-material'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { useSnackbar } from 'notistack'
import CopyButton from '../components/common/CopyButton'

// إنشاء ثيم مخصص مع خط Khalid-Art
const customTheme = createTheme({
  typography: {
    fontFamily: [
      'Khalid-Art-bold',
      'Arial',
      'sans-serif'
    ].join(','),
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @font-face {
          font-family: 'Khalid-Art-bold';
          src: url('./fonts/Khalid-Art-bold.ttf') format('truetype');
          font-weight: bold;
          font-style: normal;
        }
      `,
    },
  },
})

const LoginPage = () => {
  const [loginType, setLoginType] = useState('user') // 'user' or 'client'
  const [formData, setFormData] = useState({
    loginName: '',
    password: '',
    deviceId: '',
    clientCode: '',
    clientPassword: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [copySuccess, setCopySuccess] = useState(false)
  const [approvalDialog, setApprovalDialog] = useState({
    open: false,
    userId: null,
    deviceId: ''
  })

  const { login, clientLogin, approveDevice } = useAuth()
  const navigate = useNavigate()
  const { enqueueSnackbar } = useSnackbar()

  // توليد معرف جهاز فريد بالتنسيق المطلوب: gtx######_timestamp
  React.useEffect(() => {
    const generateDeviceId = () => {
      let deviceId = localStorage.getItem('deviceId')

      // إذا كان هناك رقم جهاز محفوظ وبالتنسيق الصحيح
      if (deviceId && deviceId.startsWith('gtx') && deviceId.includes('_')) {
        setFormData(prev => ({ ...prev, deviceId }))
      } else {
        // إنشاء رقم جهاز جديد بالتنسيق: gtx######_timestamp
        const randomPrefix = 'gtx' + Math.floor(Math.random() * 900000 + 100000)
        const timestamp = Date.now()
        const newDeviceId = randomPrefix + '_' + timestamp
        localStorage.setItem('deviceId', newDeviceId)
        setFormData(prev => ({ ...prev, deviceId: newDeviceId }))
      }
    }
    generateDeviceId()
  }, [])

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  const handleLoginTypeChange = (event, newLoginType) => {
    if (newLoginType !== null) {
      setLoginType(newLoginType)
      setError('')
    }
  }

  const handleCopyDeviceId = async () => {
    try {
      // التحقق من وجود معرف الجهاز
      if (!formData.deviceId || formData.deviceId.trim() === '') {
        enqueueSnackbar('لا يوجد معرف جهاز للنسخ', { variant: 'warning' })
        return
      }

      // محاولة النسخ باستخدام Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(formData.deviceId)
      } else {
        // طريقة بديلة للنسخ في المتصفحات القديمة
        const textArea = document.createElement('textarea')
        textArea.value = formData.deviceId
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
      }

      setCopySuccess(true)
      enqueueSnackbar('تم نسخ معرف الجهاز بنجاح!', { variant: 'success' })
      setTimeout(() => setCopySuccess(false), 2000)
    } catch (err) {
      console.error('Copy error:', err)
      enqueueSnackbar('فشل في نسخ معرف الجهاز. جرب النسخ يدوياً.', { variant: 'error' })
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      if (loginType === 'user') {
        // تسجيل دخول المستخدم
        const result = await login(formData.loginName, formData.password, formData.deviceId)
        if (result.success) {
          enqueueSnackbar('تم تسجيل الدخول بنجاح!', { variant: 'success' })
          navigate('/')
        } else {
          let errorMessage = result.error || 'حدث خطأ في تسجيل الدخول'

          // إضافة تفاصيل إضافية للأخطاء المتعلقة بالأجهزة
          if (result.details && result.details.authorizedDevices) {
            errorMessage += `\n\nالأجهزة المسموحة: ${result.details.authorizedDevices.join(', ')}\nالجهاز الحالي: ${result.details.currentDevice}`
          }

          setError(errorMessage)
          enqueueSnackbar(result.error || 'فشل في تسجيل الدخول', { variant: 'error' })
        }
      } else {
        // تسجيل دخول العميل
        const result = await clientLogin(formData.clientCode, formData.clientPassword)
        if (result.success) {
          enqueueSnackbar('تم تسجيل الدخول بنجاح!', { variant: 'success' })
          navigate('/client-dashboard')
        } else {
          setError(result.error || 'فشل في تسجيل الدخول. يرجى التحقق من البيانات')
          enqueueSnackbar(result.error || 'فشل في تسجيل الدخول', { variant: 'error' })
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      setError('حدث خطأ في تسجيل الدخول')
      enqueueSnackbar('حدث خطأ في تسجيل الدخول', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleApproveDevice = async () => {
    const result = await approveDevice(approvalDialog.userId, approvalDialog.deviceId)

    if (result.success) {
      enqueueSnackbar('تم الموافقة على الجهاز بنجاح. يمكنك الآن تسجيل الدخول.', { variant: 'success' })
      setApprovalDialog({ open: false, userId: null, deviceId: '' })
    } else {
      enqueueSnackbar(result.error, { variant: 'error' })
    }
  }

  return (
    <ThemeProvider theme={customTheme}>
      <Container component="main" maxWidth="sm">
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          py: 4
        }}
      >
        <Card sx={{
          width: '100%',
          maxWidth: 500,
          borderRadius: 4,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
          border: '1px solid rgba(0,0,0,0.05)'
        }}>
          <CardContent sx={{ p: 5 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                mb: 3
              }}
            >
              <Avatar sx={{
                m: 2,
                bgcolor: 'primary.main',
                width: 80,
                height: 80,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'scale(1.05)',
                  boxShadow: '0 12px 35px rgba(102, 126, 234, 0.4)'
                }
              }}>
                <LockOutlined sx={{ fontSize: 40 }} />
              </Avatar>
              <Typography component="h1" variant="h4" sx={{ fontWeight: 700, color: 'primary.main', fontSize: '1.8rem', lineHeight: 1.2 }}>
                نظام ادارة التطبيقات الموحد
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                مرحباً بك في نظام الإدارة المتكامل
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            {/* أزرار اختيار نوع الدخول */}
            <Box sx={{ mb: 4, display: 'flex', justifyContent: 'center' }}>
              <ToggleButtonGroup
                value={loginType}
                exclusive
                onChange={handleLoginTypeChange}
                aria-label="نوع تسجيل الدخول"
                sx={{
                  background: 'linear-gradient(145deg, #f8f9fa, #e9ecef)',
                  borderRadius: 3,
                  padding: 1,
                  boxShadow: 'inset 0 2px 4px rgba(0,0,0,0.1)',
                  '& .MuiToggleButton-root': {
                    px: 4,
                    py: 2,
                    borderRadius: 2,
                    fontWeight: 'bold',
                    fontSize: '1rem',
                    border: 'none',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
                    },
                    '&.Mui-selected': {
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      color: 'white',
                      boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)',
                      transform: 'translateY(-2px)',
                      '&:hover': {
                        background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                        boxShadow: '0 8px 25px rgba(102, 126, 234, 0.4)'
                      }
                    }
                  }
                }}
              >
                <ToggleButton value="user" aria-label="دخول مستخدم">
                  <Person sx={{ mr: 1, fontSize: 24 }} />
                  دخول مستخدم
                </ToggleButton>
                <ToggleButton value="client" aria-label="دخول عميل">
                  <Business sx={{ mr: 1, fontSize: 24 }} />
                  دخول عميل
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>

            <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
              {loginType === 'user' ? (
                <>
                  <TextField
                    margin="normal"
                    required
                    fullWidth
                    id="loginName"
                    label="اسم المستخدم"
                    name="loginName"
                    autoComplete="username"
                    autoFocus
                    value={formData.loginName}
                    onChange={handleChange}
                    disabled={loading}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
                        },
                        '&.Mui-focused': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 6px 20px rgba(102, 126, 234, 0.2)'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: <Person sx={{ mr: 1, color: 'primary.main', fontSize: 24 }} />
                    }}
                  />
                  <TextField
                    margin="normal"
                    required
                    fullWidth
                    name="password"
                    label="كلمة المرور"
                    type="password"
                    id="password"
                    autoComplete="current-password"
                    value={formData.password}
                    onChange={handleChange}
                    disabled={loading}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
                        },
                        '&.Mui-focused': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 6px 20px rgba(102, 126, 234, 0.2)'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: <LockOutlined sx={{ mr: 1, color: 'primary.main', fontSize: 24 }} />
                    }}
                  />
                </>
              ) : (
                <>
                  <TextField
                    margin="normal"
                    required
                    fullWidth
                    id="clientCode"
                    label="رمز العميل"
                    name="clientCode"
                    type="number"
                    autoFocus
                    value={formData.clientCode}
                    onChange={handleChange}
                    disabled={loading}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
                        },
                        '&.Mui-focused': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 6px 20px rgba(102, 126, 234, 0.2)'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: <Business sx={{ mr: 1, color: 'primary.main', fontSize: 24 }} />
                    }}
                  />
                  <TextField
                    margin="normal"
                    required
                    fullWidth
                    name="clientPassword"
                    label="كلمة المرور"
                    type="password"
                    id="clientPassword"
                    value={formData.clientPassword}
                    onChange={handleChange}
                    disabled={loading}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 4px 15px rgba(0,0,0,0.1)'
                        },
                        '&.Mui-focused': {
                          transform: 'translateY(-2px)',
                          boxShadow: '0 6px 20px rgba(102, 126, 234, 0.2)'
                        }
                      }
                    }}
                    InputProps={{
                      startAdornment: <LockOutlined sx={{ mr: 1, color: 'primary.main', fontSize: 24 }} />
                    }}
                  />
                </>
              )}

              {/* عرض معرف الجهاز مع زر النسخ - فقط للمستخدمين */}
              {loginType === 'user' && (
                <Box sx={{
                  mt: 2,
                  p: 2,
                  border: '1px solid',
                  borderColor: 'divider',
                  borderRadius: 2,
                  backgroundColor: 'background.paper',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2
                }}>
                  <Box sx={{ flex: 1 }}>
                    <Typography variant="body2" fontWeight="medium" color="text.primary">
                      معرف الجهاز:
                    </Typography>
                    <Typography
                      variant="body2"
                      sx={{
                        fontFamily: 'monospace',
                        backgroundColor: 'grey.100',
                        px: 1,
                        py: 0.5,
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: 'grey.300',
                        mt: 0.5,
                        fontSize: '0.875rem'
                      }}
                    >
                      {formData.deviceId}
                    </Typography>
                  </Box>
                  <Tooltip title="نسخ معرف الجهاز">
                    <IconButton
                      onClick={handleCopyDeviceId}
                      sx={{
                        bgcolor: copySuccess ? 'success.main' : 'primary.main',
                        color: 'white',
                        '&:hover': {
                          bgcolor: copySuccess ? 'success.dark' : 'primary.dark',
                          transform: 'scale(1.05)'
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {copySuccess ? <CheckCircle /> : <ContentCopy />}
                    </IconButton>
                  </Tooltip>
                </Box>
              )}

              <Button
                type="submit"
                fullWidth
                variant="contained"
                sx={{
                  mt: 4,
                  mb: 2,
                  py: 2,
                  fontSize: '1.2rem',
                  fontWeight: 'bold',
                  borderRadius: 3,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
                    transform: 'translateY(-3px)',
                    boxShadow: '0 12px 35px rgba(102, 126, 234, 0.4)'
                  },
                  '&:active': {
                    transform: 'translateY(-1px)',
                    boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)'
                  },
                  '&:disabled': {
                    background: 'linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%)',
                    transform: 'none',
                    boxShadow: '0 4px 15px rgba(149, 165, 166, 0.3)'
                  }
                }}
                disabled={loading}
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  <>
                    {loginType === 'user' ? (
                      <>
                        <Person sx={{ mr: 1 }} />
                        تسجيل دخول المستخدم
                      </>
                    ) : (
                      <>
                        <Business sx={{ mr: 1 }} />
                        تسجيل دخول العميل
                      </>
                    )}
                  </>
                )}
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Dialog للموافقة على الجهاز */}
        <Dialog open={approvalDialog.open} onClose={() => setApprovalDialog({ open: false, userId: null, deviceId: '' })}>
          <DialogTitle>الموافقة على جهاز جديد</DialogTitle>
          <DialogContent>
            <Typography>
              هذا الجهاز غير مصرح له بالوصول. هل تريد الموافقة على هذا الجهاز؟
            </Typography>
            <Typography variant="caption" sx={{ mt: 2, display: 'block' }}>
              معرف الجهاز: {approvalDialog.deviceId}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setApprovalDialog({ open: false, userId: null, deviceId: '' })}>
              إلغاء
            </Button>
            <Button onClick={handleApproveDevice} variant="contained">
              موافقة
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
    </ThemeProvider>
  )
}

export default LoginPage
