﻿import React, { useState } from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typo<PERSON>,
  Box,
  Chip,
  Divider,
  Card,
  CardContent,
  IconButton,
  TextField,
  Alert
} from '@mui/material'
import {
  Person as PersonIcon,
  Vpn<PERSON>ey as VpnKeyIcon,
  Security as SecurityIcon,
  CalendarToday as CalendarIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  AdminPanelSettings as AdminIcon,
  AccountCircle as UserIcon
} from '@mui/icons-material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useMutation } from 'react-query'
import { useAuth } from '../../contexts/AuthContext'
import { useSnackbar } from 'notistack'

// Schema للتحقق من كلمة المرور
const passwordSchema = yup.object().shape({
  currentPassword: yup.string().required('كلمة المرور الحالية مطلوبة'),
  newPassword: yup.string()
    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .required('كلمة المرور الجديدة مطلوبة'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('newPassword'), null], 'كلمات المرور غير متطابقة')
    .required('تأكيد كلمة المرور مطلوب')
})

const UserProfileDialogFixed = ({ open, onClose }) => {
  const { user, api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const [editingPassword, setEditingPassword] = useState(false)

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  })

  // تحديث كلمة المرور
  const passwordMutation = useMutation(
    (data) => api.put(`/api/users/${user.id}/password`, data),
    {
      onSuccess: () => {
        enqueueSnackbar('تم تحديث كلمة المرور بنجاح', { variant: 'success' })
        setEditingPassword(false)
        reset()
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.error || 'فشل في تحديث كلمة المرور',
          { variant: 'error' }
        )
      }
    }
  )

  const onSubmitPassword = (data) => {
    passwordMutation.mutate({
      currentPassword: data.currentPassword,
      newPassword: data.newPassword
    })
  }

  const handleCancelEdit = () => {
    setEditingPassword(false)
    reset()
  }

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد'
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // تحديد نوع المستخدم
  const getUserType = () => {
    if (user.permissions?.isAdmin) {
      return { label: 'مدير النظام', color: 'error', icon: <AdminIcon /> }
    }
    return { label: 'مستخدم عادي', color: 'primary', icon: <UserIcon /> }
  }

  const userType = getUserType()

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: '1.5rem',
        fontWeight: 'bold'
      }}>
        <span>👤 الملف الشخصي</span>
        <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
          <span style={{ fontSize: '20px' }}>✕</span>
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* معلومات المستخدم الأساسية */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PersonIcon /> معلومات المستخدم
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>رقم المستخدم:</strong>
                      </Typography>
                      <Chip
                        label={user.id}
                        color="primary"
                        variant="outlined"
                        size="small"
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>اسم المستخدم:</strong>
                      </Typography>
                      <Typography variant="body2">{user.username}</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>اسم الدخول:</strong>
                      </Typography>
                      <Typography variant="body2">{user.loginName}</Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>نوع الحساب:</strong>
                      </Typography>
                      <Chip
                        icon={userType.icon}
                        label={userType.label}
                        color={userType.color}
                        size="small"
                      />
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات الأمان */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <VpnKeyIcon /> كلمة المرور
                  </Box>
                  {!editingPassword && (
                    <IconButton
                      size="small"
                      onClick={() => setEditingPassword(true)}
                      sx={{ color: '#2c3e50' }}
                    >
                      <EditIcon />
                    </IconButton>
                  )}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                {!editingPassword ? (
                  <Box>
                    <Typography variant="body2" color="text.secondary">
                      انقر على أيقونة التعديل لتغيير كلمة المرور
                    </Typography>
                  </Box>
                ) : (
                  <Box component="form" onSubmit={handleSubmit(onSubmitPassword)}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Controller
                          name="currentPassword"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="كلمة المرور الحالية"
                              type="password"
                              fullWidth
                              size="small"
                              error={!!errors.currentPassword}
                              helperText={errors.currentPassword?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Controller
                          name="newPassword"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="كلمة المرور الجديدة"
                              type="password"
                              fullWidth
                              size="small"
                              error={!!errors.newPassword}
                              helperText={errors.newPassword?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Controller
                          name="confirmPassword"
                          control={control}
                          render={({ field }) => (
                            <TextField
                              {...field}
                              label="تأكيد كلمة المرور"
                              type="password"
                              fullWidth
                              size="small"
                              error={!!errors.confirmPassword}
                              helperText={errors.confirmPassword?.message}
                            />
                          )}
                        />
                      </Grid>
                      <Grid item xs={12}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Button
                            type="submit"
                            variant="contained"
                            size="small"
                            startIcon={<SaveIcon />}
                            disabled={passwordMutation.isLoading}
                          >
                            حفظ
                          </Button>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<CancelIcon />}
                            onClick={handleCancelEdit}
                          >
                            إلغاء
                          </Button>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات الأجهزة */}
          <Grid item xs={12}>
            <Card sx={{ mb: 2, background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#2c3e50', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SecurityIcon /> معلومات الأجهزة
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>الجهاز الأساسي:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
                        {user.deviceId || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>الجهاز الثانوي:</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
                        {user.device1 || 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات التواريخ */}
          <Grid item xs={12}>
            <Card sx={{ background: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ color: '#8e44ad', display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CalendarIcon /> معلومات التواريخ
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>تاريخ الإنشاء:</strong>
                      </Typography>
                      <Typography variant="body2">
                        {user.createdAt ? formatDate(user.createdAt) : 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                        <strong>آخر تحديث:</strong>
                      </Typography>
                      <Typography variant="body2">
                        {user.updatedAt ? formatDate(user.updatedAt) : 'غير محدد'}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default UserProfileDialogFixed
