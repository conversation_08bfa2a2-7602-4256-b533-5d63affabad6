import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Alert,
  Grid,
  Paper,
  Chip
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';

const TestPermissions = () => {
  const { user, hasPermission } = useAuth();

  const permissions = [
    { resource: 'clients', action: 'create', label: 'إنشاء عملاء' },
    { resource: 'clients', action: 'read', label: 'قراءة العملاء' },
    { resource: 'clients', action: 'update', label: 'تحديث العملاء' },
    { resource: 'clients', action: 'delete', label: 'حذف العملاء' },
    { resource: 'agents', action: 'create', label: 'إنشاء وكلاء' },
    { resource: 'agents', action: 'read', label: 'قراءة الوكلاء' },
    { resource: 'agents', action: 'update', label: 'تحديث الوكلاء' },
    { resource: 'agents', action: 'delete', label: 'حذف الوكلاء' },
    { resource: 'users', action: 'create', label: 'إنشاء مستخدمين' },
    { resource: 'users', action: 'read', label: 'قراءة المستخدمين' },
    { resource: 'users', action: 'update', label: 'تحديث المستخدمين' },
    { resource: 'users', action: 'delete', label: 'حذف المستخدمين' },
    { resource: 'dashboard', action: 'read', label: 'لوحة التحكم' },
    { resource: 'security', action: 'read', label: 'قراءة الأمان' },
    { resource: 'security', action: 'manage', label: 'إدارة الأمان' }
  ];

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, textAlign: 'center' }}>
        🔐 اختبار الصلاحيات
      </Typography>

      {!user && (
        <Alert severity="error" sx={{ mb: 3 }}>
          ❌ لم يتم تسجيل الدخول!
        </Alert>
      )}

      {user && (
        <>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                👤 معلومات المستخدم
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="primary">
                      {user.username}
                    </Typography>
                    <Typography variant="body2">
                      الاسم
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="secondary">
                      {user.loginName}
                    </Typography>
                    <Typography variant="body2">
                      اسم المستخدم
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="success.main">
                      {user.permissions?.isAdmin ? 'أدمن' : 'مستخدم عادي'}
                    </Typography>
                    <Typography variant="body2">
                      نوع المستخدم
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={3}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="info.main">
                      {user.id}
                    </Typography>
                    <Typography variant="body2">
                      معرف المستخدم
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                🔐 الصلاحيات الخام
              </Typography>
              
              <Box sx={{ p: 2, backgroundColor: 'grey.100', borderRadius: 1 }}>
                <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                  {JSON.stringify(user.permissions, null, 2)}
                </pre>
              </Box>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                ✅ اختبار الصلاحيات
              </Typography>
              
              <Grid container spacing={2}>
                {permissions.map((perm, index) => {
                  const hasAccess = hasPermission(perm.resource, perm.action);
                  return (
                    <Grid item xs={12} md={6} lg={4} key={index}>
                      <Paper 
                        sx={{ 
                          p: 2, 
                          display: 'flex', 
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          backgroundColor: hasAccess ? 'success.light' : 'error.light'
                        }}
                      >
                        <Typography variant="body2">
                          {perm.label}
                        </Typography>
                        <Chip 
                          label={hasAccess ? 'مسموح' : 'ممنوع'}
                          color={hasAccess ? 'success' : 'error'}
                          size="small"
                        />
                      </Paper>
                    </Grid>
                  );
                })}
              </Grid>
            </CardContent>
          </Card>

          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                🧪 اختبار دالة hasPermission
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Typography variant="body2">
                  <strong>hasPermission('clients', 'create'):</strong> {String(hasPermission('clients', 'create'))}
                </Typography>
                <Typography variant="body2">
                  <strong>hasPermission('users', 'create'):</strong> {String(hasPermission('users', 'create'))}
                </Typography>
                <Typography variant="body2">
                  <strong>hasPermission('agents', 'create'):</strong> {String(hasPermission('agents', 'create'))}
                </Typography>
                <Typography variant="body2">
                  <strong>user.permissions?.isAdmin:</strong> {String(user.permissions?.isAdmin)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </>
      )}
    </Box>
  );
};

export default TestPermissions;
