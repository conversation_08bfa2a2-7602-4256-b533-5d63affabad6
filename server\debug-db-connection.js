const { PrismaClient } = require('@prisma/client');

// إضافة endpoint لاختبار اتصال قاعدة البيانات
const express = require('express');
const app = express();

app.get('/test-db', async (req, res) => {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 [DEBUG] Testing database connection from external request...');
    console.log('🔍 [DEBUG] Client IP:', req.ip || req.connection.remoteAddress);
    console.log('🔍 [DEBUG] DATABASE_URL:', process.env.DATABASE_URL);
    
    // اختبار الاتصال
    await prisma.$connect();
    console.log('✅ [DEBUG] Database connected successfully');
    
    // عد المستخدمين
    const userCount = await prisma.user.count();
    console.log(`📊 [DEBUG] Users in database: ${userCount}`);
    
    // البحث عن admin
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { loginName: 'admin' }
        ]
      }
    });
    
    console.log('👤 [DEBUG] Admin user found:', !!adminUser);
    
    res.json({
      success: true,
      userCount,
      adminExists: !!adminUser,
      message: 'Database connection successful',
      clientIP: req.ip || req.connection.remoteAddress
    });
    
  } catch (error) {
    console.error('❌ [DEBUG] Database connection failed:', error.message);
    console.error('❌ [DEBUG] Full error:', error);
    
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Database connection failed',
      clientIP: req.ip || req.connection.remoteAddress
    });
  } finally {
    await prisma.$disconnect();
  }
});

app.listen(8081, '0.0.0.0', () => {
  console.log('🔍 Debug server running on http://0.0.0.0:8081');
  console.log('🔍 Test URL: http://***********:3000/test-db (will be proxied)');
});

module.exports = app;
