const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, checkPermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على جميع سجلات البيانات
router.get('/', authenticateToken, checkPermission('dataRecords', 'read'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', agentId, clientId, status } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { agentReference: { equals: parseInt(search) || 0 } },
        { agent: { agentName: { contains: search, mode: 'insensitive' } } },
        { client: { clientName: { contains: search, mode: 'insensitive' } } }
      ];
    }

    if (agentId) {
      where.agentId = parseInt(agentId);
    }

    if (clientId) {
      where.clientId = parseInt(clientId);
    }

    if (status !== undefined) {
      where.operationStatus = parseInt(status);
    }






    // جلب سجلات البيانات مع معلومات الوكيل والعميل
    const dataRecordsRaw = await prisma.dataRecord.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      orderBy: { operationDate: 'desc' },
      include: {
        agent: {
          select: { id: true, agentName: true, agencyName: true }
        },
        client: {
          select: { id: true, clientName: true, appName: true, clientCode: true }
        }
      }
    });

    const total =await prisma.dataRecord.count({ where });

    // تنسيق البيانات لتتضمن agentId و agentReference بوضوح
    const dataRecords = dataRecordsRaw.map(record => ({
      ...record,
      agentId: record.agentId, // رقم الوكيل
      agentReference: record.agentReference, // مرجع الوكيل
      agentName: record.agent?.agentName || 'غير محدد',
      clientName: record.client?.clientName || 'غير محدد'
    }));

    res.json({
      dataRecords,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get data records error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على سجل بيانات واحد
router.get('/:id', authenticateToken, checkPermission('dataRecords', 'read'), async (req, res) => {
  try {
    const dataRecord = await prisma.dataRecord.findUnique({
      where: { id: parseInt(req.params.id) },
      include: {
        agent: {
          select: { id: true, agentName: true, agencyName: true }
        },
        client: {
          select: { id: true, clientName: true, appName: true, clientCode: true }
        }
      }
    });

    if (!dataRecord) {
      return res.status(404).json({ error: 'Data record not found' });
    }

    res.json(dataRecord);
  } catch (error) {
    console.error('Get data record error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// إضافة سجل بيانات جديد
router.post('/', [
  authenticateToken,
  checkPermission('dataRecords', 'create'),
  body('agentId').isInt().withMessage('Agent ID is required'),
  body('clientId').isInt().withMessage('Client ID is required'),
  body('agentReference').isInt().withMessage('Agent reference is required'),
  body('operationStatus').isIn([0, 1]).withMessage('Operation status must be 0 or 1')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      agentId,
      clientId,
      agentReference,
      operationStatus = 0,
      clientCode,
      clientPassword,
      clientIpAddress
    } = req.body;

    // التحقق من وجود الوكيل والعميل
    const [agent, client] = await Promise.all([
      prisma.agent.findUnique({ where: { id: parseInt(agentId) } }),
      prisma.client.findUnique({ where: { id: parseInt(clientId) } })
    ]);

    if (!agent) {
      return res.status(400).json({ error: 'Agent not found' });
    }

    if (!client) {
      return res.status(400).json({ error: 'Client not found' });
    }

    const dataRecord = await prisma.dataRecord.create({
      data: {
        agentId: parseInt(agentId),
        clientId: parseInt(clientId),
        agentReference: parseInt(agentReference),
        operationStatus: parseInt(operationStatus),
        operationDate: new Date(),
        // حفظ بيانات العميل وقت العملية
        clientCode: clientCode || client.clientCode,
        clientPassword: clientPassword || client.password,
        clientIpAddress: clientIpAddress || client.ipAddress
      },
      include: {
        agent: {
          select: { id: true, agentName: true, agencyName: true }
        },
        client: {
          select: { id: true, clientName: true, appName: true, clientCode: true }
        }
      }
    });

    res.status(201).json(dataRecord);
  } catch (error) {
    console.error('Create data record error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// تحديث سجل بيانات
router.put('/:id', [
  authenticateToken,
  checkPermission('dataRecords', 'update'),
  body('agentId').optional().isInt().withMessage('Agent ID must be integer'),
  body('clientId').optional().isInt().withMessage('Client ID must be integer'),
  body('agentReference').optional().isInt().withMessage('Agent reference must be integer'),
  body('operationStatus').optional().isIn([0, 1]).withMessage('Operation status must be 0 or 1')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { agentId, clientId, agentReference, operationStatus } = req.body;
    const updateData = {};

    if (agentId !== undefined) updateData.agentId = parseInt(agentId);
    if (clientId !== undefined) updateData.clientId = parseInt(clientId);
    if (agentReference !== undefined) updateData.agentReference = parseInt(agentReference);
    if (operationStatus !== undefined) updateData.operationStatus = parseInt(operationStatus);

    const dataRecord = await prisma.dataRecord.update({
      where: { id: parseInt(req.params.id) },
      data: updateData,
      include: {
        agent: {
          select: { id: true, agentName: true, agencyName: true }
        },
        client: {
          select: { id: true, clientName: true, appName: true, clientCode: true }
        }
      }
    });

    res.json(dataRecord);
  } catch (error) {
    console.error('Update data record error:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Data record not found' });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

// حذف سجل بيانات
router.delete('/:id', authenticateToken, checkPermission('dataRecords', 'delete'), async (req, res) => {
  try {
    await prisma.dataRecord.delete({
      where: { id: parseInt(req.params.id) }
    });

    res.json({ message: 'Data record deleted successfully' });
  } catch (error) {
    console.error('Delete data record error:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Data record not found' });
    }
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
