/**
 * اختبار نهائي لدخول العميل
 */

async function testClientLoginFinal() {
  console.log('🏢 اختبار نهائي لدخول العميل...\n');

  let totalTests = 0;
  let passedTests = 0;

  try {
    // اختبار 1: API دخول العميل مباشرة
    console.log('1️⃣ اختبار API دخول العميل مباشرة:');
    totalTests++;
    
    const directApiResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 1001,
        password: 'Hash2020@'
      })
    });

    console.log(`   📡 Status: ${directApiResponse.status}`);
    
    if (directApiResponse.ok) {
      const directData = await directApiResponse.json();
      if (directData.success) {
        passedTests++;
        console.log('   ✅ API دخول العميل يعمل مباشرة!');
        console.log(`   🏢 العميل: ${directData.client?.clientName}`);
        console.log(`   🔢 رمز العميل: ${directData.client?.clientCode}`);
        console.log(`   🎫 التوكن: ${directData.client?.token || 'غير محدد'}`);
      } else {
        console.log('   ❌ API دخول العميل فشل في البيانات');
        console.log(`   📝 الخطأ: ${directData.message}`);
      }
    } else {
      const errorData = await directApiResponse.json();
      console.log('   ❌ API دخول العميل فشل');
      console.log(`   📝 الخطأ: ${errorData.message}`);
    }
    console.log('');

    // اختبار 2: العميل 1000
    console.log('2️⃣ اختبار دخول العميل 1000:');
    totalTests++;
    
    const client1000Response = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 1000,
        password: '112223333'
      })
    });

    console.log(`   📡 Status: ${client1000Response.status}`);
    
    if (client1000Response.ok) {
      const client1000Data = await client1000Response.json();
      if (client1000Data.success) {
        passedTests++;
        console.log('   ✅ دخول العميل 1000 نجح!');
        console.log(`   🏢 العميل: ${client1000Data.client?.clientName}`);
        console.log(`   🔢 رمز العميل: ${client1000Data.client?.clientCode}`);
      } else {
        console.log('   ❌ دخول العميل 1000 فشل في البيانات');
      }
    } else {
      console.log('   ❌ دخول العميل 1000 فشل');
    }
    console.log('');

    // اختبار 3: كلمة مرور خاطئة
    console.log('3️⃣ اختبار كلمة مرور خاطئة:');
    totalTests++;
    
    const wrongPasswordResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 1001,
        password: 'wrongpassword'
      })
    });

    console.log(`   📡 Status: ${wrongPasswordResponse.status}`);
    
    if (!wrongPasswordResponse.ok) {
      const wrongData = await wrongPasswordResponse.json();
      if (wrongData.message && wrongData.message.includes('كلمة المرور')) {
        passedTests++;
        console.log('   ✅ تم رفض كلمة المرور الخاطئة بنجاح');
        console.log(`   📝 رسالة الخطأ: ${wrongData.message}`);
      } else {
        console.log('   ❌ رسالة الخطأ غير صحيحة');
      }
    } else {
      console.log('   ❌ تم قبول كلمة مرور خاطئة (مشكلة أمنية!)');
    }
    console.log('');

    // اختبار 4: رمز عميل غير موجود
    console.log('4️⃣ اختبار رمز عميل غير موجود:');
    totalTests++;
    
    const nonExistentResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 9999,
        password: 'anypassword'
      })
    });

    console.log(`   📡 Status: ${nonExistentResponse.status}`);
    
    if (!nonExistentResponse.ok) {
      const nonExistentData = await nonExistentResponse.json();
      if (nonExistentData.message && nonExistentData.message.includes('رمز العميل')) {
        passedTests++;
        console.log('   ✅ تم رفض رمز العميل غير الموجود بنجاح');
        console.log(`   📝 رسالة الخطأ: ${nonExistentData.message}`);
      } else {
        console.log('   ❌ رسالة الخطأ غير صحيحة');
      }
    } else {
      console.log('   ❌ تم قبول رمز عميل غير موجود (مشكلة أمنية!)');
    }

    console.log('\n' + '='.repeat(70));
    console.log('📋 ملخص اختبار دخول العميل النهائي:');
    console.log(`📊 إجمالي الاختبارات: ${totalTests}`);
    console.log(`✅ الاختبارات الناجحة: ${passedTests}`);
    console.log(`❌ الاختبارات الفاشلة: ${totalTests - passedTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('');

    if (passedTests === totalTests) {
      console.log('🎉 جميع اختبارات دخول العميل نجحت!');
      console.log('✅ API دخول العميل يعمل بشكل مثالي');
      console.log('✅ التحقق من كلمة المرور يعمل');
      console.log('✅ التحقق من رمز العميل يعمل');
      console.log('✅ رسائل الخطأ واضحة ومفيدة');
      console.log('');
      console.log('🚀 النظام جاهز للاستخدام الإنتاجي!');
    } else {
      console.log('⚠️ بعض اختبارات دخول العميل فشلت');
      console.log('🔧 يرجى مراجعة الأخطاء أعلاه');
    }

    console.log('\n💡 بيانات الاختبار الصحيحة:');
    console.log('   🏢 العميل 1000: كلمة المرور 112223333');
    console.log('   🏢 العميل 1001: كلمة المرور Hash2020@');
    console.log('   🏢 العميل 1002: كلمة المرور password123');
    console.log('   🏢 العميل 1003: كلمة المرور test123');
    console.log('   🏢 العميل 1004: كلمة المرور demo123');
    console.log('   🏢 العميل 1005: كلمة المرور client123');
    console.log('');
    console.log('🌐 للاختبار من المتصفح:');
    console.log('   📍 افتح: http://localhost:8080');
    console.log('   🔄 اختر: دخول عميل');
    console.log('   📝 أدخل: رمز العميل وكلمة المرور');
    console.log('   ➡️ ستنتقل لصفحة: /client-dashboard');
    console.log('');
    console.log('🔧 إذا استمر الفشل:');
    console.log('   1. تأكد من تشغيل الخادم على المنفذ 8080');
    console.log('   2. تحقق من رسائل وحدة التحكم في المتصفح');
    console.log('   3. تأكد من أن كلمات المرور مشفرة بشكل صحيح');
    console.log('   4. تحقق من أن جدول العملاء يحتوي على البيانات الصحيحة');

  } catch (error) {
    console.error('❌ خطأ في اختبار دخول العميل النهائي:', error.message);
  }
}

testClientLoginFinal().catch(console.error);
