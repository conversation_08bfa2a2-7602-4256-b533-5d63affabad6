const http = require('http');

function simpleTest() {
  const options = {
    hostname: 'localhost',
    port: 8080,
    path: '/health',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers:`, res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log('Response:', data);
    });
  });

  req.on('error', (error) => {
    console.error('Error:', error.message);
  });

  req.setTimeout(5000, () => {
    console.log('Request timeout');
    req.destroy();
  });

  req.end();
}

console.log('Testing server connection...');
simpleTest();
