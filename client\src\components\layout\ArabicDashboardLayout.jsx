import React, { useState } from 'react'
import {
  <PERSON>,
  Drawer,
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  Container,
  Paper
} from '@mui/material'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

const drawerWidth = 280

const ArabicDashboardLayout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout, hasPermission } = useAuth()

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleLogout = () => {
    logout()
    handleClose()
  }

  const menuItems = [
    {
      text: 'لوحة التحكم',
      icon: '📊',
      path: '/dashboard',
      permission: null
    },
    {
      text: 'العملاء',
      icon: '👥',
      path: '/clients',
      permission: { resource: 'clients', action: 'read' }
    },
    {
      text: 'الوكلاء',
      icon: '🏢',
      path: '/agents',
      permission: { resource: 'agents', action: 'read' }
    },
    {
      text: 'المستخدمين',
      icon: '👤',
      path: '/users',
      permission: { resource: 'users', action: 'read' }
    },
    {
      text: 'البيانات',
      icon: '📋',
      path: '/data-records',
      permission: null
    },
    {
      text: 'الأمان',
      icon: '🔒',
      path: '/security',
      permission: { resource: 'security', action: 'read' }
    }
  ]

  const getPageTitle = () => {
    const currentItem = menuItems.find(item => item.path === location.pathname)
    return currentItem?.text || 'لوحة التحكم'
  }

  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* رأس القائمة */}
      <Box sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        p: 3,
        textAlign: 'center'
      }}>
        <Typography variant="h6" sx={{ fontWeight: 700, mb: 2 }}>
          نظام إدارة العملاء
        </Typography>
        <Avatar sx={{
          width: 64,
          height: 64,
          mx: 'auto',
          mb: 1,
          bgcolor: 'rgba(255,255,255,0.2)',
          fontSize: '24px'
        }}>
          👤
        </Avatar>
        <Typography variant="body2" sx={{ opacity: 0.9 }}>
          {user?.username || 'مستخدم'}
        </Typography>
      </Box>

      <Divider />

      {/* قائمة التنقل */}
      <List sx={{ flexGrow: 1, py: 1 }}>
        {menuItems.map((item) => {
          if (item.permission && !hasPermission(item.permission.resource, item.permission.action)) {
            return null
          }

          const isActive = location.pathname === item.path
          
          return (
            <ListItem key={item.path} disablePadding sx={{ px: 1 }}>
              <ListItemButton
                onClick={() => navigate(item.path)}
                sx={{
                  borderRadius: 2,
                  mx: 1,
                  mb: 0.5,
                  backgroundColor: isActive ? 'primary.main' : 'transparent',
                  color: isActive ? 'white' : 'text.primary',
                  '&:hover': {
                    backgroundColor: isActive ? 'primary.dark' : 'action.hover',
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <ListItemIcon sx={{ 
                  minWidth: 40,
                  color: isActive ? 'white' : 'text.secondary'
                }}>
                  <span style={{ fontSize: '20px' }}>{item.icon}</span>
                </ListItemIcon>
                <ListItemText 
                  primary={item.text}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: isActive ? 600 : 400
                    }
                  }}
                />
              </ListItemButton>
            </ListItem>
          )
        })}
      </List>
    </Box>
  )

  return (
    <Box sx={{ 
      display: 'flex',
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      direction: 'rtl'
    }}>
      {/* شريط علوي موحد */}
      <AppBar
        position="fixed"
        sx={{
          width: '100%',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          zIndex: (theme) => theme.zIndex.drawer + 1
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ ml: 2, display: { sm: 'none' } }}
          >
            <span style={{ fontSize: '24px' }}>☰</span>
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 600 }}>
            {getPageTitle()}
          </Typography>

          <Tooltip title="الإشعارات">
            <IconButton size="large" color="inherit">
              <Badge badgeContent={0} color="error">
                <span style={{ fontSize: '24px' }}>🔔</span>
              </Badge>
            </IconButton>
          </Tooltip>

          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
          >
            <span style={{ fontSize: '24px' }}>👤</span>
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>👤</span>
              </ListItemIcon>
              الملف الشخصي
            </MenuItem>
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>🚪</span>
              </ListItemIcon>
              تسجيل الخروج
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية على اليمين */}
      <Box
        component="nav"
        sx={{ 
          width: { sm: drawerWidth }, 
          flexShrink: { sm: 0 }
        }}
      >
        <Drawer
          variant="temporary"
          anchor="right"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: 'rtl'
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          anchor="right"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: 'rtl',
              position: 'fixed',
              height: '100%',
              top: 0,
              right: 0
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      {/* المحتوى الرئيسي */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: '#f8f9fa',
          direction: 'rtl'
        }}
      >
        <Toolbar />
        <Container maxWidth="xl" sx={{ py: 3 }}>
          <Paper 
            elevation={1} 
            sx={{ 
              p: 3, 
              borderRadius: 2,
              backgroundColor: 'white',
              minHeight: 'calc(100vh - 120px)'
            }}
          >
            {children}
          </Paper>
        </Container>
      </Box>
    </Box>
  )
}

export default ArabicDashboardLayout
