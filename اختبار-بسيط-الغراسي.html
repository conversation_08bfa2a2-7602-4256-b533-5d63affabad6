<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - وكيل الغراسي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        button {
            width: 100%;
            padding: 15px;
            font-size: 18px;
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
        }
        
        button:hover {
            background: #c0392b;
        }
        
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            min-height: 50px;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .info {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 اختبار وكيل الغراسي</h1>
        
        <div class="info">
            <p><strong>الوكيل:</strong> الغراسي</p>
            <p><strong>العميل المختبر:</strong> 1000</p>
            <p><strong>الخادم:</strong> ***********:8080</p>
        </div>
        
        <button onclick="testClient()">🚀 اختبار التحقق من العميل</button>
        
        <div id="result" class="info">
            اضغط على الزر أعلاه لبدء الاختبار...
        </div>
    </div>

    <script>
        async function testClient() {
            const resultDiv = document.getElementById('result');
            
            // عرض حالة التحميل
            resultDiv.className = 'loading';
            resultDiv.innerHTML = '⏳ جاري التحقق من العميل...';
            
            try {
                console.log('🚀 بدء الاختبار...');
                
                const response = await fetch('http://***********:8080/api/external/verify-direct', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_login_name: 'alghurasi',
                        agent_login_password: 'alghurasi123',
                        client_code: '1000',
                        client_token: 'ABC12345'
                    })
                });

                const result = await response.json();
                console.log('📥 النتيجة:', result);

                if (result.status === 'success') {
                    // نجح التحقق
                    resultDiv.className = 'success';
                    resultDiv.innerHTML = `
                        <h3>✅ تم التحقق بنجاح!</h3>
                        <p><strong>🤝 الوكيل:</strong> ${result.data.agent_info.agent_name}</p>
                        <p><strong>👤 العميل:</strong> ${result.data.client_info.client_name}</p>
                        <p><strong>📱 التطبيق:</strong> ${result.data.client_info.app_name}</p>
                        <p><strong>📊 الحالة:</strong> ${result.data.client_info.status_text}</p>
                        <p><strong>🌐 عنوان IP:</strong> ${result.data.client_info.ip_address}</p>
                        <p><strong>📅 تاريخ الإنشاء:</strong> ${result.data.client_info.created_date}</p>
                        <hr>
                        <p><strong>✅ التحقق:</strong></p>
                        <p>• الوكيل: ${result.data.verification_result.agent_verified ? 'مصادق ✅' : 'فشل ❌'}</p>
                        <p>• العميل: ${result.data.verification_result.client_verified ? 'موجود ✅' : 'غير موجود ❌'}</p>
                        <p>• التوكن: ${result.data.verification_result.token_verified ? 'مطابق ✅' : 'غير مطابق ❌'}</p>
                    `;
                } else {
                    // فشل التحقق
                    resultDiv.className = 'error';
                    resultDiv.innerHTML = `
                        <h3>❌ فشل التحقق</h3>
                        <p><strong>السبب:</strong> ${result.message}</p>
                        <p><strong>كود الخطأ:</strong> ${result.error_code}</p>
                        ${result.agent_info ? `<p><strong>الوكيل:</strong> ${result.agent_info.agent_name} (مصادق ✅)</p>` : ''}
                        ${result.client_info ? `<p><strong>العميل:</strong> موجود لكن التوكن غير مطابق</p>` : ''}
                    `;
                }

            } catch (error) {
                console.error('❌ خطأ:', error);
                
                // خطأ في الاتصال
                resultDiv.className = 'error';
                resultDiv.innerHTML = `
                    <h3>❌ خطأ في الاتصال</h3>
                    <p><strong>التفاصيل:</strong> ${error.message}</p>
                    <hr>
                    <p><strong>🔧 تحقق من:</strong></p>
                    <p>• الاتصال بالإنترنت</p>
                    <p>• عنوان الخادم: ***********:8080</p>
                    <p>• أن الخادم يعمل</p>
                    <p>• إعدادات جدار الحماية</p>
                `;
            }
        }

        // اختبار تلقائي عند تحميل الصفحة (اختياري)
        // window.onload = function() {
        //     setTimeout(testClient, 1000);
        // };
    </script>
</body>
</html>
