const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const compression = require('compression');
const path = require('path');
const fs = require('fs');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 8080;
const prisma = new PrismaClient();

console.log('🚀 Starting Real Database Server...');
console.log('📊 Database URL:', process.env.DATABASE_URL ? 'Configured' : 'Not configured');

// إنشاء مجلد logs
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// دالة اللوق
const writeLog = (level, message, data = {}) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...data
  };

  const logMessage = `[${timestamp}] ${level}: ${message}`;
  console.log(logMessage);

  // كتابة في ملف اللوق
  const logFile = path.join(logsDir, `real-server-${timestamp.split('T')[0]}.log`);
  fs.appendFileSync(logFile, JSON.stringify(logEntry) + '\n');
};

// اختبار الاتصال بقاعدة البيانات
async function testDatabaseConnection() {
  try {
    writeLog('INFO', 'Testing database connection...');
    await prisma.$connect();
    await prisma.$queryRaw`SELECT 1`;
    writeLog('INFO', '✅ Database connection successful');
    return true;
  } catch (error) {
    writeLog('ERROR', '❌ Database connection failed', { error: error.message });
    return false;
  }
}

// إنشاء المستخدم الإداري إذا لم يكن موجوداً
async function ensureAdminUser() {
  try {
    writeLog('INFO', 'Checking for admin user...');

    const adminUser = await prisma.user.findUnique({
      where: { loginName: 'admin' }
    });

    if (!adminUser) {
      writeLog('INFO', 'Creating admin user...');

      const hashedPassword = await bcrypt.hash('admin123456', 10);

      const newAdmin = await prisma.user.create({
        data: {
          username: 'المدير العام',
          loginName: 'admin',
          password: hashedPassword,
          deviceId: 'admin-device-001',
          device1: 'admin-device-001',
          permissions: {
            isAdmin: true,
            users: { read: true, write: true, delete: true },
            clients: { read: true, write: true, delete: true },
            agents: { read: true, write: true, delete: true },
            data: { read: true, write: true, delete: true },
            security: { read: true, write: true, delete: true }
          },
          isActive: true
        }
      });

      writeLog('INFO', '✅ Admin user created', { id: newAdmin.id, username: newAdmin.username });
    } else {
      writeLog('INFO', '✅ Admin user already exists', { id: adminUser.id, username: adminUser.username });
    }
  } catch (error) {
    writeLog('ERROR', 'Failed to ensure admin user', { error: error.message });
  }
}

// Middleware أساسي
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:8080',
    'http://**************:8080',
    'http://************:8080',
    'http://***********:8080',
    'http://***********:3000'
  ],
  credentials: true
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());
app.use(morgan('combined'));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'yemclient-super-secret-key-2024',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false,
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Middleware للوق
app.use((req, res, next) => {
  writeLog('INFO', 'Request received', {
    method: req.method,
    url: req.url,
    ip: req.ip
  });
  next();
});

// Health check مع فحص قاعدة البيانات
app.get('/health', async (req, res) => {
  try {
    writeLog('INFO', 'Health check requested');

    // فحص قاعدة البيانات
    await prisma.$queryRaw`SELECT 1`;

    // إحصائيات سريعة
    const [userCount, clientCount, agentCount] = await Promise.all([
      prisma.user.count().catch(() => 0),
      prisma.client.count().catch(() => 0),
      prisma.agent.count().catch(() => 0)
    ]);

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: '1.0.0',
      database: {
        status: 'connected',
        users: userCount,
        clients: clientCount,
        agents: agentCount
      }
    };

    writeLog('INFO', 'Health check successful', healthData);
    res.json(healthData);
  } catch (error) {
    writeLog('ERROR', 'Health check failed', { error: error.message });
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      database: 'disconnected'
    });
  }
});

// ==================== AUTH API ====================
app.post('/api/auth/login', async (req, res) => {
  try {
    const { loginName, password, deviceId } = req.body;
    writeLog('INFO', 'Login attempt', { loginName, deviceId });

    if (!loginName || !password) {
      writeLog('WARN', 'Login missing credentials', { loginName });
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم وكلمة المرور مطلوبان'
      });
    }

    // البحث عن المستخدم في قاعدة البيانات
    const user = await prisma.user.findUnique({
      where: { loginName }
    });

    if (!user) {
      writeLog('WARN', 'User not found', { loginName });

      // تسجيل محاولة دخول فاشلة
      try {
        await prisma.loginAttempt.create({
          data: {
            userType: 'user',
            deviceId: deviceId || 'unknown',
            ipAddress: req.ip || 'unknown',
            userAgent: req.get('User-Agent') || 'unknown',
            success: false
          }
        });
      } catch (logError) {
        writeLog('WARN', 'Failed to log failed attempt', { error: logError.message });
      }

      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      writeLog('WARN', 'Invalid password', { loginName, userId: user.id });

      // تسجيل محاولة دخول فاشلة
      try {
        await prisma.loginAttempt.create({
          data: {
            userType: 'user',
            userId: user.id,
            deviceId: deviceId || 'unknown',
            ipAddress: req.ip || 'unknown',
            userAgent: req.get('User-Agent') || 'unknown',
            success: false
          }
        });
      } catch (logError) {
        writeLog('WARN', 'Failed to log failed attempt', { error: logError.message });
      }

      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // التحقق من حالة المستخدم
    if (!user.isActive) {
      writeLog('WARN', 'User inactive', { loginName, userId: user.id });
      return res.status(403).json({
        success: false,
        message: 'الحساب غير مفعل'
      });
    }

    // تسجيل محاولة دخول ناجحة
    try {
      await prisma.loginAttempt.create({
        data: {
          userType: 'user',
          userId: user.id,
          deviceId: deviceId || 'unknown',
          ipAddress: req.ip || 'unknown',
          userAgent: req.get('User-Agent') || 'unknown',
          success: true
        }
      });
    } catch (logError) {
      writeLog('WARN', 'Failed to log successful attempt', { error: logError.message });
    }

    // إرجاع بيانات المستخدم
    const userData = {
      id: user.id,
      username: user.username,
      loginName: user.loginName,
      permissions: user.permissions,
      deviceId: user.deviceId,
      isActive: user.isActive
    };

    writeLog('INFO', 'Login successful', { userId: user.id, username: user.username });

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: userData
    });

  } catch (error) {
    writeLog('ERROR', 'Login error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

app.post('/api/auth/logout', (req, res) => {
  writeLog('INFO', 'Logout requested');
  res.json({
    success: true,
    message: 'تم تسجيل الخروج بنجاح'
  });
});

// ==================== USERS API ====================
app.get('/api/users', async (req, res) => {
  try {
    writeLog('INFO', 'Users list requested');

    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          device1: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          _count: {
            select: { clients: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    writeLog('INFO', 'Users list generated', { count: users.length, total });

    res.json({
      users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    writeLog('ERROR', 'Users list error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب قائمة المستخدمين' });
  }
});

// ==================== CLIENTS API ====================
app.get('/api/clients', async (req, res) => {
  try {
    writeLog('INFO', 'Clients list requested');

    const { page = 1, limit = 10, search = '', status, userId } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { clientCode: { equals: parseInt(search) || 0 } },
        { cardNumber: { contains: search } }
      ];
    }

    if (status) {
      where.status = parseInt(status);
    }

    if (userId) {
      where.userId = parseInt(userId);
    }

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          user: {
            select: {
              id: true,
              username: true,
              loginName: true
            }
          },
          _count: {
            select: { dataRecords: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count({ where })
    ]);

    writeLog('INFO', 'Clients list generated', { count: clients.length, total });

    res.json({
      clients,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    writeLog('ERROR', 'Clients list error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب قائمة العملاء' });
  }
});

// ==================== AGENTS API ====================
app.get('/api/agents', async (req, res) => {
  try {
    writeLog('INFO', 'Agents list requested');

    const { page = 1, limit = 10, search = '', agencyType } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (agencyType) {
      where.agencyType = agencyType;
    }

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          _count: {
            select: { dataRecords: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    writeLog('INFO', 'Agents list generated', { count: agents.length, total });

    res.json({
      agents,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    writeLog('ERROR', 'Agents list error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب قائمة الوكلاء' });
  }
});

// ==================== DATA RECORDS API ====================
app.get('/api/data-records', async (req, res) => {
  try {
    writeLog('INFO', 'Data records requested');

    const { page = 1, limit = 10, search = '', status } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {};

    if (search) {
      where.OR = [
        { clientCode: { contains: search, mode: 'insensitive' } },
        { agentReference: { equals: parseInt(search) || 0 } }
      ];
    }

    if (status) {
      where.operationStatus = parseInt(status);
    }

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        where,
        skip,
        take: parseInt(limit),
        include: {
          agent: {
            select: {
              id: true,
              agentName: true,
              agencyName: true,
              agencyType: true
            }
          },
          client: {
            select: {
              id: true,
              clientName: true,
              clientCode: true,
              appName: true
            }
          }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count({ where })
    ]);

    writeLog('INFO', 'Data records generated', { count: dataRecords.length, total });

    res.json({
      dataRecords,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    writeLog('ERROR', 'Data records error', { error: error.message });
    res.status(500).json({ error: 'فشل في جلب سجلات البيانات' });
  }
});

// ==================== SECURITY API ====================
app.get('/api/security/stats', async (req, res) => {
  try {
    writeLog('INFO', 'Security stats requested');

    const [totalUsers, activeUsers, totalClients, activeClients, totalAgents, activeAgents, totalAttempts, successfulAttempts] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } }),
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } })
    ]);

    const stats = {
      totalAttempts,
      successfulLogins: successfulAttempts,
      failedLogins: totalAttempts - successfulAttempts,
      suspiciousActivity: 0,
      blockedIPs: 0,
      activeDevices: activeUsers,
      uniqueIPs: activeUsers + activeAgents,
      lastSecurityScan: new Date().toISOString(),
      last24Hours: {
        successfulLogins: successfulAttempts,
        failedAttempts: Math.floor((totalAttempts - successfulAttempts) * 0.1)
      },
      systemHealth: {
        database: 'connected',
        api: 'operational',
        security: 'active'
      },
      summary: {
        totalUsers,
        activeUsers,
        totalClients,
        activeClients,
        totalAgents,
        activeAgents
      }
    };

    writeLog('INFO', 'Security stats generated', stats);
    res.json(stats);
  } catch (error) {
    writeLog('ERROR', 'Security stats error', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch security stats' });
  }
});

app.get('/api/security/login-attempts', async (req, res) => {
  try {
    writeLog('INFO', 'Login attempts requested');

    const { page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        skip,
        take: parseInt(limit),
        include: {
          user: { select: { username: true, loginName: true } },
          agent: { select: { agentName: true } }
        },
        orderBy: { timestamp: 'desc' }
      }),
      prisma.loginAttempt.count()
    ]);

    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id,
      type: attempt.success ? 'success' : 'failed',
      username: attempt.user?.username || attempt.agent?.agentName || 'Unknown',
      loginName: attempt.user?.loginName || 'N/A',
      ip: attempt.ipAddress,
      userAgent: attempt.userAgent || 'Browser',
      timestamp: attempt.timestamp.toISOString(),
      deviceId: attempt.deviceId,
      reason: attempt.success ? null : 'Invalid credentials'
    }));

    writeLog('INFO', 'Login attempts response generated', { count: formattedAttempts.length });

    res.json({
      attempts: formattedAttempts,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / parseInt(limit))
    });
  } catch (error) {
    writeLog('ERROR', 'Login attempts error', { error: error.message });
    res.status(500).json({ error: 'Failed to fetch login attempts' });
  }
});

// ==================== EXTERNAL API ====================
app.get('/api/external/health', async (req, res) => {
  try {
    writeLog('INFO', 'External API health check requested');

    await prisma.$queryRaw`SELECT 1`;

    res.json({
      status: 'success',
      message: 'External API is healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        version: '1.0.0',
        uptime: process.uptime()
      }
    });
  } catch (error) {
    writeLog('ERROR', 'External API health error', { error: error.message });
    res.status(500).json({
      status: 'error',
      message: 'External API health check failed',
      error_code: 'HEALTH_CHECK_FAILED'
    });
  }
});

app.get('/api/external/stats', async (req, res) => {
  try {
    writeLog('INFO', 'External API stats requested');

    const [totalClients, activeClients, totalAgents, activeAgents, totalUsers, activeUsers, totalOperations] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } }),
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.dataRecord.count()
    ]);

    const response = {
      success: true,
      message: 'System statistics retrieved successfully',
      data: {
        clients: {
          total: totalClients,
          active: activeClients,
          inactive: totalClients - activeClients
        },
        agents: {
          total: totalAgents,
          active: activeAgents,
          inactive: totalAgents - activeAgents
        },
        users: {
          total: totalUsers,
          active: activeUsers,
          inactive: totalUsers - activeUsers
        },
        operations: {
          totalRecords: totalOperations
        },
        timestamp: new Date().toISOString()
      }
    };

    writeLog('INFO', 'External API stats generated', response.data);
    res.json(response);
  } catch (error) {
    writeLog('ERROR', 'External API stats error', { error: error.message });
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve system statistics',
      error_code: 'STATS_ERROR'
    });
  }
});

// ==================== CREATE APIS ====================
app.post('/api/clients', async (req, res) => {
  try {
    const { clientName, appName, cardNumber, password, ipAddress, userId } = req.body;
    writeLog('INFO', 'Create client requested', { clientName, cardNumber });

    // التحقق من البيانات المطلوبة
    if (!clientName || !appName || !cardNumber || !password || !userId) {
      return res.status(400).json({
        error: 'جميع البيانات مطلوبة'
      });
    }

    // التحقق من عدم تكرار رقم البطاقة
    const existingClient = await prisma.client.findFirst({
      where: { cardNumber }
    });

    if (existingClient) {
      return res.status(409).json({
        error: 'رقم البطاقة موجود مسبقاً'
      });
    }

    // إنشاء رقم عميل تلقائي
    const lastClient = await prisma.client.findFirst({
      orderBy: { clientCode: 'desc' }
    });
    const clientCode = lastClient ? lastClient.clientCode + 1 : 1000;

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 10);

    // إنشاء العميل
    const newClient = await prisma.client.create({
      data: {
        clientName,
        appName,
        cardNumber,
        clientCode,
        password: hashedPassword,
        ipAddress: ipAddress || req.ip,
        userId: parseInt(userId),
        status: 1
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            loginName: true
          }
        }
      }
    });

    writeLog('INFO', 'Client created successfully', {
      clientId: newClient.id,
      clientCode: newClient.clientCode,
      clientName
    });

    res.status(201).json({
      success: true,
      message: 'تم إنشاء العميل بنجاح',
      client: newClient
    });

  } catch (error) {
    writeLog('ERROR', 'Create client error', { error: error.message });
    res.status(500).json({ error: 'فشل في إنشاء العميل' });
  }
});

// Static files for React app
const clientDistPath = path.join(__dirname, '../client/dist');
console.log('Client dist path:', clientDistPath);

if (fs.existsSync(clientDistPath)) {
  console.log('✅ Client dist folder found');
  app.use(express.static(clientDistPath));

  // React app fallback
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }

    writeLog('INFO', 'Serving React app', { path: req.path });
    res.sendFile(path.join(clientDistPath, 'index.html'));
  });
} else {
  console.log('❌ Client dist folder not found');
  app.get('*', (req, res) => {
    if (req.path.startsWith('/api/')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.send('<h1>React app not built. Please run: npm run build</h1>');
  });
}

// Error handling
app.use((error, req, res, next) => {
  writeLog('ERROR', 'Server error', { error: error.message });
  res.status(500).json({
    error: 'Internal server error',
    message: error.message
  });
});

// بدء الخادم
async function startServer() {
  try {
    // اختبار الاتصال بقاعدة البيانات
    const dbConnected = await testDatabaseConnection();

    if (dbConnected) {
      // إنشاء المستخدم الإداري
      await ensureAdminUser();
    }

    // بدء الخادم
    app.listen(PORT, '0.0.0.0', () => {
      writeLog('INFO', `🚀 Real Database Server started on port ${PORT}`);
      writeLog('INFO', `🌐 Local: http://localhost:${PORT}`);
      writeLog('INFO', `🌍 External: http://***********:${PORT}`);
      writeLog('INFO', `📊 Database: ${dbConnected ? 'Connected' : 'Disconnected'}`);
      writeLog('INFO', `✅ Server is ready`);

      console.log(`🚀 Real Database Server running on port ${PORT}`);
      console.log(`🌐 Local: http://localhost:${PORT}`);
      console.log(`🌍 External: http://***********:${PORT}`);
      console.log(`📊 Database: ${dbConnected ? '✅ Connected' : '❌ Disconnected'}`);
      console.log(`✅ Server is ready`);
    });

  } catch (error) {
    writeLog('ERROR', 'Failed to start server', { error: error.message });
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  writeLog('INFO', 'SIGTERM received, shutting down gracefully');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  writeLog('INFO', 'SIGINT received, shutting down gracefully');
  await prisma.$disconnect();
  process.exit(0);
});

// بدء الخادم
startServer();

module.exports = app;
