const { PrismaClient } = require('@prisma/client');

async function listUsers() {
  const prisma = new PrismaClient();
  
  try {
    console.log('📋 قائمة جميع المستخدمين:');
    console.log('========================');
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true,
        deviceId: true,
        createdAt: true,
        updatedAt: true
      }
    });
    
    users.forEach((user, index) => {
      console.log(`👤 مستخدم ${index + 1}:`);
      console.log(`   - ID: ${user.id}`);
      console.log(`   - اسم المستخدم: ${user.username || 'غير محدد'}`);
      console.log(`   - اسم تسجيل الدخول: ${user.loginName || 'غير محدد'}`);
      console.log(`   - البريد الإلكتروني: ${user.email || 'غير محدد'}`);
      console.log(`   - نشط: ${user.isActive ? 'نعم' : 'لا'}`);
      console.log(`   - الصلاحية: ${user.role || 'غير محدد'}`);
      console.log(`   - تاريخ الإنشاء: ${user.createdAt}`);
      console.log('   ---');
    });
    
  } catch (error) {
    console.error('❌ خطأ في قراءة المستخدمين:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

listUsers();
