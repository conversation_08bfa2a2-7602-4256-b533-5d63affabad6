# Yemen Client Management System - External API Documentation

## 🌐 **Base URL**
```
http://***********:8080/api/external/
```

## 🔐 **1. Agent Authentication**

### **Endpoint:**
```
POST /api/external/agent/auth
```

### **Description:**
Authenticates an agent using their login credentials and returns a session token for subsequent API calls.

### **Request Parameters:**
```json
{
  "login_name": "string (required) - Agent's login username",
  "login_password": "string (required) - Agent's login password"
}
```

### **Response Examples:**

#### **✅ Success Response:**
```json
{
  "status": "success",
  "message": "Agent authenticated successfully",
  "data": {
    "agent_id": 123,
    "agent_name": "Agent Name",
    "agency_type": "Yemen Mobile Agent",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_at": "2025-07-01T23:06:00Z"
  }
}
```

#### **❌ Error Response:**
```json
{
  "status": "error",
  "message": "Invalid login credentials",
  "error_code": "AUTH_FAILED"
}
```

---

## 👤 **2. Client Verification**

### **Endpoint:**
```
POST /api/external/client/verify
```

### **Description:**
Verifies a client's credentials using their client code and token. Requires valid agent authentication.

### **Request Headers:**
```
Authorization: Bearer {agent_token}
Content-Type: application/json
```

### **Request Parameters:**
```json
{
  "client_code": "string (required) - Client's unique code",
  "token": "string (required) - Client's 8+ character token"
}
```

### **Response Examples:**

#### **✅ Active Client:**
```json
{
  "status": "success",
  "message": "Client verified successfully",
  "data": {
    "client_code": "CLIENT001",
    "client_name": "Client Name",
    "app_name": "Client Application",
    "status": 1,
    "ip_address": "*************",
    "created_date": "2025-06-30T10:00:00Z"
  }
}
```

#### **✅ Inactive Client:**
```json
{
  "status": "success",
  "message": "Client found but inactive",
  "data": {
    "client_code": "CLIENT001",
    "status": 0
  }
}
```

#### **❌ Client Not Found:**
```json
{
  "status": "error",
  "message": "Client not found",
  "error_code": "CLIENT_NOT_FOUND"
}
```

#### **❌ Invalid Token:**
```json
{
  "status": "error",
  "message": "Invalid client token",
  "error_code": "TOKEN_MISMATCH"
}
```

---

## 📊 **3. Additional Agent Services**

### **A. Get Agent's Clients List:**
```
GET /api/external/agent/clients
Authorization: Bearer {agent_token}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "clients": [
      {
        "client_code": "CLIENT001",
        "client_name": "Client Name",
        "status": 1,
        "last_activity": "2025-06-30T15:30:00Z"
      }
    ],
    "total": 25
  }
}
```

### **B. Get Agent Statistics:**
```
GET /api/external/agent/stats
Authorization: Bearer {agent_token}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "total_clients": 25,
    "active_clients": 20,
    "total_operations": 150,
    "successful_operations": 145,
    "failed_operations": 5,
    "success_rate": "96.7%"
  }
}
```

### **C. Get Operations Log:**
```
GET /api/external/agent/operations?page=1&limit=10
Authorization: Bearer {agent_token}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "operations": [
      {
        "id": 1,
        "client_code": "CLIENT001",
        "operation_type": "verify",
        "status": 1,
        "timestamp": "2025-06-30T15:30:00Z",
        "ip_address": "*************"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 150,
      "total_pages": 15
    }
  }
}
```

---

## 🔒 **4. Security & Rate Limiting**

### **Authentication:**
- All requests require a valid agent token
- Tokens expire after 24 hours
- Failed authentication attempts are logged

### **Rate Limits:**
- 1000 requests per hour per agent
- 100 requests per minute per agent
- Rate limit headers included in responses:
  ```
  X-RateLimit-Limit: 1000
  X-RateLimit-Remaining: 999
  X-RateLimit-Reset: 1625097600
  ```

### **Security Headers:**
```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

---

## 📝 **5. Error Codes Reference**

| Code | Description | Solution |
|------|-------------|----------|
| AUTH_FAILED | Agent authentication failed | Check username and password |
| CLIENT_NOT_FOUND | Client does not exist | Verify client code |
| TOKEN_MISMATCH | Client token is invalid | Check client token |
| INVALID_AGENT_TOKEN | Agent session expired | Re-authenticate |
| RATE_LIMIT_EXCEEDED | Too many requests | Wait before sending new requests |
| SERVER_ERROR | Internal server error | Try again later |
| VALIDATION_ERROR | Invalid request parameters | Check request format |

---

## 🧪 **6. Code Examples**

### **JavaScript/Node.js:**
```javascript
// Agent Authentication
const authResponse = await fetch('http://***********:8080/api/external/agent/auth', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    login_name: 'agent001',
    login_password: 'password123'
  })
});

const authData = await authResponse.json();
const agentToken = authData.data.token;

// Client Verification
const verifyResponse = await fetch('http://***********:8080/api/external/client/verify', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${agentToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    client_code: 'CLIENT001',
    token: 'ABC12345'
  })
});

const verifyData = await verifyResponse.json();
console.log(verifyData);
```

### **Python:**
```python
import requests

# Agent Authentication
auth_response = requests.post(
    'http://***********:8080/api/external/agent/auth',
    json={
        'login_name': 'agent001',
        'login_password': 'password123'
    }
)

auth_data = auth_response.json()
agent_token = auth_data['data']['token']

# Client Verification
verify_response = requests.post(
    'http://***********:8080/api/external/client/verify',
    headers={
        'Authorization': f'Bearer {agent_token}',
        'Content-Type': 'application/json'
    },
    json={
        'client_code': 'CLIENT001',
        'token': 'ABC12345'
    }
)

verify_data = verify_response.json()
print(verify_data)
```

### **cURL:**
```bash
# Agent Authentication
curl -X POST http://***********:8080/api/external/agent/auth \
  -H "Content-Type: application/json" \
  -d '{
    "login_name": "agent001",
    "login_password": "password123"
  }'

# Client Verification
curl -X POST http://***********:8080/api/external/client/verify \
  -H "Authorization: Bearer YOUR_AGENT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "client_code": "CLIENT001",
    "token": "ABC12345"
  }'
```

---

## 📋 **7. Response Status Codes**

| HTTP Code | Description |
|-----------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Access denied |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

---

## 🔄 **8. Webhook Support**

### **Operation Notifications:**
Configure webhooks to receive real-time notifications:

```
POST /api/external/agent/webhooks
Authorization: Bearer {agent_token}

{
  "url": "https://your-server.com/webhook",
  "events": ["client_verified", "operation_completed"]
}
```

---

## 📞 **9. Support**

For technical support or to report issues:
- Email: <EMAIL>
- Phone: +967-xxx-xxxx
- Documentation: http://***********:8080/api/docs
- Status Page: http://***********:8080/api/status
