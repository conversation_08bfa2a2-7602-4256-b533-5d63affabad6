@echo off
chcp 65001 >nul
title إنشاء قاعدة البيانات يدوياً

echo ========================================
echo      إنشاء قاعدة البيانات يدوياً
echo ========================================

echo.
echo PostgreSQL يعمل، لكن أدوات سطر الأوامر غير متاحة.
echo سنقوم بإنشاء قاعدة البيانات باستخدام pgAdmin.

echo.
echo الخطوات المطلوبة:
echo.
echo 1. افتح pgAdmin 4
echo 2. اتصل بالخادم المحلي:
echo    - Host: localhost
echo    - Port: 5432
echo    - Username: postgres
echo    - Password: yemen123
echo.
echo 3. انقر بزر الماوس الأيمن على "Databases"
echo 4. اختر "Create" → "Database"
echo 5. اكتب اسم قاعدة البيانات: yemclient_db
echo 6. انقر "Save"
echo.
echo 7. انقر بزر الماوس الأيمن على قاعدة البيانات الجديدة
echo 8. اختر "Query Tool"
echo 9. انسخ محتوى الملف create_database.sql والصقه
echo 10. انقر "Execute" أو اضغط F5
echo.

echo ========================================
echo        أو استخدم Docker (أسهل)
echo ========================================
echo.
echo إذا كنت تفضل الطريقة الأسهل:
echo 1. تأكد من تثبيت Docker Desktop
echo 2. شغّل: docker-compose up -d
echo 3. انتظر 2-3 دقائق
echo 4. اذهب إلى: http://localhost:5050
echo 5. سجل دخول بـ:
echo    Email: <EMAIL>
echo    Password: admin123456
echo.

echo ========================================
echo           ملف SQL جاهز
echo ========================================
echo.
echo تم إنشاء ملف create_database.sql
echo يحتوي على جميع الأوامر المطلوبة لإنشاء:
echo - قاعدة البيانات yemclient_db
echo - جميع الجداول (users, agents, clients, login_attempts)
echo - البيانات التجريبية
echo - مستخدم admin بكلمة مرور: admin123456
echo.

echo اختر الطريقة المناسبة لك:
echo 1. pgAdmin يدوياً (الخطوات أعلاه)
echo 2. Docker (تلقائي)
echo 3. خروج
echo.

set /p choice="اختر رقم (1-3): "

if "%choice%"=="1" (
    echo.
    echo فتح ملف SQL...
    start notepad create_database.sql
    echo.
    echo تم فتح الملف في Notepad
    echo انسخ المحتوى والصقه في pgAdmin Query Tool
    echo.
    pause
) else if "%choice%"=="2" (
    echo.
    echo تشغيل Docker...
    docker-compose up -d
    if %errorlevel% equ 0 (
        echo.
        echo ✓ تم تشغيل Docker بنجاح!
        echo انتظار تشغيل الخدمات...
        timeout /t 30 /nobreak >nul
        echo.
        echo 📊 معلومات الوصول:
        echo pgAdmin: http://localhost:5050
        echo لوحة التحكم: http://localhost:5173
        echo.
        echo 🔑 بيانات دخول pgAdmin:
        echo Email: <EMAIL>
        echo Password: admin123456
        echo.
        echo 📋 معلومات قاعدة البيانات:
        echo Host: postgres
        echo Port: 5432
        echo Database: yemclient_db
        echo Username: yemclient_user
        echo Password: yemclient_password
        echo.
    ) else (
        echo ✗ فشل في تشغيل Docker
        echo تأكد من تثبيت Docker Desktop وتشغيله
    )
    pause
) else (
    echo خروج...
)

echo.
echo ========================================
echo     بعد إنشاء قاعدة البيانات
echo ========================================
echo.
echo لتشغيل النظام:
echo 1. cd server
echo 2. npm install
echo 3. npm start
echo.
echo في terminal آخر:
echo 1. cd client  
echo 2. npm install
echo 3. npm run dev
echo.
echo ثم اذهب إلى: http://localhost:5173
echo بيانات الدخول: admin / admin123456
echo.
