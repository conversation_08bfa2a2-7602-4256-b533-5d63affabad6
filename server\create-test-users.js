const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUsers() {
  try {
    console.log('🔧 Creating test users...');

    // Create test user with simple password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    const testUser = await prisma.user.upsert({
      where: { loginName: 'testadmin' },
      update: {
        password: hashedPassword
      },
      create: {
        username: 'Test Admin',
        loginName: 'testadmin',
        password: hashedPassword,
        isActive: true,
        permissions: JSON.stringify({
          users: { read: true, write: true },
          clients: { read: true, write: true },
          dashboard: { read: true }
        })
      }
    });

    console.log('✅ Test user created:', {
      id: testUser.id,
      username: testUser.username,
      loginName: testUser.loginName,
      password: 'admin123'
    });

    // Create test client with simple password
    const testClient = await prisma.client.upsert({
      where: { clientCode: 9999 },
      update: {
        password: '123456'
      },
      create: {
        clientName: 'عميل تجريبي للاختبار',
        clientCode: 9999,
        password: '123456',
        status: 1,
        appName: 'YemClient Test',
        cardNumber: 'TEST9999',
        ipAddress: '127.0.0.1'
      }
    });

    console.log('✅ Test client created:', {
      id: testClient.id,
      clientName: testClient.clientName,
      clientCode: testClient.clientCode,
      password: '123456'
    });

    // Update existing admin user password
    const adminUser = await prisma.user.update({
      where: { loginName: 'admin' },
      data: {
        password: await bcrypt.hash('admin123', 10)
      }
    });

    console.log('✅ Admin user password updated:', {
      id: adminUser.id,
      username: adminUser.username,
      loginName: adminUser.loginName,
      password: 'admin123'
    });

    // Update existing client 1001 password
    const client1001 = await prisma.client.update({
      where: { clientCode: 1001 },
      data: {
        password: '123456'
      }
    });

    console.log('✅ Client 1001 password updated:', {
      id: client1001.id,
      clientName: client1001.clientName,
      clientCode: client1001.clientCode,
      password: '123456'
    });

    console.log('\n🎉 Test users created successfully!');
    console.log('\n📋 Test Credentials:');
    console.log('👤 User Login:');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('   OR');
    console.log('   Username: testadmin');
    console.log('   Password: admin123');
    console.log('\n🏢 Client Login:');
    console.log('   Client Code: 1001');
    console.log('   Password: 123456');
    console.log('   OR');
    console.log('   Client Code: 9999');
    console.log('   Password: 123456');

  } catch (error) {
    console.error('❌ Error creating test users:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUsers();
