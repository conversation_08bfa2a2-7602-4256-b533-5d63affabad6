const { PrismaClient } = require('@prisma/client');

async function testDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Testing database connection...');
    
    // اختبار الاتصال
    await prisma.$connect();
    console.log('✅ Database connected successfully');
    
    // عد المستخدمين
    const userCount = await prisma.user.count();
    console.log(`📊 Users in database: ${userCount}`);
    
    // اختبار العثور على مستخدم admin
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { loginName: 'admin' }
        ]
      }
    });
    
    if (adminUser) {
      console.log('✅ Admin user found:', adminUser.username || adminUser.loginName);
      console.log('   - ID:', adminUser.id);
      console.log('   - Active:', adminUser.isActive);
      console.log('   - Created:', adminUser.createdAt);
    } else {
      console.log('❌ Admin user not found');
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('   Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testDatabase();
