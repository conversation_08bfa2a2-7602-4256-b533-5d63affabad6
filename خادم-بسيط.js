const express = require('express')
const cors = require('cors')
const path = require('path')

const app = express()
const PORT = 8080

console.log('🚀 بدء تشغيل الخادم البسيط...')

// Middleware
app.use(cors({ origin: '*', credentials: true }))
app.use(express.json())
app.use(express.urlencoded({ extended: true }))

// Logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`)
  next()
})

// Test route
app.get('/test', (req, res) => {
  res.json({ 
    message: 'الخادم يعمل بنجاح!',
    timestamp: new Date().toISOString(),
    status: 'success'
  })
})

// Mock login route for testing
app.post('/api/auth/login', (req, res) => {
  console.log('📝 طلب تسجيل دخول:', req.body)
  
  const { loginName, password, deviceId } = req.body
  
  // Mock user data with isActive
  const mockUser = {
    id: 1,
    username: 'محمد الحاشدي',
    loginName: 'hash8080',
    isActive: true,  // ← هذا هو المهم
    permissions: {
      isAdmin: true
    }
  }
  
  if (loginName === 'hash8080' && password === 'hash8080') {
    console.log('✅ تسجيل دخول ناجح')
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: mockUser,
      token: `token_${Date.now()}`
    })
  } else {
    console.log('❌ فشل في تسجيل الدخول')
    res.status(401).json({
      success: false,
      error: 'بيانات الدخول غير صحيحة'
    })
  }
})

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// Serve static files
app.use(express.static(path.join(__dirname, 'client/dist')))

// Catch all handler
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API not found' })
  }
  res.sendFile(path.join(__dirname, 'client/dist/index.html'))
})

// Error handling
app.use((error, req, res, next) => {
  console.error('❌ خطأ في الخادم:', error)
  res.status(500).json({ 
    error: 'خطأ داخلي في الخادم',
    message: error.message 
  })
})

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ الخادم البسيط يعمل على http://0.0.0.0:${PORT}`)
  console.log(`🌐 خارجي: http://***********:${PORT}`)
  console.log(`🏠 محلي: http://localhost:${PORT}`)
  console.log(`🧪 اختبار: http://localhost:${PORT}/test`)
})

server.on('error', (error) => {
  console.error('❌ خطأ في بدء الخادم:', error)
  if (error.code === 'EADDRINUSE') {
    console.log(`⚠️ المنفذ ${PORT} مستخدم بالفعل`)
  }
})

process.on('SIGINT', () => {
  console.log('\n🛑 إيقاف الخادم...')
  server.close(() => {
    console.log('✅ تم إيقاف الخادم بنجاح')
    process.exit(0)
  })
})

console.log('🎯 الخادم البسيط جاهز')
