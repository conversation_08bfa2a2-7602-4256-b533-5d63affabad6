/**
 * اختبار النظام النهائي المحدث
 */

async function testFinalUpdatedSystem() {
  console.log('🎨 اختبار النظام النهائي المحدث...\n');

  let totalTests = 0;
  let passedTests = 0;

  try {
    // اختبار 1: صفحة الدخول الموحدة
    console.log('1️⃣ اختبار صفحة الدخول الموحدة:');
    totalTests++;
    
    const unifiedResponse = await fetch('http://localhost:8080/');
    console.log(`   📡 Status: ${unifiedResponse.status}`);
    
    if (unifiedResponse.ok) {
      const content = await unifiedResponse.text();
      
      const hasNewTitle = content.includes('نظام ادارة تطبيقات الدفع الالكتروني');
      const hasUserLogin = content.includes('دخول مستخدم');
      const hasClientLogin = content.includes('دخول عميل');
      const hasInteractiveDesign = content.includes('particles') && content.includes('login-type-selector');
      
      if (hasNewTitle && hasUserLogin && hasClientLogin && hasInteractiveDesign) {
        passedTests++;
        console.log('   ✅ صفحة الدخول الموحدة مكتملة!');
        console.log('   📋 العنوان الجديد: موجود');
        console.log('   👤 خيار دخول المستخدم: موجود');
        console.log('   🏢 خيار دخول العميل: موجود');
        console.log('   🎨 التصميم التفاعلي: مطبق');
      } else {
        console.log('   ❌ صفحة الدخول الموحدة غير مكتملة');
        console.log(`   📋 العنوان الجديد: ${hasNewTitle ? '✅' : '❌'}`);
        console.log(`   👤 خيار دخول المستخدم: ${hasUserLogin ? '✅' : '❌'}`);
        console.log(`   🏢 خيار دخول العميل: ${hasClientLogin ? '✅' : '❌'}`);
        console.log(`   🎨 التصميم التفاعلي: ${hasInteractiveDesign ? '✅' : '❌'}`);
      }
    } else {
      console.log('   ❌ فشل في تحميل صفحة الدخول الموحدة');
    }
    console.log('');

    // اختبار 2: تسجيل دخول مستخدم من الصفحة الموحدة
    console.log('2️⃣ اختبار تسجيل دخول مستخدم:');
    totalTests++;
    
    const userLoginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'hash8080',
        password: 'hash8080',
        deviceId: 'unified_test_device_12345'
      })
    });

    console.log(`   📡 Status: ${userLoginResponse.status}`);
    
    if (userLoginResponse.ok) {
      const userData = await userLoginResponse.json();
      if (userData.success) {
        passedTests++;
        console.log('   ✅ تسجيل دخول المستخدم نجح!');
        console.log(`   👤 المستخدم: ${userData.user?.username}`);
        console.log(`   🔑 Token: ${userData.token?.substring(0, 20)}...`);
      } else {
        console.log('   ❌ تسجيل دخول المستخدم فشل في البيانات');
      }
    } else {
      console.log('   ❌ تسجيل دخول المستخدم فشل في الطلب');
    }
    console.log('');

    // اختبار 3: تسجيل دخول عميل من الصفحة الموحدة
    console.log('3️⃣ اختبار تسجيل دخول عميل:');
    totalTests++;
    
    const clientLoginResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientCode: 1001,
        password: 'Hash2020@'
      })
    });

    console.log(`   📡 Status: ${clientLoginResponse.status}`);
    
    if (clientLoginResponse.ok) {
      const clientData = await clientLoginResponse.json();
      if (clientData.success) {
        passedTests++;
        console.log('   ✅ تسجيل دخول العميل نجح!');
        console.log(`   🏢 العميل: ${clientData.client?.clientName}`);
        console.log(`   🔢 رمز العميل: ${clientData.client?.clientCode}`);
        
        // اختبار تحديث بيانات العميل مع النوافذ المنبثقة
        console.log('\n   🔄 اختبار تحديث بيانات العميل:');
        const updateResponse = await fetch('http://localhost:8080/api/client/update', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            clientId: clientData.client.id,
            password: 'updatedPassword2024',
            token: 'updatedToken2024'
          })
        });
        
        console.log(`   📡 Update Status: ${updateResponse.status}`);
        
        if (updateResponse.ok) {
          const updateData = await updateResponse.json();
          console.log('   ✅ تحديث بيانات العميل نجح!');
          console.log('   💬 سيظهر: "تم التحديث بنجاح" في النافذة المنبثقة');
          console.log(`   🎫 رمز التوكن الجديد: ${updateData.client?.token}`);
        } else {
          console.log('   ❌ تحديث بيانات العميل فشل!');
          console.log('   💬 سيظهر: "رمز التوكن مخالف لشروط قاعدة البيانات" في النافذة المنبثقة');
        }
        
      } else {
        console.log('   ❌ تسجيل دخول العميل فشل في البيانات');
      }
    } else {
      console.log('   ❌ تسجيل دخول العميل فشل في الطلب');
    }
    console.log('');

    // اختبار 4: صفحة لوحة تحكم العميل المحدثة
    console.log('4️⃣ اختبار صفحة لوحة تحكم العميل:');
    totalTests++;
    
    const dashboardResponse = await fetch('http://localhost:8080/client-dashboard.html');
    console.log(`   📡 Status: ${dashboardResponse.status}`);
    
    if (dashboardResponse.ok) {
      const dashboardContent = await dashboardResponse.text();
      
      const hasNewTitle = dashboardContent.includes('نظام ادارة تطبيقات الدفع الالكتروني');
      const hasTokenLabel = dashboardContent.includes('رمز التوكن الجديد');
      const hasModal = dashboardContent.includes('modal-overlay') && dashboardContent.includes('modal-content');
      const hasProfessionalDesign = dashboardContent.includes('backgroundMove') && dashboardContent.includes('containerFloat');
      
      if (hasNewTitle && hasTokenLabel && hasModal && hasProfessionalDesign) {
        passedTests++;
        console.log('   ✅ صفحة لوحة تحكم العميل محدثة!');
        console.log('   📋 العنوان الجديد: موجود');
        console.log('   🏷️ "رمز التوكن الجديد": موجود');
        console.log('   🔔 النوافذ المنبثقة: مطبقة');
        console.log('   🎨 التصميم الاحترافي: مطبق');
      } else {
        console.log('   ❌ صفحة لوحة تحكم العميل غير مكتملة');
        console.log(`   📋 العنوان الجديد: ${hasNewTitle ? '✅' : '❌'}`);
        console.log(`   🏷️ "رمز التوكن الجديد": ${hasTokenLabel ? '✅' : '❌'}`);
        console.log(`   🔔 النوافذ المنبثقة: ${hasModal ? '✅' : '❌'}`);
        console.log(`   🎨 التصميم الاحترافي: ${hasProfessionalDesign ? '✅' : '❌'}`);
      }
    } else {
      console.log('   ❌ فشل في تحميل صفحة لوحة تحكم العميل');
    }
    console.log('');

    // اختبار 5: التحقق من حذف الملفات القديمة
    console.log('5️⃣ اختبار حذف الملفات القديمة:');
    totalTests++;
    
    const oldClientLoginResponse = await fetch('http://localhost:8080/client-login.html');
    console.log(`   📡 Old Client Login Status: ${oldClientLoginResponse.status}`);
    
    if (oldClientLoginResponse.status === 404) {
      passedTests++;
      console.log('   ✅ ملف دخول العملاء القديم تم حذفه!');
      console.log('   🗑️ النظام ينتقل للدخول الموحد فقط');
    } else {
      console.log('   ❌ ملف دخول العملاء القديم لا يزال موجوداً');
    }

    console.log('\n' + '='.repeat(70));
    console.log('📋 ملخص اختبار النظام النهائي المحدث:');
    console.log(`📊 إجمالي الاختبارات: ${totalTests}`);
    console.log(`✅ الاختبارات الناجحة: ${passedTests}`);
    console.log(`❌ الاختبارات الفاشلة: ${totalTests - passedTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('');

    if (passedTests === totalTests) {
      console.log('🎉 جميع المتطلبات تم تنفيذها بنجاح!');
      console.log('✅ صفحة دخول موحدة بتصميم خرافي');
      console.log('✅ خيارين للدخول (مستخدم وعميل)');
      console.log('✅ أيقونات ذكية تفاعلية');
      console.log('✅ العنوان الجديد: "نظام ادارة تطبيقات الدفع الالكتروني"');
      console.log('✅ صفحة بيانات عميل احترافية');
      console.log('✅ نوافذ منبثقة للرسائل');
      console.log('✅ "رمز التوكن الجديد" بدلاً من "الرمز الجديد"');
      console.log('✅ حذف الملفات القديمة');
      console.log('');
      console.log('🚀 النظام جاهز للاستخدام الإنتاجي!');
    } else {
      console.log('⚠️ بعض المتطلبات لم تكتمل بعد');
    }

    console.log('\n🌐 الروابط النهائية:');
    console.log('   📍 الدخول الموحد: http://localhost:8080');
    console.log('   📍 لوحة تحكم العميل: http://localhost:8080/client-dashboard.html');
    console.log('');
    console.log('💡 بيانات الاختبار:');
    console.log('   👤 مستخدم: hash8080 / hash8080');
    console.log('   🏢 عميل: 1001 / Hash2020@');
    console.log('   🏢 عميل: 1000 / 112223333');

  } catch (error) {
    console.error('❌ خطأ في اختبار النظام النهائي:', error.message);
  }
}

testFinalUpdatedSystem().catch(console.error);
