<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار الخادم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 30px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 28px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        
        .result {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s;
            width: 100%;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .server-info {
            background: #d1ecf1;
            border: 2px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار الخادم</h1>
            <p>فحص حالة الخادم واختبار تسجيل الدخول</p>
        </div>

        <!-- معلومات الخادم -->
        <div class="server-info">
            <h3>📡 معلومات الخادم</h3>
            <p><strong>العنوان المحلي:</strong> http://localhost:8080</p>
            <p><strong>العنوان الخارجي:</strong> http://***********:8080</p>
            <p><strong>API تسجيل الدخول:</strong> /api/auth/login</p>
        </div>

        <!-- اختبار الاتصال -->
        <div class="test-section">
            <h3>🔗 اختبار الاتصال بالخادم</h3>
            <button onclick="testConnection()">🔍 فحص الاتصال</button>
            <div id="connectionStatus" class="status warning">
                ⏳ لم يتم الاختبار بعد
            </div>
        </div>

        <!-- اختبار تسجيل الدخول -->
        <div class="test-section">
            <h3>🔐 اختبار تسجيل الدخول</h3>
            <button onclick="testLogin()">🧪 اختبار تسجيل الدخول</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار isActive -->
        <div class="test-section">
            <h3>✅ اختبار حقل isActive</h3>
            <button onclick="testIsActive()">🔍 فحص isActive</button>
            <div id="isActiveResult" class="result" style="display: none;"></div>
        </div>

        <!-- حالة النظام -->
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div id="systemStatus" class="status warning">
                ⏳ جاري فحص النظام...
            </div>
        </div>
    </div>

    <script>
        // اختبار الاتصال
        async function testConnection() {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.className = 'status warning';
            statusDiv.textContent = '⏳ جاري فحص الاتصال...';
            
            try {
                // محاولة الوصول للخادم
                const response = await fetch('http://localhost:8080/health', {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ الخادم يعمل بنجاح!';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = `❌ خطأ في الخادم: ${response.status}`;
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ فشل في الاتصال: ${error.message}`;
                
                // محاولة العنوان الخارجي
                try {
                    const response2 = await fetch('http://***********:8080/health');
                    if (response2.ok) {
                        statusDiv.className = 'status success';
                        statusDiv.textContent = '✅ الخادم الخارجي يعمل!';
                    }
                } catch (error2) {
                    console.log('فشل في الاتصال بالخادم الخارجي أيضاً');
                }
            }
        }
        
        // اختبار تسجيل الدخول
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '⏳ جاري اختبار تسجيل الدخول...';
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        loginName: 'hash8080',
                        password: 'hash8080',
                        deviceId: 'koadbqwog_1751136029819'
                    })
                });
                
                const result = await response.json();
                
                let output = '🧪 نتائج اختبار تسجيل الدخول:\n';
                output += '=' .repeat(50) + '\n\n';
                output += `📊 حالة الاستجابة: ${response.ok ? 'نجح ✅' : 'فشل ❌'}\n`;
                output += `🔢 كود الاستجابة: ${response.status}\n\n`;
                
                if (response.ok && result.user) {
                    output += '👤 بيانات المستخدم:\n';
                    output += `   الرقم: ${result.user.id}\n`;
                    output += `   الاسم: ${result.user.username}\n`;
                    output += `   اسم الدخول: ${result.user.loginName}\n`;
                    output += `   isActive موجود: ${result.user.isActive !== undefined ? 'نعم ✅' : 'لا ❌'}\n`;
                    output += `   قيمة isActive: ${result.user.isActive}\n`;
                    output += `   الحالة: ${result.user.isActive ? 'نشط ✅' : 'غير نشط ❌'}\n`;
                    output += `   مدير النظام: ${result.user.permissions?.isAdmin ? 'نعم ✅' : 'لا ❌'}\n\n`;
                    output += `🔑 التوكن: ${result.token ? 'موجود ✅' : 'غير موجود ❌'}\n`;
                } else {
                    output += '❌ فشل في تسجيل الدخول:\n';
                    output += `   الخطأ: ${result.error || 'غير محدد'}\n`;
                }
                
                resultDiv.textContent = output;
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }
        
        // اختبار isActive
        async function testIsActive() {
            const resultDiv = document.getElementById('isActiveResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '⏳ جاري فحص isActive...';
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        loginName: 'hash8080',
                        password: 'hash8080',
                        deviceId: 'koadbqwog_1751136029819'
                    })
                });
                
                const result = await response.json();
                
                let output = '🔍 تحليل حقل isActive:\n';
                output += '=' .repeat(40) + '\n\n';
                
                if (response.ok && result.user) {
                    if (result.user.isActive !== undefined) {
                        output += '✅ حقل isActive موجود!\n';
                        output += `📊 القيمة: ${result.user.isActive}\n`;
                        output += `🎯 النتيجة: ${result.user.isActive ? 'المستخدم نشط' : 'المستخدم غير نشط'}\n\n`;
                        output += '🎉 تم حل المشكلة!\n';
                        output += 'الآن سيظهر المستخدم بالحالة الصحيحة في الملف الشخصي.';
                    } else {
                        output += '❌ حقل isActive غير موجود!\n';
                        output += 'هذا يعني أن الخادم لم يتم تحديثه بعد.\n\n';
                        output += '🔧 الحل:\n';
                        output += '1. تأكد من تشغيل الخادم المحدث\n';
                        output += '2. أعد تشغيل الخادم\n';
                        output += '3. امسح localStorage وسجل دخول جديد';
                    }
                } else {
                    output += '❌ فشل في تسجيل الدخول\n';
                    output += `الخطأ: ${result.error || 'غير محدد'}`;
                }
                
                resultDiv.textContent = output;
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }
        
        // فحص حالة النظام عند تحميل الصفحة
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
