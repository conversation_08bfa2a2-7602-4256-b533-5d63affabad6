@echo off
chcp 65001 >nul
title إعداد نظام إدارة العملاء والوكلاء

echo ========================================
echo    إعداد نظام إدارة العملاء والوكلاء
echo ========================================

echo.
echo 1. التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do echo ✓ Node.js موجود: %%i
)

echo.
echo 2. التحقق من PostgreSQL...
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ PostgreSQL غير مثبت أو غير موجود في PATH
    echo سيتم التحقق من Docker...
    
    echo.
    echo 3. التحقق من Docker...
    docker --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo ✗ Docker غير مثبت!
        echo يرجى تثبيت PostgreSQL أو Docker للمتابعة
        pause
        exit /b 1
    ) else (
        for /f "tokens=*" %%i in ('docker --version') do echo ✓ Docker موجود: %%i
        goto docker_setup
    )
) else (
    for /f "tokens=*" %%i in ('psql --version') do echo ✓ PostgreSQL موجود: %%i
    goto postgres_setup
)

:postgres_setup
echo.
echo === استخدام PostgreSQL المحلي ===

echo.
echo 4. إنشاء قاعدة البيانات...
set PGPASSWORD=yemen123
createdb -U postgres yemclient_db >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ تم إنشاء قاعدة البيانات yemclient_db
) else (
    echo ⚠ قاعدة البيانات موجودة مسبقاً أو حدث خطأ
)

echo.
echo 5. تثبيت متطلبات الخادم...
cd server
call npm install
if %errorlevel% neq 0 (
    echo ✗ فشل في تثبيت متطلبات الخادم
    pause
    exit /b 1
)
echo ✓ تم تثبيت متطلبات الخادم

echo.
echo 6. إنشاء الجداول...
call npx prisma migrate dev --name init
if %errorlevel% equ 0 (
    echo ✓ تم إنشاء الجداول
) else (
    echo ⚠ قد تكون الجداول موجودة مسبقاً
)

echo.
echo 7. إدخال البيانات التجريبية...
call npm run db:seed
if %errorlevel% equ 0 (
    echo ✓ تم إدخال البيانات التجريبية
) else (
    echo ⚠ قد تكون البيانات موجودة مسبقاً
)

echo.
echo 8. تثبيت متطلبات العميل...
cd ..\client
call npm install
if %errorlevel% neq 0 (
    echo ✗ فشل في تثبيت متطلبات العميل
    pause
    exit /b 1
)
echo ✓ تم تثبيت متطلبات العميل

cd ..
goto show_info

:docker_setup
echo.
echo === استخدام Docker ===

echo.
echo 4. إيقاف الحاويات السابقة...
docker-compose down >nul 2>&1

echo.
echo 5. تشغيل النظام بـ Docker...
docker-compose up -d
if %errorlevel% neq 0 (
    echo ✗ فشل في تشغيل Docker
    pause
    exit /b 1
)
echo ✓ تم تشغيل النظام بـ Docker

echo انتظار تشغيل الخدمات...
timeout /t 30 /nobreak >nul

goto show_info

:show_info
echo.
echo ========================================
echo       تم إعداد النظام بنجاح!
echo ========================================

echo.
echo 📊 معلومات الوصول:
echo لوحة التحكم: http://localhost:5173
echo API الخادم: http://***********:3000

if defined DOCKER_SETUP (
    echo pgAdmin: http://localhost:5050
    echo.
    echo 🔑 بيانات دخول pgAdmin:
    echo Email: <EMAIL>
    echo Password: admin123456
    echo.
    echo 📋 معلومات قاعدة البيانات في pgAdmin:
    echo Host: postgres
    echo Port: 5432
    echo Database: yemclient_db
    echo Username: yemclient_user
    echo Password: yemclient_password
) else (
    echo.
    echo 📋 معلومات قاعدة البيانات في pgAdmin:
    echo Host: localhost
    echo Port: 5432
    echo Database: yemclient_db
    echo Username: postgres
    echo Password: yemen123
)

echo.
echo 🔑 بيانات دخول النظام:
echo Username: admin
echo Password: admin123456

echo.
echo 🚀 لتشغيل النظام:
if defined DOCKER_SETUP (
    echo النظام يعمل بالفعل مع Docker
) else (
    echo npm run dev
)

echo.
echo 📚 ملفات مفيدة:
echo README.md - دليل شامل
echo API_DOCUMENTATION.md - توثيق API
echo START_HERE.md - دليل البداية السريعة

echo.
echo ✨ النظام جاهز للاستخدام!
echo.
pause
