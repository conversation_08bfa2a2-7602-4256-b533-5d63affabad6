@echo off
chcp 65001 >nul
title تشغيل نظام إدارة العملاء والوكلاء

color 0A
echo ========================================
echo    تشغيل نظام إدارة العملاء والوكلاء
echo ========================================

echo.
echo 🔍 البحث عن Node.js...

:: البحث في المواقع الشائعة لـ Node.js
set NODE_PATH=
set NPM_PATH=

if exist "C:\Program Files\nodejs\node.exe" (
    set NODE_PATH="C:\Program Files\nodejs\node.exe"
    set NPM_PATH="C:\Program Files\nodejs\npm.cmd"
    echo ✅ تم العثور على Node.js في: C:\Program Files\nodejs\
) else if exist "C:\Program Files (x86)\nodejs\node.exe" (
    set NODE_PATH="C:\Program Files (x86)\nodejs\node.exe"
    set NPM_PATH="C:\Program Files (x86)\nodejs\npm.cmd"
    echo ✅ تم العثور على Node.js في: C:\Program Files (x86)\nodejs\
) else (
    :: محاولة استخدام PATH العادي
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        set NODE_PATH=node
        set NPM_PATH=npm
        echo ✅ Node.js متاح في PATH
    ) else (
        echo ❌ لم يتم العثور على Node.js!
        echo.
        echo 📋 الحلول:
        echo 1. أعد تشغيل Command Prompt
        echo 2. أعد تشغيل الكمبيوتر
        echo 3. أعد تثبيت Node.js من: https://nodejs.org
        echo.
        pause
        exit /b 1
    )
)

:: التحقق من إصدار Node.js
echo.
echo 📊 معلومات Node.js:
%NODE_PATH% --version
%NPM_PATH% --version

echo.
echo 🚀 بدء تشغيل النظام...

:: التحقق من وجود مجلدات المشروع
if not exist "server" (
    echo ❌ مجلد server غير موجود!
    echo تأكد من تشغيل هذا الملف في مجلد المشروع الصحيح
    pause
    exit /b 1
)

if not exist "client" (
    echo ❌ مجلد client غير موجود!
    echo تأكد من تشغيل هذا الملف في مجلد المشروع الصحيح
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت متطلبات الخادم...
cd server
%NPM_PATH% install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت متطلبات الخادم
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت متطلبات العميل...
cd ..\client
%NPM_PATH% install
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت متطلبات العميل
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!

echo.
echo 🚀 تشغيل الخادم...
cd ..\server
start "YemClient Server" cmd /k "%NPM_PATH% start"

echo ⏳ انتظار تشغيل الخادم...
timeout /t 8 /nobreak >nul

echo.
echo 🚀 تشغيل العميل...
cd ..\client
start "YemClient Frontend" cmd /k "%NPM_PATH% run dev"

echo.
echo ========================================
echo         النظام يعمل الآن! 🎉
echo ========================================

echo.
echo 🌐 الوصول للنظام:
echo ├─ لوحة التحكم: http://localhost:5173
echo ├─ API الخادم الخارجي: http://***********:3000
echo └─ API الوكلاء: http://***********:3000/api/external/

echo.
echo 🔑 بيانات الدخول:
echo ├─ Username: admin
echo └─ Password: admin123456

echo.
echo 📊 قاعدة البيانات:
echo ├─ Host: localhost
echo ├─ Port: 5432
echo ├─ Database: yemclient_db
echo ├─ Username: postgres
echo └─ Password: yemen123

echo.
echo 🎯 اختبار API الوكلاء:
echo curl -X POST http://***********:3000/api/external/verify-client \
echo   -H "Content-Type: application/json" \
echo   -d "{\"clientCode\": 1000, \"agentId\": 1}"

echo.
echo ✨ النظام جاهز للاستخدام!
echo انتظر قليلاً حتى يكتمل تحميل الصفحات...

echo.
pause
