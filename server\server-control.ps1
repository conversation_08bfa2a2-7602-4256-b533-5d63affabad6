# 🚀 YemClient Server Control - فحص وإيقاف الخوادم
# يعمل من أي مسار - لا يحتاج مجلد محدد

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("check", "stop", "kill", "status", "help")]
    [string]$Action = "help"
)

# الألوان
$Green = "Green"
$Red = "Red"
$Yellow = "Yellow"
$Blue = "Blue"
$Cyan = "Cyan"
$Magenta = "Magenta"

Write-Host "🚀 YemClient Server Control" -ForegroundColor $Cyan
Write-Host "============================" -ForegroundColor $Cyan
Write-Host "📍 يعمل من أي مسار" -ForegroundColor $Blue

function Show-Help {
    Write-Host ""
    Write-Host "📋 الأوامر المتاحة:" -ForegroundColor $Yellow
    Write-Host ""
    Write-Host "  check  - فحص الخوادم التي تعمل" -ForegroundColor $Green
    Write-Host "  stop   - إيقاف جميع الخوادم بلطف" -ForegroundColor $Yellow
    Write-Host "  kill   - إيقاف جميع الخوادم بالقوة" -ForegroundColor $Red
    Write-Host "  status - حالة مفصلة للنظام" -ForegroundColor $Magenta
    Write-Host "  help   - عرض هذه المساعدة" -ForegroundColor $Cyan
    Write-Host ""
    Write-Host "🔧 الاستخدام:" -ForegroundColor $Yellow
    Write-Host "  .\server-control.ps1 check" -ForegroundColor $Blue
    Write-Host "  .\server-control.ps1 stop" -ForegroundColor $Blue
    Write-Host "  .\server-control.ps1 kill" -ForegroundColor $Blue
    Write-Host ""
    Write-Host "💡 ملاحظات:" -ForegroundColor $Yellow
    Write-Host "  - يعمل من أي مسار" -ForegroundColor $Blue
    Write-Host "  - يفحص Node.js و PM2" -ForegroundColor $Blue
    Write-Host "  - يوقف جميع الخوادم" -ForegroundColor $Blue
}

function Check-NodeProcesses {
    Write-Host ""
    Write-Host "🔍 فحص عمليات Node.js..." -ForegroundColor $Yellow
    
    try {
        $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
        
        if ($nodeProcesses) {
            Write-Host "✅ عدد عمليات Node.js: $($nodeProcesses.Count)" -ForegroundColor $Green
            Write-Host ""
            $nodeProcesses | Format-Table @{
                Name="PID"; Expression={$_.Id}; Width=8
            }, @{
                Name="اسم العملية"; Expression={$_.ProcessName}; Width=15
            }, @{
                Name="الذاكرة (MB)"; Expression={[math]::Round($_.WorkingSet/1MB,1)}; Width=12
            }, @{
                Name="وقت البدء"; Expression={$_.StartTime.ToString("HH:mm:ss")}; Width=10
            } -AutoSize
            
            return $nodeProcesses.Count
        } else {
            Write-Host "❌ لا توجد عمليات Node.js تعمل" -ForegroundColor $Red
            return 0
        }
    } catch {
        Write-Host "❌ خطأ في فحص Node.js: $($_.Exception.Message)" -ForegroundColor $Red
        return 0
    }
}

function Check-PM2 {
    Write-Host ""
    Write-Host "🔍 فحص PM2..." -ForegroundColor $Yellow
    
    try {
        # التحقق من وجود PM2
        $pm2Command = Get-Command pm2 -ErrorAction SilentlyContinue
        
        if (-not $pm2Command) {
            Write-Host "❌ PM2 غير مثبت" -ForegroundColor $Red
            return 0
        }
        
        # تشغيل pm2 list
        $pm2Output = & pm2 jlist 2>$null
        
        if ($LASTEXITCODE -eq 0 -and $pm2Output) {
            $pm2Data = $pm2Output | ConvertFrom-Json
            
            if ($pm2Data.Count -gt 0) {
                Write-Host "✅ عدد تطبيقات PM2: $($pm2Data.Count)" -ForegroundColor $Green
                Write-Host ""
                
                foreach ($app in $pm2Data) {
                    $status = if ($app.pm2_env.status -eq "online") { "🟢 يعمل" } else { "🔴 متوقف" }
                    $memory = [math]::Round($app.pm2_env.memory / 1MB, 1)
                    
                    Write-Host "  📱 $($app.name): $status (الذاكرة: ${memory}MB)" -ForegroundColor $Blue
                }
                
                return $pm2Data.Count
            } else {
                Write-Host "❌ لا توجد تطبيقات PM2 تعمل" -ForegroundColor $Red
                return 0
            }
        } else {
            Write-Host "❌ PM2 daemon غير يعمل" -ForegroundColor $Red
            return 0
        }
    } catch {
        Write-Host "❌ خطأ في فحص PM2: $($_.Exception.Message)" -ForegroundColor $Red
        return 0
    }
}

function Check-Ports {
    Write-Host ""
    Write-Host "🔍 فحص المنافذ..." -ForegroundColor $Yellow
    
    $ports = @(8080, 3000, 5173, 5000)
    $activePorts = 0
    
    foreach ($port in $ports) {
        try {
            $connection = Get-NetTCPConnection -LocalPort $port -State Listen -ErrorAction SilentlyContinue
            
            if ($connection) {
                Write-Host "  ✅ المنفذ $port: مستخدم" -ForegroundColor $Green
                $activePorts++
            } else {
                Write-Host "  ❌ المنفذ $port: فارغ" -ForegroundColor $Red
            }
        } catch {
            Write-Host "  ❓ المنفذ $port: غير معروف" -ForegroundColor $Yellow
        }
    }
    
    return $activePorts
}

function Test-ServerHealth {
    Write-Host ""
    Write-Host "🔍 اختبار صحة الخوادم..." -ForegroundColor $Yellow
    
    $urls = @(
        @{url="http://localhost:8080/health"; name="الخادم الرئيسي"},
        @{url="http://localhost:8080"; name="الواجهة الأمامية"},
        @{url="http://localhost:3000"; name="خادم التطوير"}
    )
    
    $workingServers = 0
    
    foreach ($server in $urls) {
        try {
            $response = Invoke-WebRequest -Uri $server.url -TimeoutSec 3 -ErrorAction Stop
            Write-Host "  ✅ $($server.name): يعمل (Status: $($response.StatusCode))" -ForegroundColor $Green
            $workingServers++
        } catch {
            Write-Host "  ❌ $($server.name): لا يعمل" -ForegroundColor $Red
        }
    }
    
    return $workingServers
}

function Stop-AllServers {
    param([bool]$ForceKill = $false)
    
    $action = if ($ForceKill) { "إيقاف بالقوة" } else { "إيقاف بلطف" }
    Write-Host ""
    Write-Host "⛔ $action لجميع الخوادم..." -ForegroundColor $Red
    
    $stoppedCount = 0
    
    # إيقاف PM2 أولاً
    Write-Host ""
    Write-Host "🔄 إيقاف PM2..." -ForegroundColor $Yellow
    try {
        $pm2Command = Get-Command pm2 -ErrorAction SilentlyContinue
        
        if ($pm2Command) {
            & pm2 stop all 2>$null | Out-Null
            Start-Sleep -Seconds 1
            & pm2 delete all 2>$null | Out-Null
            Start-Sleep -Seconds 1
            & pm2 kill 2>$null | Out-Null
            Write-Host "  ✅ تم إيقاف PM2" -ForegroundColor $Green
            $stoppedCount++
        } else {
            Write-Host "  ℹ️ PM2 غير مثبت" -ForegroundColor $Blue
        }
    } catch {
        Write-Host "  ⚠️ خطأ في إيقاف PM2: $($_.Exception.Message)" -ForegroundColor $Yellow
    }
    
    # إيقاف عمليات Node.js
    Write-Host ""
    Write-Host "🔄 إيقاف عمليات Node.js..." -ForegroundColor $Yellow
    try {
        $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
        
        if ($nodeProcesses) {
            if ($ForceKill) {
                $nodeProcesses | Stop-Process -Force
                Write-Host "  ✅ تم إيقاف $($nodeProcesses.Count) عملية بالقوة" -ForegroundColor $Green
            } else {
                $nodeProcesses | Stop-Process
                Write-Host "  ✅ تم إيقاف $($nodeProcesses.Count) عملية بلطف" -ForegroundColor $Green
            }
            $stoppedCount += $nodeProcesses.Count
        } else {
            Write-Host "  ℹ️ لا توجد عمليات Node.js للإيقاف" -ForegroundColor $Blue
        }
    } catch {
        Write-Host "  ❌ خطأ في إيقاف Node.js: $($_.Exception.Message)" -ForegroundColor $Red
    }
    
    # انتظار وفحص نهائي
    Write-Host ""
    Write-Host "⏳ انتظار 3 ثوانٍ..." -ForegroundColor $Yellow
    Start-Sleep -Seconds 3
    
    Write-Host ""
    Write-Host "🔍 فحص نهائي..." -ForegroundColor $Yellow
    
    $remainingProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($remainingProcesses) {
        Write-Host "  ⚠️ لا تزال $($remainingProcesses.Count) عملية تعمل" -ForegroundColor $Yellow
        
        if (-not $ForceKill) {
            Write-Host "  💡 جرب: .\server-control.ps1 kill" -ForegroundColor $Blue
        }
    } else {
        Write-Host "  ✅ تم إيقاف جميع الخوادم بنجاح" -ForegroundColor $Green
    }
    
    return $stoppedCount
}

function Show-SystemStatus {
    Write-Host ""
    Write-Host "📊 حالة النظام الشاملة" -ForegroundColor $Magenta
    Write-Host "======================" -ForegroundColor $Magenta
    
    $nodeCount = Check-NodeProcesses
    $pm2Count = Check-PM2
    $portCount = Check-Ports
    $serverCount = Test-ServerHealth
    
    Write-Host ""
    Write-Host "📈 ملخص الحالة:" -ForegroundColor $Magenta
    Write-Host "  🟢 عمليات Node.js: $nodeCount" -ForegroundColor $Blue
    Write-Host "  🟢 تطبيقات PM2: $pm2Count" -ForegroundColor $Blue
    Write-Host "  🟢 منافذ نشطة: $portCount" -ForegroundColor $Blue
    Write-Host "  🟢 خوادم تعمل: $serverCount" -ForegroundColor $Blue
    
    $totalActive = $nodeCount + $pm2Count + $portCount + $serverCount
    
    if ($totalActive -gt 0) {
        Write-Host ""
        Write-Host "🎯 النتيجة: النظام يعمل ($totalActive مكون نشط)" -ForegroundColor $Green
    } else {
        Write-Host ""
        Write-Host "🎯 النتيجة: النظام متوقف (لا توجد خوادم تعمل)" -ForegroundColor $Red
    }
    
    Write-Host ""
    Write-Host "💻 معلومات النظام:" -ForegroundColor $Magenta
    Write-Host "  المستخدم: $env:USERNAME" -ForegroundColor $Blue
    Write-Host "  المسار: $(Get-Location)" -ForegroundColor $Blue
    Write-Host "  الوقت: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor $Blue
}

# تنفيذ الأمر
switch ($Action.ToLower()) {
    "check" {
        $nodeCount = Check-NodeProcesses
        $pm2Count = Check-PM2
        $portCount = Check-Ports
        
        Write-Host ""
        Write-Host "📊 النتيجة:" -ForegroundColor $Cyan
        Write-Host "  Node.js: $nodeCount | PM2: $pm2Count | منافذ: $portCount" -ForegroundColor $Blue
    }
    
    "stop" {
        $stopped = Stop-AllServers -ForceKill $false
        Write-Host ""
        Write-Host "📊 تم إيقاف $stopped مكون" -ForegroundColor $Cyan
    }
    
    "kill" {
        $stopped = Stop-AllServers -ForceKill $true
        Write-Host ""
        Write-Host "📊 تم إيقاف $stopped مكون بالقوة" -ForegroundColor $Cyan
    }
    
    "status" {
        Show-SystemStatus
    }
    
    "help" {
        Show-Help
    }
    
    default {
        Show-Help
    }
}

Write-Host ""
Write-Host "✅ انتهى التنفيذ" -ForegroundColor $Green
