@echo off
chcp 65001 >nul
title فحص متطلبات النظام

color 0E
echo ========================================
echo      فحص متطلبات تشغيل النظام
echo ========================================

echo.
echo 🔍 التحقق من المتطلبات...

echo.
echo 1️⃣ فحص Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('node --version') do echo ✅ Node.js متوفر: %%i
    set NODE_OK=1
) else (
    echo ❌ Node.js غير مثبت!
    set NODE_OK=0
)

echo.
echo 2️⃣ فحص npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('npm --version') do echo ✅ npm متوفر: %%i
    set NPM_OK=1
) else (
    echo ❌ npm غير متوفر!
    set NPM_OK=0
)

echo.
echo 3️⃣ فحص Docker...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=*" %%i in ('docker --version') do echo ✅ Docker متوفر: %%i
    set DOCKER_OK=1
) else (
    echo ❌ Docker غير مثبت!
    set DOCKER_OK=0
)

echo.
echo 4️⃣ فحص PostgreSQL...
net start | findstr -i postgres >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL يعمل
    set POSTGRES_OK=1
) else (
    echo ⚠️ PostgreSQL قد لا يعمل
    set POSTGRES_OK=0
)

echo.
echo ========================================
echo           نتائج الفحص
echo ========================================

if "%NODE_OK%"=="1" if "%NPM_OK%"=="1" (
    echo.
    echo 🎉 يمكنك تشغيل النظام محلياً!
    echo.
    echo 📋 الخطوات:
    echo 1. افتح Command Prompt
    echo 2. cd server
    echo 3. npm install
    echo 4. npm start
    echo 5. افتح Command Prompt آخر
    echo 6. cd client
    echo 7. npm install
    echo 8. npm run dev
    echo 9. اذهب إلى: http://localhost:5173
    echo.
    echo هل تريد تشغيل النظام الآن؟
    echo 1. نعم - تشغيل تلقائي
    echo 2. لا - خروج
    echo.
    set /p choice="اختر (1 أو 2): "
    
    if "!choice!"=="1" (
        echo.
        echo 🚀 تشغيل النظام...
        echo تثبيت متطلبات الخادم...
        cd server
        call npm install
        if %errorlevel% neq 0 (
            echo ❌ فشل في تثبيت متطلبات الخادم
            pause
            exit /b 1
        )
        
        echo تثبيت متطلبات العميل...
        cd ..\client
        call npm install
        if %errorlevel% neq 0 (
            echo ❌ فشل في تثبيت متطلبات العميل
            pause
            exit /b 1
        )
        
        echo.
        echo ✅ تم تثبيت المتطلبات بنجاح!
        echo.
        echo تشغيل الخادم...
        cd ..\server
        start "YemClient Server" cmd /k "npm start"
        
        timeout /t 5 /nobreak >nul
        
        echo تشغيل العميل...
        cd ..\client
        start "YemClient Frontend" cmd /k "npm run dev"
        
        echo.
        echo 🎉 تم تشغيل النظام!
        echo.
        echo 🌐 الوصول:
        echo لوحة التحكم: http://localhost:5173
        echo API: http://localhost:3000
        echo.
        echo 🔑 بيانات الدخول:
        echo Username: admin
        echo Password: admin123456
        echo.
    )
) else if "%DOCKER_OK%"=="1" (
    echo.
    echo 🐳 يمكنك استخدام Docker!
    echo.
    echo 📋 الخطوات:
    echo 1. تأكد من تشغيل Docker Desktop
    echo 2. docker-compose up -d
    echo 3. انتظر 2-3 دقائق
    echo 4. اذهب إلى: http://localhost:5173
    echo.
    echo هل تريد تشغيل Docker الآن؟
    echo 1. نعم
    echo 2. لا
    echo.
    set /p choice="اختر (1 أو 2): "
    
    if "!choice!"=="1" (
        echo.
        echo 🚀 تشغيل Docker...
        docker-compose up -d
        if %errorlevel% equ 0 (
            echo.
            echo ✅ تم تشغيل النظام بنجاح!
            echo انتظار تشغيل الخدمات...
            timeout /t 30 /nobreak >nul
            echo.
            echo 🌐 الوصول:
            echo لوحة التحكم: http://localhost:5173
            echo pgAdmin: http://localhost:5050
            echo.
            echo 🔑 بيانات دخول النظام:
            echo Username: admin
            echo Password: admin123456
            echo.
            echo 🔑 بيانات دخول pgAdmin:
            echo Email: <EMAIL>
            echo Password: admin123456
            echo.
        ) else (
            echo ❌ فشل في تشغيل Docker
            echo تأكد من تشغيل Docker Desktop
        )
    )
) else (
    echo.
    echo ❌ لا يمكن تشغيل النظام!
    echo.
    echo 📥 يجب تثبيت أحد هذه:
    echo.
    echo 🟦 Node.js (الموصى به):
    echo   1. اذهب إلى: https://nodejs.org
    echo   2. حمّل النسخة LTS
    echo   3. ثبّت البرنامج
    echo   4. أعد تشغيل Command Prompt
    echo   5. شغّل هذا الملف مرة أخرى
    echo.
    echo 🐳 أو Docker:
    echo   1. اذهب إلى: https://www.docker.com/products/docker-desktop
    echo   2. حمّل وثبّت Docker Desktop
    echo   3. شغّل Docker Desktop
    echo   4. شغّل هذا الملف مرة أخرى
    echo.
)

echo.
pause
