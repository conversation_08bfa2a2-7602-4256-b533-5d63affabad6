<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وكيل الغراسي - التحقق المباشر (طلب واحد)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .highlight {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
            font-weight: bold;
            font-size: 18px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input, button, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 0;
            transition: all 0.3s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border: 2px solid #dee2e6;
            min-height: 50px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #007bff;
            font-weight: bold;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background: #e9ecef;
            border: none;
            cursor: pointer;
            margin-left: 5px;
            border-radius: 5px 5px 0 0;
        }
        
        .tab.active {
            background: #007bff;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 وكيل الغراسي - التحقق المباشر</h1>
        
        <div class="highlight">
            ✨ الآن يمكنك التحقق من العميل بطلب واحد فقط! ✨
        </div>

        <!-- اختيار نوع الخادم -->
        <div class="section">
            <h3>🌐 إعدادات الخادم</h3>
            <div class="form-group">
                <label>نوع الاتصال:</label>
                <select id="serverType" onchange="updateServerUrl()">
                    <option value="local">محلي (localhost:8080)</option>
                    <option value="external">خارجي (***********:8080)</option>
                </select>
            </div>
            <div class="form-group">
                <label>عنوان الخادم:</label>
                <input type="text" id="serverUrl" value="http://localhost:8080" readonly>
            </div>
        </div>

        <!-- بيانات الطلب -->
        <div class="section">
            <h3>📝 بيانات الطلب الموحد</h3>
            <div class="form-group">
                <label>اسم تسجيل دخول الوكيل:</label>
                <input type="text" id="agentLogin" value="agent001">
            </div>
            <div class="form-group">
                <label>كلمة مرور الوكيل:</label>
                <input type="password" id="agentPassword" value="agent123">
            </div>
            <div class="form-group">
                <label>رمز العميل:</label>
                <input type="text" id="clientCode" value="1000">
            </div>
            <div class="form-group">
                <label>توكن العميل:</label>
                <input type="text" id="clientToken" value="ABC12345">
            </div>
        </div>

        <!-- زر التنفيذ -->
        <div class="section">
            <button onclick="verifyDirect()" id="verifyBtn">
                🚀 التحقق المباشر من العميل (طلب واحد)
            </button>
        </div>

        <!-- منطقة التحميل -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            جاري المعالجة...
        </div>

        <!-- النتائج -->
        <div class="section">
            <h3>📊 نتيجة التحقق</h3>
            <div id="result" class="result">
                في انتظار التحقق من العميل...
            </div>
        </div>

        <!-- أمثلة الكود -->
        <div class="section">
            <h3>💻 أمثلة الكود للاستخدام</h3>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('curl')">cURL</button>
                <button class="tab" onclick="showTab('javascript')">JavaScript</button>
                <button class="tab" onclick="showTab('python')">Python</button>
                <button class="tab" onclick="showTab('php')">PHP</button>
            </div>

            <div id="curl" class="tab-content active">
                <div class="code-example" id="curlCode">
# للاستخدام من خارج السيرفر
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "agent001",
    "agent_login_password": "agent123",
    "client_code": "1000",
    "client_token": "ABC12345"
  }'
                </div>
            </div>

            <div id="javascript" class="tab-content">
                <div class="code-example" id="jsCode">
// JavaScript Example
async function verifyClient() {
    const response = await fetch('http://***********:8080/api/external/verify-direct', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            agent_login_name: 'agent001',
            agent_login_password: 'agent123',
            client_code: '1000',
            client_token: 'ABC12345'
        })
    });
    
    const result = await response.json();
    console.log(result);
}

verifyClient();
                </div>
            </div>

            <div id="python" class="tab-content">
                <div class="code-example">
# Python Example
import requests

data = {
    'agent_login_name': 'agent001',
    'agent_login_password': 'agent123',
    'client_code': '1000',
    'client_token': 'ABC12345'
}

response = requests.post(
    'http://***********:8080/api/external/verify-direct',
    json=data
)

result = response.json()
print(result)
                </div>
            </div>

            <div id="php" class="tab-content">
                <div class="code-example">
<?php
// PHP Example
$data = [
    'agent_login_name' => 'agent001',
    'agent_login_password' => 'agent123',
    'client_code' => '1000',
    'client_token' => 'ABC12345'
];

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents(
    'http://***********:8080/api/external/verify-direct',
    false,
    $context
);

$response = json_decode($result, true);
print_r($response);
?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث عنوان الخادم
        function updateServerUrl() {
            const serverType = document.getElementById('serverType').value;
            const serverUrl = document.getElementById('serverUrl');
            
            if (serverType === 'local') {
                serverUrl.value = 'http://localhost:8080';
            } else {
                serverUrl.value = 'http://***********:8080';
            }
            
            updateCodeExamples();
        }

        // تحديث أمثلة الكود
        function updateCodeExamples() {
            const serverUrl = document.getElementById('serverUrl').value;
            const agentLogin = document.getElementById('agentLogin').value;
            const agentPassword = document.getElementById('agentPassword').value;
            const clientCode = document.getElementById('clientCode').value;
            const clientToken = document.getElementById('clientToken').value;

            // تحديث cURL
            document.getElementById('curlCode').textContent = `curl -X POST ${serverUrl}/api/external/verify-direct \\
  -H "Content-Type: application/json" \\
  -d '{
    "agent_login_name": "${agentLogin}",
    "agent_login_password": "${agentPassword}",
    "client_code": "${clientCode}",
    "client_token": "${clientToken}"
  }'`;

            // تحديث JavaScript
            document.getElementById('jsCode').textContent = `async function verifyClient() {
    const response = await fetch('${serverUrl}/api/external/verify-direct', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            agent_login_name: '${agentLogin}',
            agent_login_password: '${agentPassword}',
            client_code: '${clientCode}',
            client_token: '${clientToken}'
        })
    });
    
    const result = await response.json();
    console.log(result);
}

verifyClient();`;
        }

        // عرض التبويبات
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            const tabButtons = document.querySelectorAll('.tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));
            
            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // عرض حالة التحميل
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // عرض النتيجة
        function showResult(message, type = 'info') {
            const element = document.getElementById('result');
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // التحقق المباشر من العميل
        async function verifyDirect() {
            showLoading(true);
            showResult('جاري التحقق من العميل...', 'info');
            
            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const agentLogin = document.getElementById('agentLogin').value;
                const agentPassword = document.getElementById('agentPassword').value;
                const clientCode = document.getElementById('clientCode').value;
                const clientToken = document.getElementById('clientToken').value;

                console.log('🚀 بدء التحقق المباشر...');
                console.log('الخادم:', serverUrl);
                console.log('الوكيل:', agentLogin);
                console.log('العميل:', clientCode);

                const response = await fetch(`${serverUrl}/api/external/verify-direct`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_login_name: agentLogin,
                        agent_login_password: agentPassword,
                        client_code: clientCode,
                        client_token: clientToken
                    })
                });

                const data = await response.json();
                console.log('📥 استجابة الخادم:', data);

                if (data.status === 'success') {
                    const agent = data.data.agent_info;
                    const client = data.data.client_info;
                    const verification = data.data.verification_result;
                    
                    const successMessage = `✅ تم التحقق بنجاح!

🤝 معلومات الوكيل:
   الاسم: ${agent.agent_name}
   النوع: ${agent.agency_type}
   الرقم: ${agent.agent_id}
   الحالة: ${verification.agent_verified ? 'مصادق ✅' : 'غير مصادق ❌'}

👤 معلومات العميل:
   الاسم: ${client.client_name}
   التطبيق: ${client.app_name}
   الرمز: ${client.client_code}
   الحالة: ${client.status_text}
   عنوان IP: ${client.ip_address}
   تاريخ الإنشاء: ${client.created_date}
   المنشئ: ${client.created_by_user}

🔐 التحقق:
   الوكيل: ${verification.agent_verified ? 'مصادق ✅' : 'فشل ❌'}
   العميل: ${verification.client_verified ? 'موجود ✅' : 'غير موجود ❌'}
   التوكن: ${verification.token_verified ? 'مطابق ✅' : 'غير مطابق ❌'}

⏰ معلومات العملية:
   الوقت: ${new Date(data.data.operation_info.timestamp).toLocaleString('ar-SA')}
   عنوان IP: ${data.data.operation_info.ip_address}
   طريقة التحقق: ${data.data.operation_info.verification_method}`;

                    showResult(successMessage, 'success');
                    
                } else {
                    let errorMessage = `❌ فشل التحقق: ${data.message}\n`;
                    
                    if (data.error_code === 'AGENT_AUTH_FAILED') {
                        errorMessage += '\n🔐 خطأ في بيانات الوكيل - تحقق من اسم المستخدم وكلمة المرور';
                    } else if (data.error_code === 'CLIENT_NOT_FOUND') {
                        errorMessage += `\n🔍 العميل برمز ${clientCode} غير موجود في النظام`;
                        if (data.agent_info) {
                            errorMessage += `\n✅ الوكيل مصادق: ${data.agent_info.agent_name}`;
                        }
                    } else if (data.error_code === 'TOKEN_MISMATCH') {
                        errorMessage += `\n🔐 التوكن ${clientToken} غير مطابق للتوكن المحفوظ`;
                        if (data.agent_info) {
                            errorMessage += `\n✅ الوكيل مصادق: ${data.agent_info.agent_name}`;
                        }
                        if (data.client_info) {
                            errorMessage += `\n✅ العميل موجود: ${data.client_info.client_code}`;
                        }
                    }
                    
                    errorMessage += `\n\n🔧 كود الخطأ: ${data.error_code}`;

                    showResult(errorMessage, 'error');
                }

            } catch (error) {
                console.error('❌ خطأ في الاتصال:', error);
                let errorMsg = `❌ خطأ في الاتصال: ${error.message}\n\n`;
                
                if (error.message.includes('Failed to fetch')) {
                    errorMsg += '🌐 تحقق من:\n';
                    errorMsg += '   - عنوان الخادم صحيح\n';
                    errorMsg += '   - الخادم يعمل\n';
                    errorMsg += '   - لا توجد مشاكل في الشبكة\n';
                    errorMsg += '   - استخدم العنوان الخارجي من خارج السيرفر';
                }
                
                showResult(errorMsg, 'error');
            }

            showLoading(false);
        }

        // تحديث أمثلة الكود عند تحميل الصفحة
        window.onload = function() {
            updateCodeExamples();
            
            // تحديث الكود عند تغيير القيم
            const inputs = ['agentLogin', 'agentPassword', 'clientCode', 'clientToken'];
            inputs.forEach(id => {
                document.getElementById(id).addEventListener('input', updateCodeExamples);
            });
        };
    </script>
</body>
</html>
