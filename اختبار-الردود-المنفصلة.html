<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 اختبار الردود المنفصلة - نظام الوكلاء</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 28px;
        }
        
        .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .responses-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #17a2b8;
        }
        
        .response-example {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        input, select, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 0;
            transition: all 0.3s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 15px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            text-align: center;
        }
        
        .test-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .test-agent-error {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .test-client-error {
            background: linear-gradient(45deg, #fd7e14, #e55a00);
            color: white;
        }
        
        .result {
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .agent-error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .client-error {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        
        .loading {
            background: #e2e3e5;
            color: #495057;
            border: 2px solid #ced4da;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 اختبار الردود المنفصلة</h1>
            <p class="subtitle">نظام الوكلاء - ردود محددة لكل نوع خطأ</p>
        </div>

        <!-- معلومات الردود -->
        <div class="responses-info">
            <h3>📊 أنواع الردود الجديدة</h3>
            
            <h4>✅ نجح التحقق:</h4>
            <div class="response-example">{"status":"success","client_status":1}  // العميل نشط
{"status":"success","client_status":0}  // العميل غير نشط</div>
            
            <h4>❌ خطأ في بيانات الوكيل:</h4>
            <div class="response-example">{"status":"agent_error"}</div>
            
            <h4>❌ خطأ في بيانات العميل:</h4>
            <div class="response-example">{"status":"client_error"}</div>
            
            <h4>❌ خطأ في الخادم:</h4>
            <div class="response-example">{"status":"error"}</div>
        </div>

        <!-- بيانات الاختبار -->
        <div class="section">
            <h3>📝 بيانات الاختبار</h3>
            <div class="form-group">
                <label>اسم المستخدم للوكيل:</label>
                <input type="text" id="agentUser" value="alghurasi">
            </div>
            <div class="form-group">
                <label>كلمة مرور الوكيل:</label>
                <input type="password" id="agentPass" value="alghurasi123">
            </div>
            <div class="form-group">
                <label>رمز العميل:</label>
                <input type="text" id="clientCode" value="1000">
            </div>
            <div class="form-group">
                <label>توكن العميل:</label>
                <input type="text" id="clientToken" value="ABC12345">
            </div>
        </div>

        <!-- أزرار الاختبار -->
        <div class="section">
            <h3>⚡ اختبارات الردود المختلفة</h3>
            <div class="test-buttons">
                <button class="test-btn test-success" onclick="testSuccess()">
                    ✅ اختبار النجاح
                </button>
                <button class="test-btn test-agent-error" onclick="testAgentError()">
                    🔐 اختبار خطأ الوكيل
                </button>
                <button class="test-btn test-client-error" onclick="testClientError()">
                    👤 اختبار خطأ العميل
                </button>
                <button class="test-btn test-client-error" onclick="testTokenError()">
                    🔑 اختبار خطأ التوكن
                </button>
            </div>
        </div>

        <!-- زر التحقق العام -->
        <div class="section">
            <button onclick="verifyClient()" id="verifyBtn">
                🔍 تنفيذ التحقق بالبيانات الحالية
            </button>
        </div>

        <!-- النتيجة -->
        <div class="section">
            <h3>📊 نتيجة الاختبار</h3>
            <div id="result" class="result info">
                اختر نوع الاختبار أو أدخل البيانات وانقر على "تنفيذ التحقق"...
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="section">
            <h3>💡 شرح الردود</h3>
            <p><strong>agent_error:</strong> يعني أن اسم المستخدم أو كلمة المرور للوكيل خاطئة</p>
            <p><strong>client_error:</strong> يعني أن العميل غير موجود أو التوكن غير مطابق</p>
            <p><strong>success:</strong> يعني أن جميع البيانات صحيحة، مع إرجاع حالة العميل</p>
            <p><strong>error:</strong> يعني خطأ عام في الخادم</p>
        </div>
    </div>

    <script>
        // عرض النتيجة
        function showResult(message, type = 'info') {
            const element = document.getElementById('result');
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // التحقق من العميل
        async function verifyClient() {
            showResult('⏳ جاري التحقق...', 'loading');
            
            try {
                const agentUser = document.getElementById('agentUser').value;
                const agentPass = document.getElementById('agentPass').value;
                const clientCode = document.getElementById('clientCode').value;
                const clientToken = document.getElementById('clientToken').value;

                const response = await fetch('http://***********:8080/api/external/verify-direct', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_login_name: agentUser,
                        agent_login_password: agentPass,
                        client_code: clientCode,
                        client_token: clientToken
                    })
                });

                const result = await response.json();
                console.log('النتيجة:', result);

                switch (result.status) {
                    case 'success':
                        if (result.client_status === 1) {
                            showResult(`✅ نجح التحقق - العميل نشط\n\nالرد: {"status":"success","client_status":1}\n\nالمعنى: العميل ${clientCode} نشط ويمكن المتابعة معه`, 'success');
                        } else {
                            showResult(`⚠️ نجح التحقق - العميل غير نشط\n\nالرد: {"status":"success","client_status":0}\n\nالمعنى: العميل ${clientCode} غير نشط ولا يمكن المتابعة`, 'success');
                        }
                        break;
                    case 'agent_error':
                        showResult(`🔐 خطأ في بيانات الوكيل\n\nالرد: {"status":"agent_error"}\n\nالمعنى: اسم المستخدم "${agentUser}" أو كلمة المرور خاطئة`, 'agent-error');
                        break;
                    case 'client_error':
                        showResult(`👤 خطأ في بيانات العميل\n\nالرد: {"status":"client_error"}\n\nالمعنى: العميل "${clientCode}" غير موجود أو التوكن "${clientToken}" غير مطابق`, 'client-error');
                        break;
                    case 'error':
                        showResult(`❌ خطأ في الخادم\n\nالرد: {"status":"error"}\n\nالمعنى: خطأ عام في النظام - حاول مرة أخرى`, 'error');
                        break;
                    default:
                        showResult(`❓ رد غير متوقع\n\nالرد: ${JSON.stringify(result)}\n\nتحقق من النظام`, 'error');
                }

            } catch (error) {
                console.error('خطأ:', error);
                showResult(`❌ خطأ في الاتصال\n\nالتفاصيل: ${error.message}\n\nتحقق من:\n• الاتصال بالإنترنت\n• عنوان الخادم\n• إعدادات جدار الحماية`, 'error');
            }
        }

        // اختبار النجاح
        function testSuccess() {
            document.getElementById('agentUser').value = 'alghurasi';
            document.getElementById('agentPass').value = 'alghurasi123';
            document.getElementById('clientCode').value = '1000';
            document.getElementById('clientToken').value = 'ABC12345';
            verifyClient();
        }

        // اختبار خطأ الوكيل
        function testAgentError() {
            document.getElementById('agentUser').value = 'alghurasi';
            document.getElementById('agentPass').value = 'WRONG_PASSWORD';
            document.getElementById('clientCode').value = '1000';
            document.getElementById('clientToken').value = 'ABC12345';
            verifyClient();
        }

        // اختبار خطأ العميل (عميل غير موجود)
        function testClientError() {
            document.getElementById('agentUser').value = 'alghurasi';
            document.getElementById('agentPass').value = 'alghurasi123';
            document.getElementById('clientCode').value = '9999';
            document.getElementById('clientToken').value = 'INVALID';
            verifyClient();
        }

        // اختبار خطأ التوكن
        function testTokenError() {
            document.getElementById('agentUser').value = 'alghurasi';
            document.getElementById('agentPass').value = 'alghurasi123';
            document.getElementById('clientCode').value = '1000';
            document.getElementById('clientToken').value = 'WRONG_TOKEN';
            verifyClient();
        }
    </script>
</body>
</html>
