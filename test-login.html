<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - YemClient</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: background 0.3s;
        }
        button:hover {
            background: #5a6fd8;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .server-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .info-card {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .info-card h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 5px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-unknown { background: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار تسجيل الدخول - YemClient</h1>
            <p>أداة شاملة لاختبار ومراقبة نظام تسجيل الدخول</p>
        </div>

        <!-- معلومات الخادم -->
        <div class="test-section">
            <h3>📊 حالة الخادم</h3>
            <button onclick="checkServerStatus()">فحص حالة الخادم</button>
            <div id="serverStatus" class="result info" style="display: none;"></div>
            <div id="serverInfo" class="server-info"></div>
        </div>

        <!-- اختبار تسجيل دخول المستخدم -->
        <div class="test-section">
            <h3>👤 اختبار تسجيل دخول المستخدم</h3>
            <div class="form-group">
                <label>اسم المستخدم:</label>
                <input type="text" id="userLoginName" value="admin" placeholder="اسم المستخدم">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="userPassword" value="admin123" placeholder="كلمة المرور">
            </div>
            <div class="form-group">
                <label>معرف الجهاز:</label>
                <input type="text" id="userDeviceId" placeholder="سيتم توليده تلقائياً">
            </div>
            <button onclick="testUserLogin()">اختبار دخول المستخدم</button>
            <div id="userLoginResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار تسجيل دخول العميل -->
        <div class="test-section">
            <h3>🏢 اختبار تسجيل دخول العميل</h3>
            <div class="form-group">
                <label>رقم العميل:</label>
                <input type="text" id="clientCode" value="1001" placeholder="رقم العميل">
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="clientPassword" value="123456" placeholder="كلمة المرور">
            </div>
            <button onclick="testClientLogin()">اختبار دخول العميل</button>
            <div id="clientLoginResult" class="result" style="display: none;"></div>
        </div>

        <!-- اختبار التحقق من الجلسة -->
        <div class="test-section">
            <h3>🔐 اختبار التحقق من الجلسة</h3>
            <button onclick="testSessionValidation()">اختبار التحقق من الجلسة</button>
            <div id="sessionResult" class="result" style="display: none;"></div>
        </div>

        <!-- محاولات تسجيل الدخول الأخيرة -->
        <div class="test-section">
            <h3>📋 محاولات تسجيل الدخول الأخيرة</h3>
            <button onclick="getLoginAttempts()">عرض المحاولات الأخيرة</button>
            <div id="loginAttemptsResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        const SERVER_URL = 'http://***********:8080';
        let currentToken = localStorage.getItem('token');

        // توليد معرف جهاز فريد
        function generateDeviceId() {
            let deviceId = localStorage.getItem('deviceId');
            if (!deviceId) {
                deviceId = `device_${Math.random().toString(36).substring(2, 11)}_${Date.now()}`;
                localStorage.setItem('deviceId', deviceId);
            }
            return deviceId;
        }

        // تحديث معرف الجهاز في النموذج
        document.getElementById('userDeviceId').value = generateDeviceId();

        // دالة عامة لإجراء طلبات HTTP
        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(SERVER_URL + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...(currentToken && { 'Authorization': `Bearer ${currentToken}` }),
                        ...options.headers
                    },
                    credentials: 'include',
                    ...options
                });

                const data = await response.json();
                return {
                    ok: response.ok,
                    status: response.status,
                    data
                };
            } catch (error) {
                return {
                    ok: false,
                    status: 0,
                    error: error.message
                };
            }
        }

        // فحص حالة الخادم
        async function checkServerStatus() {
            const statusDiv = document.getElementById('serverStatus');
            const infoDiv = document.getElementById('serverInfo');
            
            statusDiv.style.display = 'block';
            statusDiv.textContent = 'جاري فحص حالة الخادم...';
            statusDiv.className = 'result info';

            try {
                // فحص الصحة العامة
                const healthResult = await makeRequest('/health');
                
                // فحص معلومات النظام
                const systemResult = await makeRequest('/api/debug/system-info');

                if (healthResult.ok) {
                    statusDiv.textContent = `✅ الخادم يعمل بشكل طبيعي\nالحالة: ${healthResult.data.status}\nوقت التشغيل: ${Math.floor(healthResult.data.uptime / 60)} دقيقة`;
                    statusDiv.className = 'result success';

                    // عرض معلومات مفصلة
                    if (systemResult.ok) {
                        const info = systemResult.data.systemInfo;
                        infoDiv.innerHTML = `
                            <div class="info-card">
                                <h4>🖥️ الخادم</h4>
                                <p>Node.js: ${info.server.nodeVersion}</p>
                                <p>المنصة: ${info.server.platform}</p>
                                <p>المنفذ: ${info.server.port}</p>
                                <p>البيئة: ${info.server.environment}</p>
                            </div>
                            <div class="info-card">
                                <h4>💾 الذاكرة</h4>
                                <p>المستخدمة: ${Math.round(info.server.memory.used / 1024 / 1024)} MB</p>
                                <p>الكومة: ${Math.round(info.server.memory.heapUsed / 1024 / 1024)} MB</p>
                            </div>
                            <div class="info-card">
                                <h4>🗄️ قاعدة البيانات</h4>
                                <p>الحالة: <span class="status-indicator status-${info.database.connected ? 'online' : 'offline'}"></span> ${info.database.connected ? 'متصلة' : 'غير متصلة'}</p>
                            </div>
                        `;
                    }
                } else {
                    statusDiv.textContent = `❌ فشل في الاتصال بالخادم\nالحالة: ${healthResult.status}\nالخطأ: ${healthResult.error || healthResult.data?.error || 'خطأ غير معروف'}`;
                    statusDiv.className = 'result error';
                    infoDiv.innerHTML = '';
                }
            } catch (error) {
                statusDiv.textContent = `❌ خطأ في فحص الخادم: ${error.message}`;
                statusDiv.className = 'result error';
                infoDiv.innerHTML = '';
            }
        }

        // اختبار تسجيل دخول المستخدم
        async function testUserLogin() {
            const resultDiv = document.getElementById('userLoginResult');
            const loginName = document.getElementById('userLoginName').value;
            const password = document.getElementById('userPassword').value;
            const deviceId = document.getElementById('userDeviceId').value;

            resultDiv.style.display = 'block';
            resultDiv.textContent = 'جاري اختبار تسجيل دخول المستخدم...';
            resultDiv.className = 'result info';

            const result = await makeRequest('/api/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    loginName,
                    password,
                    deviceId,
                    userType: 'user'
                })
            });

            if (result.ok && result.data.success) {
                currentToken = result.data.token;
                if (currentToken) {
                    localStorage.setItem('token', currentToken);
                }
                
                resultDiv.textContent = `✅ تم تسجيل الدخول بنجاح!\nالمستخدم: ${result.data.user.username}\nالنوع: ${result.data.user.accountType}\nالتوكن: ${currentToken ? 'موجود' : 'غير موجود'}\nمعرف الجلسة: ${result.data.sessionId || 'غير متوفر'}`;
                resultDiv.className = 'result success';
            } else {
                resultDiv.textContent = `❌ فشل تسجيل الدخول\nالحالة: ${result.status}\nالخطأ: ${result.data?.message || result.data?.error || result.error || 'خطأ غير معروف'}\nالتفاصيل: ${JSON.stringify(result.data, null, 2)}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار تسجيل دخول العميل
        async function testClientLogin() {
            const resultDiv = document.getElementById('clientLoginResult');
            const clientCode = document.getElementById('clientCode').value;
            const password = document.getElementById('clientPassword').value;
            const deviceId = generateDeviceId();

            resultDiv.style.display = 'block';
            resultDiv.textContent = 'جاري اختبار تسجيل دخول العميل...';
            resultDiv.className = 'result info';

            const result = await makeRequest('/api/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    loginName: clientCode,
                    password,
                    deviceId,
                    userType: 'client'
                })
            });

            if (result.ok && result.data.success) {
                currentToken = result.data.token;
                if (currentToken) {
                    localStorage.setItem('token', currentToken);
                }
                
                resultDiv.textContent = `✅ تم تسجيل دخول العميل بنجاح!\nالعميل: ${result.data.user.username}\nرقم العميل: ${result.data.user.clientCode || clientCode}\nالنوع: ${result.data.user.accountType}\nالتوكن: ${currentToken ? 'موجود' : 'غير موجود'}`;
                resultDiv.className = 'result success';
            } else {
                resultDiv.textContent = `❌ فشل تسجيل دخول العميل\nالحالة: ${result.status}\nالخطأ: ${result.data?.message || result.data?.error || result.error || 'خطأ غير معروف'}\nالتفاصيل: ${JSON.stringify(result.data, null, 2)}`;
                resultDiv.className = 'result error';
            }
        }

        // اختبار التحقق من الجلسة
        async function testSessionValidation() {
            const resultDiv = document.getElementById('sessionResult');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'جاري اختبار التحقق من الجلسة...';
            resultDiv.className = 'result info';

            if (!currentToken) {
                resultDiv.textContent = '❌ لا يوجد توكن محفوظ. يرجى تسجيل الدخول أولاً.';
                resultDiv.className = 'result error';
                return;
            }

            const result = await makeRequest('/api/auth/validate');

            if (result.ok && result.data.success) {
                resultDiv.textContent = `✅ الجلسة صالحة!\nالمستخدم: ${result.data.user.username}\nالنوع: ${result.data.user.accountType}\nالحالة: ${result.data.user.isActive ? 'نشط' : 'غير نشط'}`;
                resultDiv.className = 'result success';
            } else {
                resultDiv.textContent = `❌ الجلسة غير صالحة\nالحالة: ${result.status}\nالخطأ: ${result.data?.message || result.error || 'خطأ غير معروف'}`;
                resultDiv.className = 'result error';
                
                // مسح التوكن المنتهي الصلاحية
                currentToken = null;
                localStorage.removeItem('token');
            }
        }

        // عرض محاولات تسجيل الدخول الأخيرة
        async function getLoginAttempts() {
            const resultDiv = document.getElementById('loginAttemptsResult');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'جاري جلب محاولات تسجيل الدخول...';
            resultDiv.className = 'result info';

            const result = await makeRequest('/api/debug/login-attempts?limit=10');

            if (result.ok && result.data.success) {
                const attempts = result.data.attempts;
                let output = `📋 آخر ${attempts.length} محاولات تسجيل دخول:\n\n`;
                
                attempts.forEach((attempt, index) => {
                    const status = attempt.success ? '✅' : '❌';
                    const time = new Date(attempt.timestamp).toLocaleString('ar-SA');
                    output += `${index + 1}. ${status} ${attempt.user} (${attempt.userType})\n`;
                    output += `   الوقت: ${time}\n`;
                    output += `   IP: ${attempt.ipAddress}\n`;
                    output += `   الجهاز: ${attempt.deviceId}\n\n`;
                });
                
                resultDiv.textContent = output;
                resultDiv.className = 'result success';
            } else {
                resultDiv.textContent = `❌ فشل في جلب محاولات تسجيل الدخول\nالحالة: ${result.status}\nالخطأ: ${result.data?.error || result.error || 'خطأ غير معروف'}`;
                resultDiv.className = 'result error';
            }
        }

        // فحص حالة الخادم عند تحميل الصفحة
        window.onload = function() {
            checkServerStatus();
        };
    </script>
</body>
</html>
