const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function checkDeviceIds() {
  try {
    console.log('🔍 فحص معرفات الأجهزة للمستخدمين:')
    console.log('=======================================')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        isActive: true
      }
    })

    for (const user of users) {
      console.log(`👤 المستخدم: ${user.username}`)
      console.log(`   - ID: ${user.id}`)
      console.log(`   - اسم تسجيل الدخول: ${user.loginName}`)
      console.log(`   - نشط: ${user.isActive ? 'نعم' : 'لا'}`)
      console.log(`   - معرف الجهاز: ${user.deviceId || 'غير محدد'}`)
      console.log('   ---')
    }

  } catch (error) {
    console.error('❌ خطأ في فحص معرفات الأجهزة:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkDeviceIds()
