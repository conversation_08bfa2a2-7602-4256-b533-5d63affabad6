const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function addSecondDeviceToHashdi() {
  try {
    console.log('📱 إضافة الجهاز الثاني للمستخدم محمد الحاشدي')
    console.log('================================================')
    
    // البحث عن المستخدم محمد الحاشدي
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true
      }
    })

    if (!user) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }
    
    console.log(`👤 تم العثور على المستخدم: ${user.username}`)
    console.log(`📱 الجهاز الحالي: ${user.deviceId || 'غير محدد'}`)
    
    // الأجهزة المطلوبة
    const device1 = 'honbi5nms_1751046183491'  // الجهاز القديم
    const device2 = 'b78dex9jv_1751070014503'  // الجهاز الجديد
    
    console.log(`📱 الجهاز الأول (القديم): ${device1}`)
    console.log(`📱 الجهاز الثاني (الجديد): ${device2}`)
    
    // إنشاء قائمة الأجهزة الصحيحة
    const correctDeviceList = `${device1},${device2}`
    
    console.log(`📱 قائمة الأجهزة المطلوبة: ${correctDeviceList}`)
    
    // تحديث قاعدة البيانات
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { deviceId: correctDeviceList }
    })
    
    console.log('✅ تم تحديث قاعدة البيانات بنجاح!')
    
    // عرض النتيجة النهائية
    const finalDevices = updatedUser.deviceId.split(',').map(id => id.trim())
    
    console.log('')
    console.log('🎉 النتيجة النهائية:')
    console.log(`👤 المستخدم: ${updatedUser.username}`)
    console.log(`🆔 معرف المستخدم: ${updatedUser.id}`)
    console.log(`📱 عدد الأجهزة: ${finalDevices.length}`)
    console.log(`📱 قائمة الأجهزة:`)
    finalDevices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device}`)
    })
    
    console.log('')
    console.log('🔐 الآن يمكن للمستخدم تسجيل الدخول من أي من الجهازين!')
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الجهاز:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addSecondDeviceToHashdi()
