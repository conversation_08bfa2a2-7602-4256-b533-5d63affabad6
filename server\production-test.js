const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'YemClient-Test/2.0'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: parsed
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testProductionServer() {
  console.log('🧪 Production Server Tests Starting...\n');
  
  const tests = [];
  let passed = 0;
  let failed = 0;

  try {
    // 1. Health Check
    console.log('Testing: Health Check');
    const healthResult = await makeRequest('/health');
    
    if (healthResult.status === 200 && healthResult.data.status === 'healthy') {
      console.log('✅ Health Check: Status 200');
      console.log(`   Database: ${healthResult.data.database?.status || 'unknown'}`);
      console.log(`   Version: ${healthResult.data.version || 'unknown'}`);
      console.log(`   Environment: ${healthResult.data.environment || 'unknown'}`);
      passed++;
    } else {
      console.log('❌ Health Check: Failed');
      failed++;
    }
    console.log('');

    // 2. Ready Check
    console.log('Testing: Ready Check');
    const readyResult = await makeRequest('/ready');
    
    if (readyResult.status === 200) {
      console.log('✅ Ready Check: Status 200');
      passed++;
    } else {
      console.log('❌ Ready Check: Failed');
      failed++;
    }
    console.log('');

    // 3. Metrics
    console.log('Testing: Metrics');
    const metricsResult = await makeRequest('/metrics');
    
    if (metricsResult.status === 200 && metricsResult.data.uptime !== undefined) {
      console.log('✅ Metrics: Status 200');
      console.log(`   Uptime: ${Math.floor(metricsResult.data.uptime)}s`);
      console.log(`   Memory: ${Math.floor(metricsResult.data.memory?.rss / 1024 / 1024)}MB`);
      console.log(`   Platform: ${metricsResult.data.platform}`);
      passed++;
    } else {
      console.log('❌ Metrics: Failed');
      failed++;
    }
    console.log('');

    // 4. Security Headers
    console.log('Testing: Security Headers');
    const securityResult = await makeRequest('/health');
    
    const securityHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection'
    ];
    
    let securityPassed = true;
    securityHeaders.forEach(header => {
      if (!securityResult.headers[header]) {
        securityPassed = false;
      }
    });
    
    if (securityPassed) {
      console.log('✅ Security Headers: Present');
      console.log(`   X-Content-Type-Options: ${securityResult.headers['x-content-type-options']}`);
      console.log(`   X-Frame-Options: ${securityResult.headers['x-frame-options']}`);
      passed++;
    } else {
      console.log('❌ Security Headers: Missing');
      failed++;
    }
    console.log('');

    // 5. Rate Limiting Test
    console.log('Testing: Rate Limiting');
    try {
      // إرسال عدة طلبات سريعة
      const rateLimitPromises = [];
      for (let i = 0; i < 3; i++) {
        rateLimitPromises.push(makeRequest('/health'));
      }
      
      const rateLimitResults = await Promise.all(rateLimitPromises);
      const allSuccess = rateLimitResults.every(result => result.status === 200);
      
      if (allSuccess) {
        console.log('✅ Rate Limiting: Working (allows normal requests)');
        passed++;
      } else {
        console.log('❌ Rate Limiting: Too restrictive');
        failed++;
      }
    } catch (error) {
      console.log('❌ Rate Limiting: Error -', error.message);
      failed++;
    }
    console.log('');

    // 6. Database Connection
    console.log('Testing: Database APIs');
    const dbTests = [
      { name: 'Users API', path: '/api/users' },
      { name: 'Clients API', path: '/api/clients' },
      { name: 'Agents API', path: '/api/agents' },
      { name: 'Data Records API', path: '/api/data-records' },
      { name: 'Security Stats API', path: '/api/security/stats' }
    ];

    for (const test of dbTests) {
      try {
        const result = await makeRequest(test.path);
        if (result.status === 200) {
          console.log(`✅ ${test.name}: Status 200`);
          passed++;
        } else {
          console.log(`❌ ${test.name}: Status ${result.status}`);
          failed++;
        }
      } catch (error) {
        console.log(`❌ ${test.name}: Error - ${error.message}`);
        failed++;
      }
    }
    console.log('');

    // 7. Authentication Test
    console.log('Testing: Authentication');
    try {
      const loginResult = await makeRequest('/api/auth/login', 'POST', {
        loginName: 'admin',
        password: 'admin123456',
        deviceId: 'test-device'
      });
      
      if (loginResult.status === 200 && loginResult.data.success) {
        console.log('✅ Authentication: Login successful');
        console.log(`   User: ${loginResult.data.user?.username || 'unknown'}`);
        console.log(`   Account Type: ${loginResult.data.user?.accountType || 'unknown'}`);
        passed++;
      } else {
        console.log('❌ Authentication: Login failed');
        console.log(`   Message: ${loginResult.data.message || 'unknown'}`);
        failed++;
      }
    } catch (error) {
      console.log('❌ Authentication: Error -', error.message);
      failed++;
    }
    console.log('');

    // 8. Error Handling
    console.log('Testing: Error Handling');
    try {
      const errorResult = await makeRequest('/api/nonexistent');
      
      if (errorResult.status === 404) {
        console.log('✅ Error Handling: 404 for non-existent endpoint');
        passed++;
      } else {
        console.log('❌ Error Handling: Unexpected response for 404');
        failed++;
      }
    } catch (error) {
      console.log('❌ Error Handling: Error -', error.message);
      failed++;
    }
    console.log('');

    // 9. Static Files
    console.log('Testing: Static Files');
    try {
      const staticResult = await makeRequest('/');
      
      if (staticResult.status === 200 || staticResult.status === 503) {
        console.log('✅ Static Files: Serving correctly');
        passed++;
      } else {
        console.log('❌ Static Files: Not serving correctly');
        failed++;
      }
    } catch (error) {
      console.log('❌ Static Files: Error -', error.message);
      failed++;
    }
    console.log('');

    // النتائج النهائية
    const total = passed + failed;
    const successRate = ((passed / total) * 100).toFixed(2);
    
    console.log('📊 Production Server Test Results:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📈 Success Rate: ${successRate}%`);
    
    if (successRate >= 90) {
      console.log('\n🎉 Production server is ready for deployment!');
    } else if (successRate >= 70) {
      console.log('\n⚠️  Production server has some issues but is functional');
    } else {
      console.log('\n❌ Production server has significant issues');
    }

  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
}

// تشغيل الاختبارات
testProductionServer();
