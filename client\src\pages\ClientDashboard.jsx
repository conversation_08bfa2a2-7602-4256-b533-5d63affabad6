import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  Typography,
  Grid,
  TextField,
  Button,
  Alert,
  Divider,
  IconButton,
  CircularProgress,
  Container,
  Avatar,
  Chip
} from '@mui/material'
import {
  Person as PersonIcon,
  Business as BusinessIcon,
  VpnKey as VpnKeyIcon,
  Security as SecurityIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  ExitToApp as LogoutIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material'
import { createTheme, ThemeProvider } from '@mui/material/styles'
import { useSnackbar } from 'notistack'

// إنشاء ثيم مخصص مع خط Khalid-Art
const customTheme = createTheme({
  typography: {
    fontFamily: [
      'Khalid-Art-bold',
      'Arial',
      'sans-serif'
    ].join(','),
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @font-face {
          font-family: 'Khalid-Art-bold';
          src: url('./fonts/Khalid-Art-bold.ttf') format('truetype');
          font-weight: bold;
          font-style: normal;
        }
      `,
    },
  },
})

const ClientDashboard = () => {
  const [clientData, setClientData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [editingPassword, setEditingPassword] = useState(false)
  const [editingToken, setEditingToken] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: '',
    newToken: ''
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const { enqueueSnackbar } = useSnackbar()

  // جلب بيانات العميل عند تحميل الصفحة
  useEffect(() => {
    const loadClientData = () => {
      try {
        const storedData = localStorage.getItem('clientData')
        if (storedData) {
          const data = JSON.parse(storedData)
          setClientData(data)
          setFormData(prev => ({
            ...prev,
            newToken: data.token || ''
          }))
        } else {
          // إعادة توجيه لصفحة الدخول إذا لم توجد بيانات
          window.location.replace('/')
        }
      } catch (error) {
        console.error('Error loading client data:', error)
        window.location.replace('/')
      } finally {
        setLoading(false)
      }
    }

    loadClientData()
  }, [])

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
    setSuccess('')
  }

  const handleUpdatePassword = async () => {
    if (!formData.newPassword) {
      setError('يرجى إدخال كلمة المرور الجديدة')
      return
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين')
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/client/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientId: clientData.id,
          password: formData.newPassword
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setSuccess('تم تحديث كلمة المرور بنجاح')
        enqueueSnackbar('تم تحديث كلمة المرور بنجاح', { variant: 'success' })
        setEditingPassword(false)
        setFormData(prev => ({
          ...prev,
          newPassword: '',
          confirmPassword: ''
        }))
      } else {
        setError(data.message || 'فشل في تحديث كلمة المرور')
        enqueueSnackbar(data.message || 'فشل في تحديث كلمة المرور', { variant: 'error' })
      }
    } catch (error) {
      console.error('Update password error:', error)
      setError('حدث خطأ في تحديث كلمة المرور')
      enqueueSnackbar('حدث خطأ في تحديث كلمة المرور', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateToken = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/client/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          clientId: clientData.id,
          token: formData.newToken
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        setSuccess('تم تحديث رمز التوكن بنجاح')
        enqueueSnackbar('تم تحديث رمز التوكن بنجاح', { variant: 'success' })
        setEditingToken(false)

        // تحديث البيانات المحلية
        const updatedClientData = {
          ...clientData,
          token: data.client.token
        }
        setClientData(updatedClientData)
        localStorage.setItem('clientData', JSON.stringify(updatedClientData))
      } else {
        setError(data.message || 'فشل في تحديث رمز التوكن')
        enqueueSnackbar(data.message || 'فشل في تحديث رمز التوكن', { variant: 'error' })
      }
    } catch (error) {
      console.error('Update token error:', error)
      setError('حدث خطأ في تحديث رمز التوكن')
      enqueueSnackbar('حدث خطأ في تحديث رمز التوكن', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = () => {
    if (window.confirm('هل أنت متأكد من تسجيل الخروج؟')) {
      localStorage.removeItem('clientData')
      sessionStorage.clear()
      window.location.replace('/')
    }
  }

  const handleCancelEdit = () => {
    setEditingPassword(false)
    setEditingToken(false)
    setFormData(prev => ({
      ...prev,
      newPassword: '',
      confirmPassword: '',
      newToken: clientData?.token || ''
    }))
    setError('')
    setSuccess('')
  }

  if (loading) {
    return (
      <ThemeProvider theme={customTheme}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}>
          <CircularProgress size={60} sx={{ color: 'white' }} />
        </Box>
      </ThemeProvider>
    )
  }

  if (!clientData) {
    return (
      <ThemeProvider theme={customTheme}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
        }}>
          <Typography variant="h6" sx={{ color: 'white' }}>
            لا توجد بيانات عميل
          </Typography>
        </Box>
      </ThemeProvider>
    )
  }

  return (
    <ThemeProvider theme={customTheme}>
      <Box sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        py: 4
      }}>
        <Container maxWidth="md">
          {/* Header */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 4
          }}>
            <Typography variant="h4" sx={{
              color: 'white',
              fontWeight: 'bold',
              textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
            }}>
              🏢 نظام ادارة التطبيقات الموحد
            </Typography>
            <Button
              variant="contained"
              startIcon={<LogoutIcon />}
              onClick={handleLogout}
              sx={{
                background: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
                '&:hover': {
                  background: 'linear-gradient(135deg, #c0392b 0%, #a93226 100%)',
                  transform: 'translateY(-2px)',
                  boxShadow: '0 8px 25px rgba(231, 76, 60, 0.3)'
                }
              }}
            >
              تسجيل الخروج
            </Button>
          </Box>

          {/* رسائل النجاح والخطأ */}
          {error && (
            <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity="success" sx={{ mb: 3, borderRadius: 2 }}>
              {success}
            </Alert>
          )}

          {/* بطاقة معلومات العميل */}
          <Card sx={{
            borderRadius: 4,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
            background: 'linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%)',
            border: '1px solid rgba(0,0,0,0.05)'
          }}>
            <CardContent sx={{ p: 4 }}>
              {/* عنوان البطاقة */}
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 3,
                pb: 2,
                borderBottom: '2px solid #e9ecef'
              }}>
                <Avatar sx={{
                  mr: 2,
                  bgcolor: 'primary.main',
                  width: 60,
                  height: 60,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                }}>
                  <BusinessIcon sx={{ fontSize: 30 }} />
                </Avatar>
                <Box>
                  <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#2c3e50' }}>
                    معلومات العميل
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    عرض وتحديث بيانات العميل
                  </Typography>
                </Box>
              </Box>

              <Grid container spacing={3}>
                {/* المعلومات الأساسية */}
                <Grid item xs={12}>
                  <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom sx={{
                        color: '#1976d2',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1
                      }}>
                        <PersonIcon /> المعلومات الأساسية
                      </Typography>
                      <Divider sx={{ mb: 2 }} />

                      <Grid container spacing={2}>
                        <Grid item xs={12} md={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                              <strong>رمز العميل:</strong>
                            </Typography>
                            <Chip
                              label={clientData.clientCode}
                              color="primary"
                              variant="outlined"
                              size="small"
                            />
                          </Box>
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                            <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                              <strong>اسم العميل:</strong>
                            </Typography>
                            <Typography variant="body2">{clientData.clientName}</Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                {/* تحديث كلمة المرور */}
                <Grid item xs={12}>
                  <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%)' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom sx={{
                        color: '#f57c00',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        justifyContent: 'space-between'
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <VpnKeyIcon /> تحديث كلمة المرور
                        </Box>
                        {!editingPassword && (
                          <IconButton
                            size="small"
                            onClick={() => setEditingPassword(true)}
                            sx={{ color: '#f57c00' }}
                            title="تعديل كلمة المرور"
                          >
                            <EditIcon />
                          </IconButton>
                        )}
                      </Typography>
                      <Divider sx={{ mb: 2 }} />

                      {!editingPassword ? (
                        <Typography variant="body2" color="text.secondary">
                          اضغط على أيقونة التعديل لتغيير كلمة المرور
                        </Typography>
                      ) : (
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6}>
                            <TextField
                              fullWidth
                              type={showPassword ? 'text' : 'password'}
                              label="كلمة المرور الجديدة"
                              name="newPassword"
                              value={formData.newPassword}
                              onChange={handleChange}
                              disabled={loading}
                              InputProps={{
                                endAdornment: (
                                  <IconButton
                                    onClick={() => setShowPassword(!showPassword)}
                                    edge="end"
                                  >
                                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                                  </IconButton>
                                )
                              }}
                            />
                          </Grid>
                          <Grid item xs={12} md={6}>
                            <TextField
                              fullWidth
                              type={showPassword ? 'text' : 'password'}
                              label="تأكيد كلمة المرور"
                              name="confirmPassword"
                              value={formData.confirmPassword}
                              onChange={handleChange}
                              disabled={loading}
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                              <Button
                                variant="contained"
                                startIcon={<SaveIcon />}
                                onClick={handleUpdatePassword}
                                disabled={loading}
                                sx={{
                                  background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                                  '&:hover': {
                                    background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)'
                                  }
                                }}
                              >
                                حفظ
                              </Button>
                              <Button
                                variant="outlined"
                                startIcon={<CancelIcon />}
                                onClick={handleCancelEdit}
                                disabled={loading}
                              >
                                إلغاء
                              </Button>
                            </Box>
                          </Grid>
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>

                {/* تحديث رمز التوكن */}
                <Grid item xs={12}>
                  <Card sx={{ mb: 3, background: 'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)' }}>
                    <CardContent>
                      <Typography variant="h6" gutterBottom sx={{
                        color: '#7b1fa2',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                        justifyContent: 'space-between'
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SecurityIcon /> رمز التوكن
                        </Box>
                        {!editingToken && (
                          <IconButton
                            size="small"
                            onClick={() => setEditingToken(true)}
                            sx={{ color: '#7b1fa2' }}
                            title="تعديل رمز التوكن"
                          >
                            <EditIcon />
                          </IconButton>
                        )}
                      </Typography>
                      <Divider sx={{ mb: 2 }} />

                      {!editingToken ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" color="text.secondary" sx={{ minWidth: '120px' }}>
                            <strong>رمز التوكن الحالي:</strong>
                          </Typography>
                          <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.9rem' }}>
                            {clientData.token || 'غير محدد'}
                          </Typography>
                        </Box>
                      ) : (
                        <Grid container spacing={2}>
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              label="رمز التوكن الجديد"
                              name="newToken"
                              value={formData.newToken}
                              onChange={handleChange}
                              disabled={loading}
                              placeholder="أدخل رمز التوكن الجديد"
                              inputProps={{
                                style: { fontFamily: 'monospace' }
                              }}
                            />
                          </Grid>
                          <Grid item xs={12}>
                            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                              <Button
                                variant="contained"
                                startIcon={<SaveIcon />}
                                onClick={handleUpdateToken}
                                disabled={loading}
                                sx={{
                                  background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                                  '&:hover': {
                                    background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)'
                                  }
                                }}
                              >
                                حفظ
                              </Button>
                              <Button
                                variant="outlined"
                                startIcon={<CancelIcon />}
                                onClick={handleCancelEdit}
                                disabled={loading}
                              >
                                إلغاء
                              </Button>
                            </Box>
                          </Grid>
                        </Grid>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Container>
      </Box>
    </ThemeProvider>
  )
}

export default ClientDashboard
