@echo off
echo ========================================
echo       إيقاف جميع خوادم النظام
echo ========================================
echo.

echo 1. إيقاف جميع عمليات Node.js...
taskkill /F /IM node.exe /T >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم إيقاف عمليات Node.js
) else (
    echo ℹ️ لم توجد عمليات Node.js للإيقاف
)

echo 2. إيقاف العمليات على المنفذ 8080...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080 2^>nul') do (
    taskkill /F /PID %%a >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        echo ✅ تم إيقاف العملية %%a
    )
)

echo 3. إيقاف العمليات على المنفذ 3000...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000 2^>nul') do (
    taskkill /F /PID %%a >nul 2>&1
    if !ERRORLEVEL! EQU 0 (
        echo ✅ تم إيقاف العملية %%a
    )
)

echo.
echo 4. التحقق من النتيجة...
netstat -ano | findstr ":8080\|:3000" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ⚠️ لا تزال هناك عمليات تعمل على المنافذ:
    netstat -ano | findstr ":8080\|:3000"
) else (
    echo ✅ تم إيقاف جميع الخوادم بنجاح
)

echo.
echo ========================================
echo        تم الانتهاء من الإيقاف
echo ========================================
pause
