/**
 * مثال بسيط لوكيل الغراسي للتحقق من العميل
 * Yemen Client Management System - Simple Agent Example
 */

// إعدادات الوكيل
const AGENT_CONFIG = {
    serverUrl: 'http://localhost:8080/api/external',
    loginName: 'agent001',  // اسم تسجيل دخول وكيل الغراسي
    password: 'agent123'    // كلمة مرور الوكيل
};

// بيانات العميل للتحقق
const CLIENT_DATA = {
    code: '1000',      // رمز العميل
    token: 'ABC12345'  // توكن العميل
};

/**
 * الخطوة 1: تسجيل دخول الوكيل
 */
async function authenticateAgent() {
    console.log('🔐 بدء تسجيل دخول الوكيل...');
    
    try {
        const response = await fetch(`${AGENT_CONFIG.serverUrl}/agent/auth`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                login_name: AGENT_CONFIG.loginName,
                login_password: AGENT_CONFIG.password
            })
        });

        const data = await response.json();
        
        if (data.status === 'success') {
            console.log('✅ تم تسجيل الدخول بنجاح!');
            console.log('اسم الوكيل:', data.data.agent_name);
            console.log('نوع الوكالة:', data.data.agency_type);
            console.log('التوكن:', data.data.token.substring(0, 20) + '...');
            
            return data.data.token; // إرجاع التوكن
        } else {
            console.error('❌ فشل تسجيل الدخول:', data.message);
            return null;
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error.message);
        return null;
    }
}

/**
 * الخطوة 2: التحقق من العميل
 */
async function verifyClient(agentToken) {
    console.log('👤 بدء التحقق من العميل...');
    console.log('رمز العميل:', CLIENT_DATA.code);
    console.log('توكن العميل:', CLIENT_DATA.token);
    
    try {
        const response = await fetch(`${AGENT_CONFIG.serverUrl}/client/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${agentToken}`
            },
            body: JSON.stringify({
                client_code: CLIENT_DATA.code,
                token: CLIENT_DATA.token
            })
        });

        const data = await response.json();
        
        if (data.status === 'success') {
            console.log('✅ تم العثور على العميل!');
            console.log('اسم العميل:', data.data.client_name);
            console.log('اسم التطبيق:', data.data.app_name);
            console.log('حالة العميل:', data.data.status === 1 ? 'نشط' : 'غير نشط');
            console.log('عنوان IP:', data.data.ip_address);
            console.log('تاريخ الإنشاء:', data.data.created_date);
            
            return {
                success: true,
                clientData: data.data
            };
        } else {
            console.error('❌ فشل التحقق من العميل:', data.message);
            
            if (data.error_code === 'CLIENT_NOT_FOUND') {
                console.error('🔍 العميل غير موجود في النظام');
            } else if (data.error_code === 'TOKEN_MISMATCH') {
                console.error('🔐 التوكن غير مطابق');
            }
            
            return {
                success: false,
                error: data.message,
                errorCode: data.error_code
            };
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * العملية الكاملة: تسجيل دخول + التحقق من العميل
 */
async function fullVerificationProcess() {
    console.log('🚀 بدء العملية الكاملة للتحقق من العميل...');
    console.log('=' .repeat(50));
    
    // الخطوة 1: تسجيل دخول الوكيل
    const agentToken = await authenticateAgent();
    
    if (!agentToken) {
        console.error('💥 فشل في تسجيل دخول الوكيل - توقف العملية');
        return {
            success: false,
            step: 'authentication',
            error: 'فشل في تسجيل دخول الوكيل'
        };
    }
    
    console.log(''); // سطر فارغ
    
    // الخطوة 2: التحقق من العميل
    const verificationResult = await verifyClient(agentToken);
    
    console.log(''); // سطر فارغ
    console.log('📊 ملخص النتائج:');
    console.log('=' .repeat(50));
    
    if (verificationResult.success) {
        console.log('🎉 العملية مكتملة بنجاح!');
        console.log('✅ الوكيل: مصادق');
        console.log('✅ العميل: موجود ومتحقق منه');
        console.log('✅ التوكن: مطابق');
        
        return {
            success: true,
            agentAuthenticated: true,
            clientVerified: true,
            clientData: verificationResult.clientData
        };
    } else {
        console.log('⚠️ العملية مكتملة مع أخطاء');
        console.log('✅ الوكيل: مصادق');
        console.log('❌ العميل: فشل التحقق');
        console.log('❌ السبب:', verificationResult.error);
        
        return {
            success: false,
            agentAuthenticated: true,
            clientVerified: false,
            error: verificationResult.error,
            errorCode: verificationResult.errorCode
        };
    }
}

/**
 * مثال للاستخدام في Node.js
 */
if (typeof module !== 'undefined' && module.exports) {
    // تصدير الوظائف للاستخدام في Node.js
    module.exports = {
        authenticateAgent,
        verifyClient,
        fullVerificationProcess,
        AGENT_CONFIG,
        CLIENT_DATA
    };
    
    // تشغيل المثال إذا تم استدعاء الملف مباشرة
    if (require.main === module) {
        // تحتاج إلى تثبيت node-fetch أولاً: npm install node-fetch
        const fetch = require('node-fetch');
        global.fetch = fetch;
        
        fullVerificationProcess().then(result => {
            console.log('\n🏁 النتيجة النهائية:', result);
        }).catch(error => {
            console.error('\n💥 خطأ عام:', error);
        });
    }
}

/**
 * مثال للاستخدام في المتصفح
 */
if (typeof window !== 'undefined') {
    // إضافة الوظائف إلى النافذة العامة
    window.YemenClientAgent = {
        authenticateAgent,
        verifyClient,
        fullVerificationProcess,
        AGENT_CONFIG,
        CLIENT_DATA
    };
    
    console.log('🌐 مكتبة وكيل الغراسي جاهزة للاستخدام في المتصفح');
    console.log('استخدم: YemenClientAgent.fullVerificationProcess()');
}

/**
 * مثال لاستخدام الكود في تطبيق ويب
 */
async function webExample() {
    console.log('🌐 مثال للاستخدام في تطبيق الويب...');
    
    // يمكنك تغيير بيانات العميل هنا
    CLIENT_DATA.code = '1000';
    CLIENT_DATA.token = 'ABC12345';
    
    const result = await fullVerificationProcess();
    
    // عرض النتيجة في صفحة الويب
    if (typeof document !== 'undefined') {
        const resultDiv = document.getElementById('result');
        if (resultDiv) {
            if (result.success) {
                resultDiv.innerHTML = `
                    <div style="color: green;">
                        <h3>✅ تم التحقق من العميل بنجاح</h3>
                        <p><strong>اسم العميل:</strong> ${result.clientData.client_name}</p>
                        <p><strong>حالة العميل:</strong> ${result.clientData.status === 1 ? 'نشط' : 'غير نشط'}</p>
                        <p><strong>التوكن:</strong> مطابق</p>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="color: red;">
                        <h3>❌ فشل التحقق من العميل</h3>
                        <p><strong>السبب:</strong> ${result.error}</p>
                        <p><strong>كود الخطأ:</strong> ${result.errorCode || 'غير محدد'}</p>
                    </div>
                `;
            }
        }
    }
    
    return result;
}

/**
 * مثال لتحديث بيانات العميل والتحقق مرة أخرى
 */
async function checkDifferentClient(clientCode, clientToken) {
    console.log(`🔄 التحقق من عميل جديد: ${clientCode}`);
    
    // تحديث بيانات العميل
    CLIENT_DATA.code = clientCode;
    CLIENT_DATA.token = clientToken;
    
    // تنفيذ العملية
    return await fullVerificationProcess();
}

// أمثلة للاستخدام:
console.log(`
📚 أمثلة الاستخدام:

1. العملية الكاملة:
   fullVerificationProcess()

2. التحقق من عميل مختلف:
   checkDifferentClient('1001', 'XYZ67890')

3. في تطبيق الويب:
   webExample()

4. خطوة بخطوة:
   const token = await authenticateAgent()
   const result = await verifyClient(token)
`);

/**
 * مثال لمعالجة أخطاء الشبكة
 */
async function robustVerification(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        console.log(`🔄 المحاولة ${attempt} من ${maxRetries}...`);
        
        try {
            const result = await fullVerificationProcess();
            console.log('✅ نجحت العملية في المحاولة', attempt);
            return result;
        } catch (error) {
            console.error(`❌ فشلت المحاولة ${attempt}:`, error.message);
            
            if (attempt === maxRetries) {
                console.error('💥 فشلت جميع المحاولات');
                throw error;
            }
            
            // انتظار قبل المحاولة التالية
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
}
