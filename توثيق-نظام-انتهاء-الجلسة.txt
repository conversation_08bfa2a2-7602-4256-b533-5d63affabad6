# 🕐 توثيق نظام انتهاء الجلسة التلقائي
# Yemen Client Management System - Session Timeout Documentation

========================================
📋 نظرة عامة:
========================================

تم إضافة نظام انتهاء الجلسة التلقائي لحماية النظام من الوصول غير المصرح به
في حالة ترك المستخدم للنظام دون تسجيل خروج.

🎯 **الهدف:** حماية البيانات الحساسة
⏰ **المدة:** 5 دقائق عدم نشاط
🔔 **التحذير:** دقيقة واحدة قبل الانتهاء
🔒 **الإجراء:** تسجيل خروج تلقائي

========================================
🔧 المكونات المضافة:
========================================

## 1. useSessionTimeout Hook:
📄 **الملف:** `client/src/hooks/useSessionTimeout.js`
🎯 **الوظيفة:** إدارة مؤقت الجلسة ومراقبة النشاط

**المميزات:**
- مراقبة أحداث النشاط (ماوس، لوحة مفاتيح، تمرير)
- إعادة تعيين المؤقت عند النشاط
- تحذير قبل دقيقة واحدة من الانتهاء
- تسجيل خروج تلقائي
- مسح البيانات المحفوظة

## 2. SessionStatus Component:
📄 **الملف:** `client/src/components/SessionStatus.jsx`
🎯 **الوظيفة:** عرض حالة الجلسة للمستخدم

**المميزات:**
- عرض الوقت المتبقي
- شريط تقدم ملون
- أزرار تجديد وخروج
- إخفاء/إظهار تلقائي

## 3. SessionWarningDialog Component:
📄 **الملف:** `client/src/components/SessionWarningDialog.jsx`
🎯 **الوظيفة:** نافذة تحذير قبل انتهاء الجلسة

**المميزات:**
- عد تنازلي للوقت المتبقي
- خيار تمديد الجلسة
- خيار تسجيل الخروج الفوري
- تصميم جذاب ومفهوم

========================================
⚙️ كيفية عمل النظام:
========================================

## 1. بدء المراقبة:
- يبدأ النظام عند تسجيل الدخول
- يتم تعيين مؤقت 5 دقائق
- يتم مراقبة أحداث النشاط

## 2. مراقبة النشاط:
**الأحداث المراقبة:**
- mousedown (ضغط الماوس)
- mousemove (حركة الماوس)
- keypress (ضغط المفاتيح)
- scroll (التمرير)
- touchstart (اللمس)
- click (النقر)
- keydown (ضغط المفاتيح)

## 3. إعادة التعيين:
- عند اكتشاف أي نشاط
- يتم إعادة تعيين المؤقت إلى 5 دقائق
- يتم إخفاء التحذيرات

## 4. التحذير:
- يظهر بعد 4 دقائق من عدم النشاط
- نافذة تحذير مع عد تنازلي
- خيارات للتمديد أو الخروج

## 5. انتهاء الجلسة:
- بعد 5 دقائق من عدم النشاط
- مسح التوكن من localStorage
- مسح بيانات المستخدم
- توجيه لصفحة تسجيل الدخول
- عرض رسالة توضيحية

========================================
🎨 واجهة المستخدم:
========================================

## 1. مؤشر الحالة:
- يظهر في أعلى يسار الشاشة
- يعرض الوقت المتبقي
- شريط تقدم ملون:
  * أخضر: أكثر من دقيقتين
  * برتقالي: دقيقة إلى دقيقتين
  * أحمر: أقل من دقيقة

## 2. نافذة التحذير:
- تظهر قبل دقيقة واحدة
- عد تنازلي للثواني المتبقية
- زر "متابعة الجلسة"
- زر "تسجيل الخروج"

## 3. الرسائل:
- تحذير في أعلى الشاشة
- رسالة عند انتهاء الجلسة
- إشعارات toast للتنبيه

========================================
🔒 الأمان:
========================================

## 1. حماية البيانات:
✅ مسح التوكن تلقائياً
✅ مسح بيانات المستخدم
✅ منع الوصول غير المصرح

## 2. التحقق المستمر:
✅ فحص صحة التوكن
✅ مراقبة النشاط المستمرة
✅ إعادة تعيين عند النشاط

## 3. الخروج الآمن:
✅ مسح جميع البيانات المحفوظة
✅ توجيه آمن لصفحة الدخول
✅ منع العودة بزر الرجوع

========================================
🧪 الاختبار:
========================================

## ملف الاختبار:
📄 **اختبار-انتهاء-الجلسة.html**

**المميزات:**
- محاكاة كاملة للنظام
- عرض مرئي للمؤقت
- أزرار للتحكم والاختبار
- سجل الأنشطة
- معلومات تقنية

**كيفية الاختبار:**
1. افتح ملف الاختبار
2. راقب العد التنازلي
3. اختبر محاكاة النشاط
4. راقب التحذيرات
5. اختبر انتهاء الجلسة

========================================
⚙️ الإعدادات:
========================================

## المتغيرات القابلة للتخصيص:

```javascript
const SESSION_TIMEOUT = 5 * 60 * 1000; // 5 دقائق
const WARNING_TIME = 1 * 60 * 1000;    // دقيقة تحذير
```

## تخصيص المدة:
- لتغيير مدة الجلسة: عدل `SESSION_TIMEOUT`
- لتغيير وقت التحذير: عدل `WARNING_TIME`
- لإضافة أحداث جديدة: عدل مصفوفة `events`

========================================
🔧 التكامل:
========================================

## في App.jsx:
```javascript
import { useSessionTimeout } from './hooks/useSessionTimeout';
import SessionStatus from './components/SessionStatus';

// في المكون الرئيسي
useSessionTimeout();

// في JSX
<SessionStatus />
```

## في أي مكون:
```javascript
const { resetTimer, logout, getRemainingTime } = useSessionTimeout();

// إعادة تعيين يدوي
resetTimer();

// خروج يدوي
logout();

// الحصول على الوقت المتبقي
const remaining = getRemainingTime();
```

========================================
🐛 استكشاف الأخطاء:
========================================

## مشاكل محتملة:

### 1. النظام لا يعمل:
- تحقق من استيراد useSessionTimeout
- تأكد من وجود التوكن في localStorage
- تحقق من console للأخطاء

### 2. التحذير لا يظهر:
- تحقق من استيراد SessionStatus
- تأكد من وضع المكون في المكان الصحيح
- تحقق من CSS والتداخل

### 3. النشاط لا يُكتشف:
- تحقق من مصفوفة الأحداث
- تأكد من عدم منع الأحداث
- تحقق من ربط مستمعي الأحداث

========================================
📊 الإحصائيات:
========================================

## الملفات المضافة:
- useSessionTimeout.js (135 سطر)
- SessionStatus.jsx (144 سطر)
- SessionWarningDialog.jsx (95 سطر)
- اختبار-انتهاء-الجلسة.html (300+ سطر)

## المميزات:
✅ مراقبة تلقائية للنشاط
✅ تحذير مسبق
✅ خروج آمن
✅ واجهة مستخدم جذابة
✅ قابلية التخصيص
✅ اختبار شامل

========================================
🎯 الخلاصة:
========================================

✅ **تم تطبيق نظام انتهاء الجلسة بنجاح**
✅ **مدة الجلسة: 5 دقائق عدم نشاط**
✅ **تحذير قبل دقيقة واحدة**
✅ **خروج تلقائي آمن**
✅ **واجهة مستخدم متقدمة**
✅ **قابلية اختبار وتخصيص**

🔒 **النظام الآن محمي ضد الوصول غير المصرح به!**

========================================
📞 للدعم الفني:
========================================

في حالة وجود مشاكل:
1. راجع ملف الاختبار
2. تحقق من console المتصفح
3. تأكد من صحة الاستيراد
4. اختبر في متصفح مختلف
5. اتصل بالدعم الفني

🚀 **النظام جاهز للاستخدام مع حماية متقدمة!**
