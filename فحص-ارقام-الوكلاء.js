const { PrismaClient } = require('./server/node_modules/@prisma/client')

const prisma = new PrismaClient()

async function checkAgentNumbers() {
  try {
    console.log('🔍 فحص أرقام الوكلاء في قاعدة البيانات...')
    console.log('=' .repeat(60))
    
    // جلب جميع الوكلاء مع ترتيب حسب ID
    const agents = await prisma.agent.findMany({
      orderBy: { id: 'asc' },
      select: {
        id: true,
        agentName: true,
        agencyName: true,
        agencyType: true,
        loginName: true,
        isActive: true,
        createdAt: true
      }
    })

    console.log(`📊 إجمالي الوكلاء في قاعدة البيانات: ${agents.length}`)
    console.log('')

    agents.forEach((agent, index) => {
      console.log(`الوكيل رقم ${index + 1}:`)
      console.log(`  🆔 رقم المعرف (ID): ${agent.id}`)
      console.log(`  👤 اسم الوكيل: ${agent.agentName}`)
      console.log(`  🏢 اسم الوكالة: ${agent.agencyName || 'غير محدد'}`)
      console.log(`  📋 نوع الوكالة: ${agent.agencyType}`)
      console.log(`  🔑 اسم تسجيل الدخول: ${agent.loginName || 'غير محدد'}`)
      console.log(`  ✅ الحالة: ${agent.isActive ? 'نشط' : 'غير نشط'}`)
      console.log(`  📅 تاريخ الإنشاء: ${agent.createdAt.toLocaleString('ar-SA')}`)
      console.log('')
    })

    // فحص الفجوات في الأرقام
    console.log('🔍 فحص التسلسل في أرقام المعرفات:')
    console.log('=' .repeat(40))
    
    const ids = agents.map(agent => agent.id).sort((a, b) => a - b)
    const missingIds = []
    
    for (let i = 1; i <= Math.max(...ids); i++) {
      if (!ids.includes(i)) {
        missingIds.push(i)
      }
    }

    if (missingIds.length > 0) {
      console.log(`❌ أرقام معرفات مفقودة: ${missingIds.join(', ')}`)
      console.log(`📊 عدد المعرفات المفقودة: ${missingIds.length}`)
      console.log('')
      console.log('💡 هذا يعني أنه تم حذف وكلاء أو حدثت أخطاء في الإدراج')
    } else {
      console.log('✅ جميع أرقام المعرفات متسلسلة بدون فجوات')
    }

    console.log('')
    console.log('📈 تحليل الأرقام:')
    console.log(`  🔢 أصغر رقم معرف: ${Math.min(...ids)}`)
    console.log(`  🔢 أكبر رقم معرف: ${Math.max(...ids)}`)
    console.log(`  📊 المدى: ${Math.max(...ids) - Math.min(...ids) + 1}`)
    console.log(`  📊 العدد الفعلي: ${ids.length}`)

  } catch (error) {
    console.error('❌ خطأ في فحص الوكلاء:', error)
  }
}

async function checkDataRecords() {
  try {
    console.log('')
    console.log('🔍 فحص سجل العمليات (data_records)...')
    console.log('=' .repeat(60))
    
    // جلب آخر 20 عملية
    const records = await prisma.dataRecord.findMany({
      orderBy: { id: 'desc' },
      take: 20,
      include: {
        agent: {
          select: {
            agentName: true
          }
        }
      }
    })

    console.log(`📊 آخر ${records.length} عملية:`)
    console.log('')

    records.reverse().forEach((record, index) => {
      console.log(`العملية ${records.length - index}:`)
      console.log(`  🆔 رقم المعرف: ${record.id}`)
      console.log(`  🤝 الوكيل: ${record.agent?.agentName || 'غير محدد'} (ID: ${record.agentId})`)
      console.log(`  👤 رمز العميل: ${record.clientCode}`)
      console.log(`  📊 حالة العملية: ${record.operationStatus === 1 ? 'ناجحة ✅' : 'فاشلة ❌'}`)
      console.log(`  📅 تاريخ العملية: ${record.operationDate.toLocaleString('ar-SA')}`)
      console.log(`  🔢 رقم مرجع الوكيل: ${record.agentReference}`)
      console.log('')
    })

    // فحص الفجوات في أرقام العمليات
    const allRecords = await prisma.dataRecord.findMany({
      select: { id: true },
      orderBy: { id: 'asc' }
    })

    const recordIds = allRecords.map(r => r.id).sort((a, b) => a - b)
    const missingRecordIds = []
    
    for (let i = 1; i <= Math.max(...recordIds); i++) {
      if (!recordIds.includes(i)) {
        missingRecordIds.push(i)
      }
    }

    console.log('🔍 تحليل تسلسل العمليات:')
    console.log('=' .repeat(40))
    
    if (missingRecordIds.length > 0) {
      console.log(`❌ أرقام عمليات مفقودة: ${missingRecordIds.slice(0, 10).join(', ')}${missingRecordIds.length > 10 ? '...' : ''}`)
      console.log(`📊 عدد العمليات المفقودة: ${missingRecordIds.length}`)
      console.log('')
      console.log('💡 هذا يعني أنه تم حذف عمليات أو حدثت أخطاء في التسجيل')
    } else {
      console.log('✅ جميع أرقام العمليات متسلسلة بدون فجوات')
    }

    console.log('')
    console.log('📈 إحصائيات العمليات:')
    console.log(`  📊 إجمالي العمليات: ${recordIds.length}`)
    console.log(`  🔢 أصغر رقم: ${Math.min(...recordIds)}`)
    console.log(`  🔢 أكبر رقم: ${Math.max(...recordIds)}`)

  } catch (error) {
    console.error('❌ خطأ في فحص العمليات:', error)
  }
}

async function checkLoginAttempts() {
  try {
    console.log('')
    console.log('🔍 فحص محاولات تسجيل الدخول...')
    console.log('=' .repeat(60))
    
    const attempts = await prisma.loginAttempt.findMany({
      orderBy: { id: 'desc' },
      take: 10,
      include: {
        agent: {
          select: {
            agentName: true
          }
        }
      }
    })

    console.log(`📊 آخر ${attempts.length} محاولة تسجيل دخول:`)
    console.log('')

    attempts.reverse().forEach((attempt, index) => {
      console.log(`المحاولة ${attempts.length - index}:`)
      console.log(`  🆔 رقم المعرف: ${attempt.id}`)
      console.log(`  👤 نوع المستخدم: ${attempt.userType}`)
      console.log(`  🤝 الوكيل: ${attempt.agent?.agentName || 'غير محدد'} (ID: ${attempt.agentId || 'N/A'})`)
      console.log(`  📊 النتيجة: ${attempt.success ? 'ناجحة ✅' : 'فاشلة ❌'}`)
      console.log(`  🌐 عنوان IP: ${attempt.ipAddress}`)
      console.log(`  📅 التاريخ: ${attempt.createdAt.toLocaleString('ar-SA')}`)
      console.log('')
    })

  } catch (error) {
    console.error('❌ خطأ في فحص محاولات تسجيل الدخول:', error)
  }
}

async function main() {
  console.log('🚀 بدء فحص شامل لأرقام المعرفات والعمليات...')
  console.log('=' .repeat(80))
  
  await checkAgentNumbers()
  await checkDataRecords()
  await checkLoginAttempts()
  
  console.log('')
  console.log('🎯 الخلاصة:')
  console.log('=' .repeat(40))
  console.log('1. رقم الوكيل = رقم المعرف (ID) في قاعدة البيانات')
  console.log('2. الأرقام تتغير حسب ترتيب الإدراج في قاعدة البيانات')
  console.log('3. إذا كانت هناك فجوات، فهذا يعني حذف سجلات أو أخطاء')
  console.log('4. رقم مرجع الوكيل (agentReference) عشوائي لكل عملية')
  console.log('')
  
  await prisma.$disconnect()
}

main().catch(console.error)
