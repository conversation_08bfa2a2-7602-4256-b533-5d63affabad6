/**
 * إصلاح كلمات مرور العملاء
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function fixClientPasswords() {
  console.log('🔧 إصلاح كلمات مرور العملاء...\n');

  try {
    // تحديث كلمة مرور العميل 1000
    console.log('1️⃣ تحديث كلمة مرور العميل 1000:');
    const hashedPassword1000 = await bcrypt.hash('112223333', 10);
    
    await prisma.client.update({
      where: { clientCode: 1000 },
      data: { password: hashedPassword1000 }
    });
    
    console.log('   ✅ تم تحديث كلمة مرور العميل 1000 (112223333)');

    // تحديث كلمة مرور العميل 1001
    console.log('2️⃣ تحديث كلمة مرور العميل 1001:');
    const hashedPassword1001 = await bcrypt.hash('Hash2020@', 10);
    
    await prisma.client.update({
      where: { clientCode: 1001 },
      data: { password: hashedPassword1001 }
    });
    
    console.log('   ✅ تم تحديث كلمة مرور العميل 1001 (Hash2020@)');

    // تحديث كلمة مرور العميل 1002
    console.log('3️⃣ تحديث كلمة مرور العميل 1002:');
    const hashedPassword1002 = await bcrypt.hash('password123', 10);
    
    await prisma.client.update({
      where: { clientCode: 1002 },
      data: { password: hashedPassword1002 }
    });
    
    console.log('   ✅ تم تحديث كلمة مرور العميل 1002 (password123)');

    // تحديث كلمة مرور العميل 1003
    console.log('4️⃣ تحديث كلمة مرور العميل 1003:');
    const hashedPassword1003 = await bcrypt.hash('test123', 10);
    
    await prisma.client.update({
      where: { clientCode: 1003 },
      data: { password: hashedPassword1003 }
    });
    
    console.log('   ✅ تم تحديث كلمة مرور العميل 1003 (test123)');

    // تحديث كلمة مرور العميل 1004
    console.log('5️⃣ تحديث كلمة مرور العميل 1004:');
    const hashedPassword1004 = await bcrypt.hash('demo123', 10);
    
    await prisma.client.update({
      where: { clientCode: 1004 },
      data: { password: hashedPassword1004 }
    });
    
    console.log('   ✅ تم تحديث كلمة مرور العميل 1004 (demo123)');

    // تحديث كلمة مرور العميل 1005
    console.log('6️⃣ تحديث كلمة مرور العميل 1005:');
    const hashedPassword1005 = await bcrypt.hash('client123', 10);
    
    await prisma.client.update({
      where: { clientCode: 1005 },
      data: { password: hashedPassword1005 }
    });
    
    console.log('   ✅ تم تحديث كلمة مرور العميل 1005 (client123)');

    console.log('\n🧪 اختبار كلمات المرور المحدثة:');
    
    // اختبار العميل 1000
    const client1000 = await prisma.client.findFirst({
      where: { clientCode: 1000 }
    });
    
    if (client1000) {
      const isValid1000 = await bcrypt.compare('112223333', client1000.password);
      console.log(`   🔐 العميل 1000 - كلمة المرور "112223333": ${isValid1000 ? '✅ صحيحة' : '❌ خاطئة'}`);
    }

    // اختبار العميل 1001
    const client1001 = await prisma.client.findFirst({
      where: { clientCode: 1001 }
    });
    
    if (client1001) {
      const isValid1001 = await bcrypt.compare('Hash2020@', client1001.password);
      console.log(`   🔐 العميل 1001 - كلمة المرور "Hash2020@": ${isValid1001 ? '✅ صحيحة' : '❌ خاطئة'}`);
    }

    console.log('\n✅ تم إصلاح جميع كلمات مرور العملاء بنجاح!');
    console.log('\n📋 كلمات المرور الجديدة:');
    console.log('   🏢 العميل 1000: 112223333');
    console.log('   🏢 العميل 1001: Hash2020@');
    console.log('   🏢 العميل 1002: password123');
    console.log('   🏢 العميل 1003: test123');
    console.log('   🏢 العميل 1004: demo123');
    console.log('   🏢 العميل 1005: client123');

  } catch (error) {
    console.error('❌ خطأ في إصلاح كلمات مرور العملاء:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixClientPasswords().catch(console.error);
