/**
 * اختبار شامل لمكتبات الربط
 * Yemen Client Management System - SDK Test
 */

// استيراد المكتبات
const YemenClientAgentAPI = require('./YemenClientAPI-Agent.js')
const YemenClientAPI = require('./YemenClientAPI-Client.js')

// إعدادات الاختبار
const SERVER_URL = 'http://localhost:8080'
const AGENT_CREDENTIALS = {
  loginName: 'agent001',
  password: 'agent123'
}
const CLIENT_CREDENTIALS = {
  code: '1000',
  token: 'ABC12345'
}

/**
 * اختبار مكتبة الوكيل
 */
async function testAgentSDK() {
  console.log('\n🤝 Testing Agent SDK...')
  console.log('=' .repeat(40))

  try {
    // إنشاء مثيل الوكيل
    const agent = new YemenClientAgentAPI(
      SERVER_URL,
      AGENT_CREDENTIALS.loginName,
      AGENT_CREDENTIALS.password
    )

    // 1. تسجيل الدخول
    console.log('\n1️⃣ Testing Agent Authentication...')
    const authResult = await agent.authenticate()
    console.log('✅ Agent authenticated:', authResult.agent_name)

    // 2. فحص حالة النظام
    console.log('\n2️⃣ Testing Health Check...')
    const health = await agent.checkHealth()
    console.log('✅ System health:', health.message)

    // 3. التحقق من عميل
    console.log('\n3️⃣ Testing Client Verification...')
    const clientResult = await agent.verifyClient(
      CLIENT_CREDENTIALS.code,
      CLIENT_CREDENTIALS.token
    )
    
    if (clientResult.error) {
      console.log('❌ Client verification failed:', clientResult.error)
    } else {
      console.log('✅ Client verified:', clientResult.client_name)
    }

    // 4. الحصول على الإحصائيات
    console.log('\n4️⃣ Testing Agent Statistics...')
    const stats = await agent.getStats()
    console.log('✅ Agent stats:', {
      totalOperations: stats.total_operations,
      successRate: stats.success_rate,
      uniqueClients: stats.unique_clients
    })

    // 5. الحصول على سجل العمليات
    console.log('\n5️⃣ Testing Operations Log...')
    const operations = await agent.getOperations(1, 3)
    console.log('✅ Operations retrieved:', operations.operations.length, 'records')

    // 6. تسجيل الخروج
    console.log('\n6️⃣ Testing Agent Logout...')
    const logoutResult = await agent.logout()
    console.log('✅ Agent logout:', logoutResult ? 'Success' : 'Failed')

    return true

  } catch (error) {
    console.error('❌ Agent SDK Test Failed:', error.message)
    return false
  }
}

/**
 * اختبار مكتبة العميل
 */
async function testClientSDK() {
  console.log('\n👤 Testing Client SDK...')
  console.log('=' .repeat(40))

  try {
    // إنشاء مثيل العميل
    const client = new YemenClientAPI(
      SERVER_URL,
      CLIENT_CREDENTIALS.code,
      CLIENT_CREDENTIALS.token
    )

    // 1. فحص حالة النظام
    console.log('\n1️⃣ Testing System Health Check...')
    const health = await client.checkSystemHealth()
    if (health.success) {
      console.log('✅ System health:', health.message)
    } else {
      console.log('❌ Health check failed:', health.error)
    }

    // 2. التحقق من صحة بيانات الاعتماد
    console.log('\n2️⃣ Testing Credentials Validation...')
    const isValid = client.validateCredentials()
    console.log('✅ Credentials valid:', isValid)

    // 3. التحقق من العميل عبر وكيل
    console.log('\n3️⃣ Testing Client Verification through Agent...')
    const verifyResult = await client.verifyThroughAgent(
      AGENT_CREDENTIALS.loginName,
      AGENT_CREDENTIALS.password
    )

    if (verifyResult.success) {
      console.log('✅ Client verified successfully!')
      console.log('   Client:', verifyResult.client.client_name)
      console.log('   Agent:', verifyResult.agent.name)
      console.log('   Status:', verifyResult.client.status === 1 ? 'Active' : 'Inactive')
    } else {
      console.log('❌ Client verification failed:', verifyResult.error)
    }

    // 4. اختبار بيانات خاطئة
    console.log('\n4️⃣ Testing Invalid Client Data...')
    const invalidClient = new YemenClientAPI(SERVER_URL, '9999', 'INVALID')
    const invalidResult = await invalidClient.verifyThroughAgent(
      AGENT_CREDENTIALS.loginName,
      AGENT_CREDENTIALS.password
    )

    if (!invalidResult.success) {
      console.log('✅ Invalid client correctly rejected:', invalidResult.errorCode)
    } else {
      console.log('❌ Invalid client should have been rejected')
    }

    return true

  } catch (error) {
    console.error('❌ Client SDK Test Failed:', error.message)
    return false
  }
}

/**
 * اختبار الأداء
 */
async function testPerformance() {
  console.log('\n⚡ Testing Performance...')
  console.log('=' .repeat(40))

  try {
    const agent = new YemenClientAgentAPI(
      SERVER_URL,
      AGENT_CREDENTIALS.loginName,
      AGENT_CREDENTIALS.password
    )

    // قياس وقت المصادقة
    const authStart = Date.now()
    await agent.authenticate()
    const authTime = Date.now() - authStart
    console.log(`✅ Authentication time: ${authTime}ms`)

    // قياس وقت التحقق من العميل
    const verifyStart = Date.now()
    await agent.verifyClient(CLIENT_CREDENTIALS.code, CLIENT_CREDENTIALS.token)
    const verifyTime = Date.now() - verifyStart
    console.log(`✅ Client verification time: ${verifyTime}ms`)

    // قياس وقت الإحصائيات
    const statsStart = Date.now()
    await agent.getStats()
    const statsTime = Date.now() - statsStart
    console.log(`✅ Stats retrieval time: ${statsTime}ms`)

    await agent.logout()

    // تقييم الأداء
    const avgTime = (authTime + verifyTime + statsTime) / 3
    console.log(`📊 Average response time: ${avgTime.toFixed(1)}ms`)
    
    if (avgTime < 200) {
      console.log('🚀 Performance: Excellent')
    } else if (avgTime < 500) {
      console.log('✅ Performance: Good')
    } else {
      console.log('⚠️ Performance: Needs improvement')
    }

    return true

  } catch (error) {
    console.error('❌ Performance Test Failed:', error.message)
    return false
  }
}

/**
 * تشغيل جميع الاختبارات
 */
async function runAllTests() {
  console.log('🚀 Starting Yemen Client Management SDK Tests...')
  console.log('=' .repeat(60))

  const tests = [
    { name: 'Agent SDK', func: testAgentSDK },
    { name: 'Client SDK', func: testClientSDK },
    { name: 'Performance', func: testPerformance }
  ]

  let passed = 0
  let failed = 0

  for (const test of tests) {
    try {
      const result = await test.func()
      if (result) {
        passed++
        console.log(`\n✅ ${test.name} Test: PASSED`)
      } else {
        failed++
        console.log(`\n❌ ${test.name} Test: FAILED`)
      }
    } catch (error) {
      failed++
      console.log(`\n💥 ${test.name} Test: CRASHED - ${error.message}`)
    }

    // انتظار قصير بين الاختبارات
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  console.log('\n' + '=' .repeat(60))
  console.log('📊 SDK Test Results:')
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`)

  if (failed === 0) {
    console.log('🎉 All SDK tests passed! Libraries are ready for distribution.')
  } else {
    console.log('⚠️ Some tests failed. Please review the SDK implementation.')
  }

  console.log('\n📦 SDK Files Ready:')
  console.log('   📄 YemenClientAPI-Agent.js - للوكلاء')
  console.log('   📄 YemenClientAPI-Client.js - للعملاء')
  console.log('   📄 Documentation files - الوثائق')
}

// تشغيل الاختبارات
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testAgentSDK,
  testClientSDK,
  testPerformance,
  runAllTests
}
