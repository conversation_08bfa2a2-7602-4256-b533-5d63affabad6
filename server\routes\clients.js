const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, checkPermission } = require('../middleware/auth');
const {
  hashPassword,
  validatePasswordStrength,
  generateUniqueClientToken,
  validateToken
} = require('../utils/encryption');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على جميع العملاء
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', status } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: 'insensitive' } },
        { appName: { contains: search, mode: 'insensitive' } },
        { cardNumber: { contains: search } },
        { clientCode: { equals: parseInt(search) || 0 } }
      ];
    }

    if (status) {
      where.status = parseInt(status);
    }

    // جلب العملاء مع معلومات المستخدمين والوكلاء
    const clientsRaw = await prisma.client.findMany({
      where,
      skip: parseInt(skip),
      take: parseInt(limit),
      orderBy: { createdAt: 'desc' }
    });

    // جلب معلومات المستخدمين منفصلة
    const userIds = [...new Set(clientsRaw.map(c => c.userId).filter(Boolean))];

    const [users, total] = await Promise.all([
      userIds.length > 0 ? prisma.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, username: true, loginName: true }
      }) : [],
      prisma.client.count({ where })
    ]);

    // ربط البيانات
    const clients = clientsRaw.map(client => {
      const user = users.find(u => u.id === client.userId) || null;
      return {
        ...client,
        userId: client.userId, // التأكد من إرجاع userId
        user: user,
        createdByUser: user ? user.username || user.loginName : null
      };
    });

    res.json({
      clients,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get clients error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على عميل واحد
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const client = await prisma.client.findUnique({
      where: { id: parseInt(req.params.id) }
    });

    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // جلب معلومات المستخدم منفصلة
    const user = client.userId ? await prisma.user.findUnique({
      where: { id: client.userId },
      select: { id: true, username: true, loginName: true }
    }) : null;

    res.json({
      ...client,
      user
    });
  } catch (error) {
    console.error('Get client error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// إضافة عميل جديد
router.post('/', [
  authenticateToken,
  body('clientName').notEmpty().withMessage('اسم العميل مطلوب'),
  body('appName').notEmpty().withMessage('اسم التطبيق مطلوب'),
  body('cardNumber').isLength({ min: 8, max: 11 }).withMessage('رقم البطاقة يجب أن يكون 8-11 رقم'),
  body('password').custom((value) => {
    const validation = validatePasswordStrength(value);
    if (!validation.isValid) {
      throw new Error(validation.errors.join(', '));
    }
    return true;
  }),
  body('ipAddress').isIP().withMessage('عنوان IP غير صحيح')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { clientName, appName, cardNumber, password, ipAddress, dataId } = req.body;

    // التحقق من عدم تكرار رقم البطاقة
    const existingClient = await prisma.client.findFirst({
      where: { cardNumber: cardNumber }
    });

    if (existingClient) {
      return res.status(400).json({ error: 'Card number already exists' });
    }

    // إنشاء رقم عميل تلقائي
    const lastClient = await prisma.client.findFirst({
      orderBy: { clientCode: 'desc' }
    });
    const nextClientCode = lastClient ? lastClient.clientCode + 1 : 1000;

    // الحصول على رقم المستخدم - من البيانات المرسلة أو من التوكن
    const currentUserId = req.body.userId || req.user.id || req.user.userId;

    console.log('🔍 إنشاء عميل جديد:');
    console.log('   - البيانات المرسلة:', req.body);
    console.log('   - userId من البيانات:', req.body.userId);
    console.log('   - userId من التوكن:', req.user.id);
    console.log('   - userId المختار:', currentUserId);

    // التأكد من وجود userId
    if (!currentUserId) {
      console.log('❌ لا يوجد userId');
      return res.status(400).json({ error: 'User ID not found' });
    }

    // تشفير كلمة المرور
    console.log('🔐 تشفير كلمة المرور...');
    const hashedPassword = await hashPassword(password);

    // توليد توكن فريد
    console.log('🎫 توليد توكن فريد...');
    const clientToken = await generateUniqueClientToken(prisma);

    console.log('✅ تم توليد التوكن:', clientToken);

    const client = await prisma.client.create({
      data: {
        clientName: clientName,
        appName: appName,
        cardNumber: cardNumber,
        password: hashedPassword, // كلمة مرور مشفرة
        token: clientToken, // توكن فريد
        ipAddress: ipAddress,
        clientCode: nextClientCode,
        dataId: dataId ? parseInt(dataId) : null,
        userId: currentUserId, // إضافة رقم المستخدم تلقائياً
        status: 1 // نشط افتراضياً (1=active, 2=blocked)
      }
    });

    console.log('✅ تم إنشاء العميل بنجاح:', {
      id: client.id,
      clientCode: client.clientCode,
      userId: client.userId,
      token: client.token,
      hasEncryptedPassword: !!client.password
    });

    res.status(201).json(client);
  } catch (error) {
    console.error('Create client error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// تحديث عميل
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const clientId = parseInt(req.params.id);
    let updateData = { ...req.body };

    // منع تحديث userId - يجب أن يبقى كما هو
    delete updateData.userId;
    delete updateData.user;
    delete updateData.createdAt;
    delete updateData.updatedAt;
    delete updateData.id;

    console.log('🔄 تحديث عميل:');
    console.log('   - clientId:', clientId);
    console.log('   - updateData (بعد التنظيف):', updateData);

    // التحقق من وجود العميل
    const existingClient = await prisma.client.findUnique({
      where: { id: clientId }
    });

    if (!existingClient) {
      console.log('❌ العميل غير موجود:', clientId);
      return res.status(404).json({ error: 'Client not found' });
    }

    console.log('✅ العميل موجود:', existingClient.clientName);

    // التحقق من عدم تكرار رقم البطاقة
    if (updateData.cardNumber && updateData.cardNumber !== existingClient.cardNumber) {
      const duplicateCard = await prisma.client.findFirst({
        where: {
          cardNumber: updateData.cardNumber,
          id: { not: clientId }
        }
      });

      if (duplicateCard) {
        return res.status(400).json({ error: 'Card number already exists' });
      }
    }
    console.log('🔄 تنفيذ التحديث...');
    const client = await prisma.client.update({
      where: { id: clientId },
      data: updateData
    });

    console.log('✅ تم تحديث العميل بنجاح:', {
      id: client.id,
      clientCode: client.clientCode,
      clientName: client.clientName
    });

    res.json(client);
  } catch (error) {
    console.error('❌ Update client error:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      code: error.code
    });
    res.status(500).json({
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// حذف عميل
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const clientId = parseInt(req.params.id);

    const client = await prisma.client.findUnique({
      where: { id: clientId },
      include: {
        _count: {
          select: { dataRecords: true }
        }
      }
    });

    if (!client) {
      return res.status(404).json({ error: 'Client not found' });
    }

    // التحقق من وجود سجلات بيانات مرتبطة
    if (client._count.dataRecords > 0) {
      return res.status(400).json({
        error: 'Cannot delete client with associated data records',
        dataRecordsCount: client._count.dataRecords
      });
    }

    await prisma.client.delete({
      where: { id: clientId }
    });

    res.json({ message: 'Client deleted successfully' });
  } catch (error) {
    console.error('Delete client error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
