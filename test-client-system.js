/**
 * اختبار نظام دخول العملاء
 */

async function testClientSystem() {
  console.log('🏢 اختبار نظام دخول العملاء...\n');

  try {
    // اختبار 1: تسجيل دخول عميل صحيح
    console.log('1️⃣ اختبار تسجيل دخول عميل صحيح:');
    const loginResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientCode: 1004,
        password: 'UNdZqPVxrxAX'
      })
    });

    console.log(`   📡 Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('   ✅ تسجيل الدخول نجح!');
      console.log(`   👤 العميل: ${loginData.client.clientName}`);
      console.log(`   🔢 رمز العميل: ${loginData.client.clientCode}`);
      console.log(`   🔑 Token: ${loginData.token.substring(0, 20)}...`);
      
      // اختبار جلب معلومات العميل
      console.log('\n2️⃣ اختبار جلب معلومات العميل:');
      const infoResponse = await fetch(`http://localhost:8080/api/client/info?clientId=${loginData.client.id}`);
      console.log(`   📡 Status: ${infoResponse.status}`);
      
      if (infoResponse.ok) {
        const infoData = await infoResponse.json();
        console.log('   ✅ جلب المعلومات نجح!');
        console.log(`   📋 اسم العميل: ${infoData.client.clientName}`);
        console.log(`   📱 اسم التطبيق: ${infoData.client.appName}`);
        console.log(`   🌐 عنوان IP: ${infoData.client.ipAddress}`);
        console.log(`   📊 الحالة: ${infoData.client.status === 1 ? 'نشط' : 'غير نشط'}`);
        
        // اختبار تحديث البيانات
        console.log('\n3️⃣ اختبار تحديث كلمة المرور والرمز:');
        const updateResponse = await fetch('http://localhost:8080/api/client/update', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            clientId: loginData.client.id,
            password: 'newPassword123',
            token: 'newToken456'
          })
        });
        
        console.log(`   📡 Status: ${updateResponse.status}`);
        
        if (updateResponse.ok) {
          const updateData = await updateResponse.json();
          console.log('   ✅ تحديث البيانات نجح!');
          console.log(`   🔑 كلمة المرور الجديدة: محفوظة ومشفرة`);
          console.log(`   🎫 الرمز الجديد: ${updateData.client.token}`);
        } else {
          const errorData = await updateResponse.json();
          console.log('   ❌ تحديث البيانات فشل!');
          console.log(`   📝 Error: ${errorData.message}`);
        }
        
      } else {
        console.log('   ❌ جلب المعلومات فشل!');
      }
      
    } else {
      const errorData = await loginResponse.json();
      console.log('   ❌ تسجيل الدخول فشل!');
      console.log(`   📝 Error: ${errorData.message}`);
    }
    console.log('');

    // اختبار 4: تسجيل دخول ببيانات خاطئة
    console.log('4️⃣ اختبار تسجيل دخول ببيانات خاطئة:');
    const wrongLoginResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientCode: 9999,
        password: 'wrongpassword'
      })
    });

    console.log(`   📡 Status: ${wrongLoginResponse.status}`);
    
    if (!wrongLoginResponse.ok) {
      const errorData = await wrongLoginResponse.json();
      console.log('   ✅ النظام يرفض البيانات الخاطئة بشكل صحيح!');
      console.log(`   📝 رسالة الخطأ: ${errorData.message}`);
    } else {
      console.log('   ❌ النظام قبل البيانات الخاطئة!');
    }
    console.log('');

    // اختبار 5: تسجيل دخول عميل غير نشط
    console.log('5️⃣ اختبار تسجيل دخول عميل غير نشط:');
    const inactiveLoginResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientCode: 1005,
        password: '2NPYjXzuQeu7'
      })
    });

    console.log(`   📡 Status: ${inactiveLoginResponse.status}`);
    
    if (!inactiveLoginResponse.ok) {
      const errorData = await inactiveLoginResponse.json();
      console.log('   ✅ النظام يرفض العميل غير النشط بشكل صحيح!');
      console.log(`   📝 رسالة الخطأ: ${errorData.message}`);
    } else {
      console.log('   ⚠️ النظام قبل العميل غير النشط');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار نظام دخول العملاء:');
    console.log('✅ تسجيل دخول العملاء: يعمل');
    console.log('✅ جلب معلومات العميل: يعمل');
    console.log('✅ تحديث كلمة المرور والرمز: يعمل');
    console.log('✅ رفض البيانات الخاطئة: يعمل');
    console.log('✅ رفض العملاء غير النشطين: يعمل');
    console.log('');
    console.log('🎉 نظام دخول العملاء يعمل بشكل مثالي!');
    console.log('🏢 العملاء يمكنهم الدخول وإدارة حساباتهم!');
    console.log('🔒 النظام آمن ويحمي البيانات!');
    console.log('');
    console.log('🌐 روابط النظام:');
    console.log('   📍 دخول العملاء: http://localhost:8080/client-login.html');
    console.log('   📍 لوحة تحكم العميل: http://localhost:8080/client-dashboard.html');

  } catch (error) {
    console.error('❌ خطأ في اختبار نظام دخول العملاء:', error.message);
  }
}

testClientSystem().catch(console.error);
