@echo off
echo ========================================
echo    Yemen Client Management System
echo    Creating Distribution Package
echo ========================================

:: إنشاء مجلد التوزيع
if exist "Yemen-Client-API-SDK" rmdir /s /q "Yemen-Client-API-SDK"
mkdir "Yemen-Client-API-SDK"

:: نسخ ملفات المكتبات
echo Copying SDK libraries...
copy "YemenClientAPI-Agent.js" "Yemen-Client-API-SDK\"
copy "YemenClientAPI-Client.js" "Yemen-Client-API-SDK\"

:: نسخ الوثائق
echo Copying documentation...
copy "دليل-المطور-API.md" "Yemen-Client-API-SDK\"
copy "API-Documentation-EN.md" "Yemen-Client-API-SDK\"
copy "API-System-Design.md" "Yemen-Client-API-SDK\"
copy "README-SDK.md" "Yemen-Client-API-SDK\"

:: نسخ ملفات الاختبار
echo Copying test files...
copy "test-sdk.js" "Yemen-Client-API-SDK\"

:: إنشاء ملف package.json
echo Creating package.json...
echo {> "Yemen-Client-API-SDK\package.json"
echo   "name": "yemen-client-api-sdk",>> "Yemen-Client-API-SDK\package.json"
echo   "version": "1.0.0",>> "Yemen-Client-API-SDK\package.json"
echo   "description": "Yemen Client Management System - SDK Libraries",>> "Yemen-Client-API-SDK\package.json"
echo   "main": "YemenClientAPI-Agent.js",>> "Yemen-Client-API-SDK\package.json"
echo   "scripts": {>> "Yemen-Client-API-SDK\package.json"
echo     "test": "node test-sdk.js">> "Yemen-Client-API-SDK\package.json"
echo   },>> "Yemen-Client-API-SDK\package.json"
echo   "dependencies": {>> "Yemen-Client-API-SDK\package.json"
echo     "node-fetch": "^2.6.7">> "Yemen-Client-API-SDK\package.json"
echo   },>> "Yemen-Client-API-SDK\package.json"
echo   "keywords": ["yemen", "client", "management", "api", "sdk"],>> "Yemen-Client-API-SDK\package.json"
echo   "author": "Yemen Client Management Team",>> "Yemen-Client-API-SDK\package.json"
echo   "license": "MIT">> "Yemen-Client-API-SDK\package.json"
echo }>> "Yemen-Client-API-SDK\package.json"

:: إنشاء ملف LICENSE
echo Creating LICENSE...
echo MIT License> "Yemen-Client-API-SDK\LICENSE"
echo.>> "Yemen-Client-API-SDK\LICENSE"
echo Copyright (c) 2025 Yemen Client Management System>> "Yemen-Client-API-SDK\LICENSE"
echo.>> "Yemen-Client-API-SDK\LICENSE"
echo Permission is hereby granted, free of charge, to any person obtaining a copy>> "Yemen-Client-API-SDK\LICENSE"
echo of this software and associated documentation files (the "Software"), to deal>> "Yemen-Client-API-SDK\LICENSE"
echo in the Software without restriction, including without limitation the rights>> "Yemen-Client-API-SDK\LICENSE"
echo to use, copy, modify, merge, publish, distribute, sublicense, and/or sell>> "Yemen-Client-API-SDK\LICENSE"
echo copies of the Software, and to permit persons to whom the Software is>> "Yemen-Client-API-SDK\LICENSE"
echo furnished to do so, subject to the following conditions:>> "Yemen-Client-API-SDK\LICENSE"
echo.>> "Yemen-Client-API-SDK\LICENSE"
echo The above copyright notice and this permission notice shall be included in all>> "Yemen-Client-API-SDK\LICENSE"
echo copies or substantial portions of the Software.>> "Yemen-Client-API-SDK\LICENSE"

:: إنشاء ملف أمثلة
echo Creating examples...
mkdir "Yemen-Client-API-SDK\examples"

echo // مثال للوكلاء> "Yemen-Client-API-SDK\examples\agent-example.js"
echo const YemenClientAgentAPI = require('../YemenClientAPI-Agent.js')>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo.>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo async function main() {>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo   const agent = new YemenClientAgentAPI(>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     'http://***********:8080',>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     'your_agent_login',>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     'your_agent_password'>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo   )>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo.>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo   try {>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     await agent.authenticate()>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     const result = await agent.verifyClient('1000', 'ABC12345')>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     console.log('Result:', result)>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     await agent.logout()>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo   } catch (error) {>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo     console.error('Error:', error.message)>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo   }>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo }>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo.>> "Yemen-Client-API-SDK\examples\agent-example.js"
echo main()>> "Yemen-Client-API-SDK\examples\agent-example.js"

echo // مثال للعملاء> "Yemen-Client-API-SDK\examples\client-example.js"
echo const YemenClientAPI = require('../YemenClientAPI-Client.js')>> "Yemen-Client-API-SDK\examples\client-example.js"
echo.>> "Yemen-Client-API-SDK\examples\client-example.js"
echo async function main() {>> "Yemen-Client-API-SDK\examples\client-example.js"
echo   const client = new YemenClientAPI(>> "Yemen-Client-API-SDK\examples\client-example.js"
echo     'http://***********:8080',>> "Yemen-Client-API-SDK\examples\client-example.js"
echo     'your_client_code',>> "Yemen-Client-API-SDK\examples\client-example.js"
echo     'your_client_token'>> "Yemen-Client-API-SDK\examples\client-example.js"
echo   )>> "Yemen-Client-API-SDK\examples\client-example.js"
echo.>> "Yemen-Client-API-SDK\examples\client-example.js"
echo   try {>> "Yemen-Client-API-SDK\examples\client-example.js"
echo     const result = await client.verifyThroughAgent('agent_login', 'agent_password')>> "Yemen-Client-API-SDK\examples\client-example.js"
echo     console.log('Result:', result)>> "Yemen-Client-API-SDK\examples\client-example.js"
echo   } catch (error) {>> "Yemen-Client-API-SDK\examples\client-example.js"
echo     console.error('Error:', error.message)>> "Yemen-Client-API-SDK\examples\client-example.js"
echo   }>> "Yemen-Client-API-SDK\examples\client-example.js"
echo }>> "Yemen-Client-API-SDK\examples\client-example.js"
echo.>> "Yemen-Client-API-SDK\examples\client-example.js"
echo main()>> "Yemen-Client-API-SDK\examples\client-example.js"

:: إنشاء ملف HTML للمتصفح
echo Creating browser example...
echo ^<!DOCTYPE html^>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo ^<html^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo ^<head^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo     ^<title^>Yemen Client API - Browser Example^</title^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo ^</head^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo ^<body^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo     ^<h1^>Yemen Client Management System^</h1^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo     ^<script src="../YemenClientAPI-Agent.js"^>^</script^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo     ^<script^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo         async function testAPI() {>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo             const agent = new YemenClientAgentAPI(>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo                 'http://***********:8080',>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo                 'agent001',>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo                 'agent123'>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo             )>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo             try {>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo                 await agent.authenticate()>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo                 console.log('Agent authenticated!')>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo             } catch (error) {>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo                 console.error('Error:', error)>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo             }>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo         }>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo         testAPI()>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo     ^</script^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo ^</body^>>> "Yemen-Client-API-SDK\examples\browser-example.html"
echo ^</html^>>> "Yemen-Client-API-SDK\examples\browser-example.html"

:: عرض النتيجة
echo.
echo ========================================
echo   Distribution Package Created!
echo ========================================
echo.
echo Package location: Yemen-Client-API-SDK\
echo.
echo Files included:
echo   📄 YemenClientAPI-Agent.js
echo   📄 YemenClientAPI-Client.js  
echo   📄 دليل-المطور-API.md
echo   📄 API-Documentation-EN.md
echo   📄 README-SDK.md
echo   📄 test-sdk.js
echo   📄 package.json
echo   📄 LICENSE
echo   📁 examples\
echo.
echo Ready for distribution!
echo.
pause
