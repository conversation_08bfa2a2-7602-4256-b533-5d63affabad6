const bcrypt = require('bcryptjs');

async function createHashedPassword() {
    const password = 'Hash8080';
    const saltRounds = 12;
    
    try {
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        console.log('كلمة المرور الأصلية:', password);
        console.log('كلمة المرور المشفرة:', hashedPassword);
        
        // SQL لإدخال المستخدم
        const sql = `
INSERT INTO users (username, device_id, login_name, password, permissions, is_active, created_at, updated_at) 
VALUES (
    'محمد الحاشدي',
    NULL,
    'hash8080',
    '${hashedPassword}',
    '{"isAdmin": true, "clients": {"create": true, "read": true, "update": true, "delete": true}, "agents": {"create": true, "read": true, "update": true, "delete": true}, "users": {"create": true, "read": true, "update": true, "delete": true}, "dashboard": {"read": true}, "security": {"read": true, "manage": true}}',
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
);`;
        
        console.log('\n--- SQL لإدخال المستخدم ---');
        console.log(sql);
        
        console.log('\n--- معلومات المستخدم ---');
        console.log('الاسم: محمد الحاشدي');
        console.log('اسم المستخدم: hash8080');
        console.log('كلمة المرور: Hash8080');
        console.log('الصلاحيات: أدمن كامل الصلاحيات');
        console.log('جهاز المستخدم: فارغ (NULL)');
        
    } catch (error) {
        console.error('خطأ في تشفير كلمة المرور:', error);
    }
}

createHashedPassword();
