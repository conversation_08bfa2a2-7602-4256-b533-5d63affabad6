import React, { useEffect } from 'react'
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Divider,
  Switch,
  FormControlLabel
} from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useMutation } from 'react-query'
import { useAuth } from '../../contexts/AuthContext'
import { useSnackbar } from 'notistack'

const schema = yup.object({
  agentName: yup.string().required('اسم الوكيل مطلوب'),
  agencyName: yup.string().required('اسم الوكالة مطلوب'),
  agencyType: yup.string().required('نوع الوكالة مطلوب'),
  ipAddress: yup.string()
    .required('عنوان IP مطلوب')
    .matches(
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      'عنوان IP غير صحيح'
    ),
  loginName: yup.string()
    .nullable()
    .min(8, 'اسم الدخول يجب أن يكون 8 أحرف على الأقل')
    .matches(/^[a-zA-Z0-9]+$/, 'اسم الدخول يجب أن يحتوي على حروف وأرقام فقط'),
  loginPassword: yup.string()
    .nullable()
    .min(8, 'كلمة مرور الدخول يجب أن تكون 8 أحرف على الأقل')
    .matches(/^[a-zA-Z0-9]+$/, 'كلمة مرور الدخول يجب أن تحتوي على حروف وأرقام فقط'),
  isActive: yup.boolean()
})

const AgentForm = ({ agent, onSuccess, readOnly = false }) => {
  const { api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      agentName: '',
      agencyName: '',
      agencyType: '',
      ipAddress: '',
      loginName: '',
      loginPassword: '',
      isActive: true
    }
  })

  // إضافة/تحديث وكيل
  const mutation = useMutation(
    (data) => {
      if (agent) {
        return api.put(`/api/agents/${agent.id}`, data)
      } else {
        return api.post('/api/agents', data)
      }
    },
    {
      onSuccess: () => {
        enqueueSnackbar(
          agent ? 'تم تحديث الوكيل بنجاح' : 'تم إضافة الوكيل بنجاح',
          { variant: 'success' }
        )
        onSuccess()
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.error || 'حدث خطأ في حفظ البيانات',
          { variant: 'error' }
        )
      }
    }
  )

  // تعبئة النموذج عند التعديل
  useEffect(() => {
    if (agent) {
      reset({
        agentName: agent.agentName || '',
        agencyName: agent.agencyName || '',
        agencyType: agent.agencyType || '',
        ipAddress: agent.ipAddress || '',
        loginName: agent.loginName || '',
        loginPassword: '', // لا نعرض كلمة المرور المشفرة
        deviceId: agent.deviceId || '',
        isActive: agent.isActive !== undefined ? agent.isActive : true
      })
    }
  }, [agent, reset])

  const onSubmit = (data) => {
    mutation.mutate(data)
  }

  const agencyTypes = [
    'وكيل يمن موبايل',
    'وكيل سبافون',
    'وكيل يو',
    'وكيل واي',
    'وكيل خدمات البريد',
    'وكيل العاب وبطائق',
    'اخرى'
  ]

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            معلومات الوكيل الأساسية
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="agentName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="اسم الوكيل"
                error={!!errors.agentName}
                helperText={errors.agentName?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="agencyName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="اسم الوكالة"
                error={!!errors.agencyName}
                helperText={errors.agencyName?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="agencyType"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.agencyType}>
                <InputLabel>نوع الوكالة</InputLabel>
                <Select
                  {...field}
                  label="نوع الوكالة"
                  disabled={readOnly}
                >
                  {agencyTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type}
                    </MenuItem>
                  ))}
                </Select>
                {errors.agencyType && (
                  <Typography variant="caption" color="error" sx={{ mt: 1, ml: 2 }}>
                    {errors.agencyType.message}
                  </Typography>
                )}
              </FormControl>
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="ipAddress"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="عنوان IP"
                placeholder="***********"
                error={!!errors.ipAddress}
                helperText={errors.ipAddress?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        {/* اسم الدخول */}
        <Grid item xs={12} md={6}>
          <Controller
            name="loginName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="اسم الدخول (8+ أحرف وأرقام)"
                placeholder="username123"
                error={!!errors.loginName}
                helperText={errors.loginName?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        {/* كلمة مرور الدخول */}
        <Grid item xs={12} md={6}>
          <Controller
            name="loginPassword"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="كلمة مرور الدخول (8+ أحرف وأرقام)"
                type="password"
                placeholder="password123"
                error={!!errors.loginPassword}
                helperText={errors.loginPassword?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="isActive"
            control={control}
            render={({ field }) => (
              <FormControlLabel
                control={
                  <Switch
                    {...field}
                    checked={field.value}
                    disabled={readOnly}
                  />
                }
                label="الوكيل نشط"
              />
            )}
          />
        </Grid>

        {!readOnly && (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                disabled={mutation.isLoading}
              >
                {mutation.isLoading ? 'جاري الحفظ...' : (agent ? 'تحديث' : 'إضافة')}
              </Button>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default AgentForm
