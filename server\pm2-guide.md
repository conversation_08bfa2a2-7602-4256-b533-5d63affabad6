# 🚀 دليل PM2 للخادم المحسن

## 📋 نظرة عامة

PM2 هو مدير عمليات متقدم لتطبيقات Node.js يوفر:
- **إعادة التشغيل التلقائي** عند الأخطاء
- **مراقبة الأداء** في الوقت الفعلي
- **إدارة اللوقات** المتقدمة
- **التحميل بدون انقطاع** (Zero-downtime reload)
- **التشغيل في الخلفية** (Daemon mode)

## 🛠️ التثبيت والإعداد

### 1. تثبيت PM2
```bash
# تثبيت PM2 عالمياً
npm install -g pm2

# أو باستخدام yarn
yarn global add pm2
```

### 2. التحقق من التثبيت
```bash
pm2 --version
```

## 🚀 تشغيل الخادم

### الطرق المختلفة للتشغيل

#### 1. باستخدام ملف التكوين (الأفضل)
```bash
# بدء الخادم
pm2 start ecosystem.config.js

# أو باستخدام npm script
npm run pm2:start
```

#### 2. التشغيل المباشر
```bash
# تشغيل مباشر
pm2 start production-server.js --name "yemclient-server"

# مع متغيرات البيئة
pm2 start production-server.js --name "yemclient-server" --env production
```

#### 3. مع خيارات متقدمة
```bash
pm2 start production-server.js \
  --name "yemclient-server" \
  --instances 1 \
  --max-memory-restart 1G \
  --log-date-format "YYYY-MM-DD HH:mm:ss Z" \
  --error-file logs/pm2-error.log \
  --out-file logs/pm2-out.log
```

## 📊 مراقبة وإدارة العمليات

### عرض حالة العمليات
```bash
# عرض جميع العمليات
pm2 status

# أو
pm2 list

# عرض تفاصيل عملية معينة
pm2 show yemclient-server
```

### مراقبة الأداء في الوقت الفعلي
```bash
# مراقبة شاملة
pm2 monit

# مراقبة عملية معينة
pm2 monit yemclient-server
```

### عرض اللوقات
```bash
# عرض جميع اللوقات
pm2 logs

# عرض لوقات عملية معينة
pm2 logs yemclient-server

# عرض آخر 100 سطر
pm2 logs yemclient-server --lines 100

# متابعة اللوقات في الوقت الفعلي
pm2 logs yemclient-server --follow
```

## 🔄 إدارة دورة الحياة

### إعادة التشغيل
```bash
# إعادة تشغيل عملية معينة
pm2 restart yemclient-server

# إعادة تشغيل جميع العمليات
pm2 restart all

# إعادة تشغيل مع تنظيف الذاكرة
pm2 restart yemclient-server --update-env
```

### إعادة التحميل (Zero-downtime)
```bash
# إعادة تحميل بدون انقطاع
pm2 reload yemclient-server

# إعادة تحميل جميع العمليات
pm2 reload all
```

### الإيقاف والحذف
```bash
# إيقاف عملية
pm2 stop yemclient-server

# إيقاف جميع العمليات
pm2 stop all

# حذف عملية من PM2
pm2 delete yemclient-server

# حذف جميع العمليات
pm2 delete all
```

## 📈 مراقبة الأداء

### معلومات الذاكرة والمعالج
```bash
# عرض استخدام الموارد
pm2 monit

# معلومات مفصلة
pm2 show yemclient-server
```

### إعادة التشغيل التلقائي عند استهلاك الذاكرة
```bash
# إعداد حد أقصى للذاكرة (1GB)
pm2 start production-server.js --max-memory-restart 1G
```

### مراقبة ملفات النظام
```bash
# مراقبة تغييرات الملفات (للتطوير فقط)
pm2 start production-server.js --watch

# استثناء مجلدات معينة
pm2 start production-server.js --watch --ignore-watch="node_modules logs"
```

## 🔧 التكوين المتقدم

### ملف ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'yemclient-server',
    script: './production-server.js',
    
    // إعدادات الأداء
    instances: 1,                    // عدد النسخ
    exec_mode: 'fork',              // نمط التشغيل
    max_memory_restart: '1G',       // إعادة تشغيل عند 1GB
    
    // إعدادات البيئة
    env: {
      NODE_ENV: 'development',
      PORT: 8080
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 8080
    },
    
    // إعدادات اللوقات
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    
    // إعدادات إعادة التشغيل
    autorestart: true,              // إعادة تشغيل تلقائي
    restart_delay: 1000,            // تأخير إعادة التشغيل
    max_restarts: 10,               // حد أقصى لإعادة التشغيل
    min_uptime: '10s',              // وقت تشغيل أدنى
    
    // إعدادات الإغلاق
    kill_timeout: 5000,             // وقت انتظار الإغلاق
    listen_timeout: 3000,           // وقت انتظار الاستماع
    shutdown_with_message: true,    // إرسال رسالة عند الإغلاق
    wait_ready: true,               // انتظار إشارة الجاهزية
    
    // متغيرات إضافية
    instance_var: 'INSTANCE_ID',
    time: true,                     // إضافة الوقت للوقات
    watch: false                    // عدم مراقبة الملفات في الإنتاج
  }]
};
```

## 🔄 البدء التلقائي

### إعداد البدء التلقائي مع النظام
```bash
# إنشاء سكريبت البدء التلقائي
pm2 startup

# حفظ قائمة العمليات الحالية
pm2 save

# إلغاء البدء التلقائي
pm2 unstartup
```

### للأنظمة المختلفة
```bash
# Ubuntu/Debian
pm2 startup ubuntu

# CentOS/RHEL
pm2 startup centos

# Windows (يتطلب إعداد إضافي)
pm2-windows-startup install
```

## 📊 إدارة اللوقات

### تنظيف اللوقات
```bash
# مسح جميع اللوقات
pm2 flush

# مسح لوقات عملية معينة
pm2 flush yemclient-server
```

### تدوير اللوقات
```bash
# تثبيت pm2-logrotate
pm2 install pm2-logrotate

# تكوين تدوير اللوقات
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. العملية لا تبدأ
```bash
# فحص اللوقات
pm2 logs yemclient-server

# فحص تفاصيل العملية
pm2 show yemclient-server

# إعادة تشغيل مع تنظيف البيئة
pm2 restart yemclient-server --update-env
```

#### 2. استهلاك ذاكرة عالي
```bash
# مراقبة الذاكرة
pm2 monit

# إعادة تشغيل العملية
pm2 restart yemclient-server

# تقليل حد الذاكرة
pm2 restart yemclient-server --max-memory-restart 512M
```

#### 3. العملية تتوقف باستمرار
```bash
# فحص عدد إعادات التشغيل
pm2 show yemclient-server

# زيادة وقت التشغيل الأدنى
pm2 restart yemclient-server --min-uptime 30s

# فحص الأخطاء في اللوقات
pm2 logs yemclient-server --err
```

## 📋 أوامر مفيدة

### معلومات النظام
```bash
# معلومات PM2
pm2 info

# إصدار PM2
pm2 --version

# قائمة الوحدات المثبتة
pm2 module:list
```

### إدارة التكوين
```bash
# إعادة تحميل التكوين
pm2 startOrReload ecosystem.config.js

# تحديث التكوين
pm2 restart ecosystem.config.js --update-env

# حفظ التكوين الحالي
pm2 save --force
```

### مراقبة متقدمة
```bash
# عرض معلومات مفصلة
pm2 prettylist

# تصدير معلومات JSON
pm2 jlist

# عرض البيئة
pm2 env 0
```

## 🚀 أفضل الممارسات

### 1. استخدام ملف التكوين
- استخدم دائماً `ecosystem.config.js`
- احفظ جميع الإعدادات في الملف
- استخدم بيئات مختلفة للتطوير والإنتاج

### 2. إدارة اللوقات
- استخدم تدوير اللوقات لتجنب امتلاء القرص
- راقب اللوقات بانتظام
- استخدم مستويات لوق مناسبة

### 3. مراقبة الأداء
- راقب استهلاك الذاكرة والمعالج
- اضبط حدود الذاكرة المناسبة
- استخدم `pm2 monit` للمراقبة المستمرة

### 4. النشر
- استخدم `pm2 reload` للنشر بدون انقطاع
- احفظ التكوين بعد كل تغيير
- اختبر التكوين في بيئة التطوير أولاً

---

**تم إعداده بواسطة:** Augment Agent  
**التاريخ:** يوليو 2025  
**الإصدار:** 2.0.0
