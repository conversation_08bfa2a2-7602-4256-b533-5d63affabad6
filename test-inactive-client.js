/**
 * اختبار العميل غير النشط
 */

async function testInactiveClient() {
  console.log('🧪 اختبار العميل غير النشط...\n');

  try {
    // اختبار العميل غير النشط (1005)
    console.log('1️⃣ اختبار العميل غير النشط (1005):');
    
    const inactiveResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1005,
        client_token: 'UNdZqPVxrxAX'
      })
    });

    console.log(`   📡 Status: ${inactiveResponse.status}`);
    
    if (inactiveResponse.ok) {
      const inactiveData = await inactiveResponse.json();
      console.log('   ✅ التحقق نجح!');
      console.log(`   📊 Status: ${inactiveData.status}`);
      console.log(`   👤 Client Status: ${inactiveData.client_status}`);
      
      if (inactiveData.client_status === 0) {
        console.log('   🎉 النظام يعرض حالة العميل غير النشط بشكل صحيح!');
        console.log('   📝 الرد يحتوي على: {"status":"success","client_status":0}');
      } else if (inactiveData.client_status === 1) {
        console.log('   ⚠️ النظام يعرض العميل كنشط رغم أنه غير نشط!');
        console.log('   📝 الرد يحتوي على: {"status":"success","client_status":1}');
      } else {
        console.log(`   ❓ حالة غير متوقعة: ${inactiveData.client_status}`);
      }
    } else {
      const errorData = await inactiveResponse.json();
      console.log('   ❌ التحقق فشل!');
      console.log(`   📝 Error Status: ${errorData.status}`);
      console.log(`   💬 Message: ${errorData.message || 'لا توجد رسالة'}`);
      
      if (errorData.status === 'client_error') {
        console.log('   ⚠️ النظام يرفض العميل غير النشط كـ client_error');
        console.log('   📝 الرد: {"status":"client_error"}');
        console.log('   🔧 هذا يعني أن النظام لا يفرق بين العميل غير الموجود والعميل غير النشط');
      }
    }
    console.log('');

    // اختبار العميل النشط للمقارنة (1004)
    console.log('2️⃣ اختبار العميل النشط للمقارنة (1004):');
    
    const activeResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      })
    });

    console.log(`   📡 Status: ${activeResponse.status}`);
    
    if (activeResponse.ok) {
      const activeData = await activeResponse.json();
      console.log('   ✅ التحقق نجح!');
      console.log(`   📊 Status: ${activeData.status}`);
      console.log(`   👤 Client Status: ${activeData.client_status}`);
      console.log('   📝 الرد: {"status":"success","client_status":1}');
    } else {
      const errorData = await activeResponse.json();
      console.log('   ❌ التحقق فشل!');
      console.log(`   📝 Error: ${errorData.status}`);
    }
    console.log('');

    // اختبار عميل غير موجود للمقارنة (9999)
    console.log('3️⃣ اختبار عميل غير موجود للمقارنة (9999):');
    
    const notFoundResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 9999,
        client_token: 'INVALID'
      })
    });

    console.log(`   📡 Status: ${notFoundResponse.status}`);
    
    if (notFoundResponse.ok) {
      const notFoundData = await notFoundResponse.json();
      console.log('   ⚠️ التحقق نجح (غير متوقع)!');
      console.log(`   📊 Status: ${notFoundData.status}`);
    } else {
      const errorData = await notFoundResponse.json();
      console.log('   ✅ التحقق فشل (كما هو متوقع)!');
      console.log(`   📝 Error: ${errorData.status}`);
      console.log('   📝 الرد: {"status":"client_error"}');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار حالات العملاء:');
    console.log('');
    console.log('🎯 النتائج المطلوبة:');
    console.log('✅ العميل النشط (1004): {"status":"success","client_status":1}');
    console.log('⚠️ العميل غير النشط (1005): {"status":"success","client_status":0}');
    console.log('❌ العميل غير الموجود (9999): {"status":"client_error"}');
    console.log('');
    console.log('📝 ملاحظة: إذا كان العميل غير النشط يعطي client_error');
    console.log('   فهذا يعني أن النظام يحتاج إصلاح للتفريق بين:');
    console.log('   - العميل غير الموجود (client_error)');
    console.log('   - العميل غير النشط (success مع client_status: 0)');

  } catch (error) {
    console.error('❌ خطأ في اختبار العميل غير النشط:', error.message);
  }
}

testInactiveClient().catch(console.error);
