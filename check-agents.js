const { PrismaClient } = require('./server/node_modules/@prisma/client')
const bcrypt = require('./server/node_modules/bcrypt')

const prisma = new PrismaClient()

async function checkAgents() {
  try {
    console.log('🔍 Checking agents in database...')

    const agents = await prisma.agent.findMany({
      select: {
        id: true,
        agentName: true,
        loginName: true,
        loginPassword: true,
        isActive: true
      }
    })

    console.log(`Found ${agents.length} agents:`)

    for (const agent of agents) {
      console.log(`\nAgent ID: ${agent.id}`)
      console.log(`Name: ${agent.agentName}`)
      console.log(`Login Name: ${agent.loginName}`)
      console.log(`Has Password: ${!!agent.loginPassword}`)
      console.log(`Is Active: ${agent.isActive}`)

      if (agent.loginPassword) {
        console.log(`Password Hash: ${agent.loginPassword.substring(0, 20)}...`)
      }
    }

    // إنشاء وكيل اختبار إذا لم يكن موجوداً
    const testAgent = await prisma.agent.findFirst({
      where: { loginName: 'agent001' }
    })

    if (!testAgent) {
      console.log('\n🔧 Creating test agent...')
      const hashedPassword = await bcrypt.hash('agent123', 10)

      const newAgent = await prisma.agent.create({
        data: {
          agentName: 'وكيل اختبار',
          agencyName: 'شركة الاختبار',
          agencyType: 'وكيل يمن موبايل',
          ipAddress: '127.0.0.1',
          loginName: 'agent001',
          loginPassword: hashedPassword,
          isActive: true
        }
      })

      console.log('✅ Test agent created:', newAgent)
    } else {
      console.log('\n✅ Test agent exists')

      // التحقق من كلمة المرور
      if (testAgent.loginPassword) {
        const isValid = await bcrypt.compare('agent123', testAgent.loginPassword)
        console.log(`Password check: ${isValid ? '✅ Valid' : '❌ Invalid'}`)
      } else {
        console.log('❌ No password set for test agent')

        // تحديث كلمة المرور
        const hashedPassword = await bcrypt.hash('agent123', 10)
        await prisma.agent.update({
          where: { id: testAgent.id },
          data: { loginPassword: hashedPassword }
        })
        console.log('✅ Password updated for test agent')
      }
    }

  } catch (error) {
    console.error('❌ Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkAgents()
