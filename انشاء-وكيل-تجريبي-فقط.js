const { PrismaClient } = require('./server/node_modules/@prisma/client')
const bcrypt = require('./server/node_modules/bcrypt')

const prisma = new PrismaClient()

async function createTestAgentOnly() {
  try {
    console.log('🔧 إنشاء الوكيل التجريبي فقط...')
    console.log('=' .repeat(50))
    
    // التحقق من وجود الوكيل التجريبي
    const existingAgent = await prisma.agent.findFirst({
      where: { loginName: 'testuser' }
    })

    if (existingAgent) {
      console.log('⚠️ الوكيل التجريبي موجود بالفعل:')
      console.log(`   الرقم: ${existingAgent.id}`)
      console.log(`   الاسم: ${existingAgent.agentName}`)
      console.log(`   اسم المستخدم: testuser`)
      console.log(`   كلمة المرور: test123`)
      console.log('')
      console.log('✅ يمكن استخدام البيانات الموجودة')
      return
    }

    // إنشاء الوكيل التجريبي
    console.log('👤 إنشاء الوكيل التجريبي الجديد...')
    
    const hashedPassword = await bcrypt.hash('test123', 10)
    
    const testAgent = await prisma.agent.create({
      data: {
        agentName: 'فحص تجريبي',
        agencyName: 'وكالة الاختبار',
        agencyType: 'وكيل اختبار',
        ipAddress: '*************',
        loginName: 'testuser',
        loginPassword: hashedPassword,
        isActive: true
      }
    })
    
    console.log('✅ تم إنشاء الوكيل التجريبي بنجاح:')
    console.log(`   الرقم: ${testAgent.id}`)
    console.log(`   الاسم: ${testAgent.agentName}`)
    console.log(`   اسم المستخدم: testuser`)
    console.log(`   كلمة المرور: test123`)
    console.log(`   نوع الوكالة: ${testAgent.agencyType}`)
    console.log(`   عنوان IP: ${testAgent.ipAddress}`)
    console.log('')

    // عرض العملاء التجريبيين الموجودين
    console.log('👥 العملاء التجريبيون الموجودون:')
    console.log('=' .repeat(40))
    
    const testClients = await prisma.client.findMany({
      where: {
        clientCode: {
          in: [1004, 1005, 9999]
        }
      },
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        token: true,
        status: true
      }
    })

    if (testClients.length > 0) {
      testClients.forEach(client => {
        console.log(`العميل ${client.clientCode}:`)
        console.log(`   الاسم: ${client.clientName}`)
        console.log(`   التوكن: ${client.token}`)
        console.log(`   الحالة: ${client.status === 1 ? 'نشط' : 'غير نشط'}`)
        console.log('')
      })
    } else {
      console.log('⚠️ لم يتم العثور على عملاء تجريبيين')
      console.log('💡 يمكن استخدام العملاء الموجودين للاختبار')
    }

    // عرض ملخص البيانات التجريبية
    console.log('📋 ملخص البيانات التجريبية:')
    console.log('=' .repeat(50))
    console.log('')
    console.log('🏢 الوكيل التجريبي:')
    console.log('   اسم المستخدم: testuser')
    console.log('   كلمة المرور: test123')
    console.log('   الاسم: فحص تجريبي')
    console.log('')
    console.log('👥 العملاء المتاحون للاختبار:')
    console.log('   1004 + TEST1004 (إذا كان موجود)')
    console.log('   1005 + TEST1005 (إذا كان موجود)')
    console.log('   9999 + DUMMY999 (إذا كان موجود)')
    console.log('   أو استخدم العملاء الموجودين: 1000 + ABC12345')
    console.log('')
    console.log('🔧 اختبارات مقترحة:')
    console.log('   ✅ testuser + test123 + 1000 + ABC12345 = success')
    console.log('   ❌ testuser + WRONG + 1000 + ABC12345 = agent_error')
    console.log('   ❌ testuser + test123 + 1000 + WRONG = client_error')
    console.log('   ❌ testuser + test123 + 8888 + INVALID = client_error')
    console.log('')
    console.log('🎉 الوكيل التجريبي جاهز للاستخدام!')

  } catch (error) {
    console.error('❌ خطأ في إنشاء الوكيل التجريبي:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestAgentOnly()
