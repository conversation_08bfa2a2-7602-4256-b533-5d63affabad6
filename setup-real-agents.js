const { PrismaClient } = require('./server/node_modules/@prisma/client')
const bcrypt = require('./server/node_modules/bcrypt')

const prisma = new PrismaClient()

async function setupRealAgents() {
  try {
    console.log('🔧 إعداد بيانات تسجيل الدخول للوكلاء الحقيقيين...')
    
    // إعداد وكيل الغراسي
    const alGhurasiAgent = await prisma.agent.findFirst({
      where: { agentName: 'الغراسي' }
    })

    if (alGhurasiAgent) {
      console.log('✅ تم العثور على وكيل الغراسي')
      
      // إضافة بيانات تسجيل الدخول
      const hashedPassword = await bcrypt.hash('alghurasi123', 10)
      
      await prisma.agent.update({
        where: { id: alGhurasiAgent.id },
        data: {
          loginName: 'alghurasi',
          loginPassword: hashedPassword
        }
      })
      
      console.log('✅ تم إعداد بيانات تسجيل الدخول لوكيل الغراسي')
      console.log('   اسم المستخدم: alghurasi')
      console.log('   كلمة المرور: alghurasi123')
    } else {
      console.log('⚠️ لم يتم العثور على وكيل الغراسي')
    }

    // إعداد وكيل المترب
    const alMutaribAgent = await prisma.agent.findFirst({
      where: { agentName: 'المترب' }
    })

    if (alMutaribAgent) {
      console.log('✅ تم العثور على وكيل المترب')
      
      // إضافة بيانات تسجيل الدخول
      const hashedPassword = await bcrypt.hash('almutarib123', 10)
      
      await prisma.agent.update({
        where: { id: alMutaribAgent.id },
        data: {
          loginName: 'almutarib',
          loginPassword: hashedPassword
        }
      })
      
      console.log('✅ تم إعداد بيانات تسجيل الدخول لوكيل المترب')
      console.log('   اسم المستخدم: almutarib')
      console.log('   كلمة المرور: almutarib123')
    } else {
      console.log('⚠️ لم يتم العثور على وكيل المترب')
    }

    // عرض جميع الوكلاء مع بيانات تسجيل الدخول
    console.log('\n📋 قائمة الوكلاء المحدثة:')
    const allAgents = await prisma.agent.findMany({
      select: {
        id: true,
        agentName: true,
        loginName: true,
        isActive: true
      }
    })

    allAgents.forEach(agent => {
      console.log(`\nالوكيل: ${agent.agentName}`)
      console.log(`  الرقم: ${agent.id}`)
      console.log(`  اسم المستخدم: ${agent.loginName || 'غير محدد'}`)
      console.log(`  الحالة: ${agent.isActive ? 'نشط' : 'غير نشط'}`)
    })

    console.log('\n🎉 تم إعداد جميع الوكلاء بنجاح!')

  } catch (error) {
    console.error('❌ خطأ في إعداد الوكلاء:', error)
  } finally {
    await prisma.$disconnect()
  }
}

setupRealAgents()
