-- إ<PERSON><PERSON><PERSON><PERSON> الجداول لقاعدة البيانات yemclient_db

-- إ<PERSON><PERSON><PERSON><PERSON> جدول المستخدمين
CREATE TABLE users (
    user_id SERIAL PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    device_id TEXT,
    login_name TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    permissions JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الوكلاء
CREATE TABLE agents (
    agent_id SERIAL PRIMARY KEY,
    agent_name TEXT NOT NULL,
    agency_name TEXT NOT NULL,
    agency_type TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    device_id TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول العملاء
CREATE TABLE clients (
    client_id SERIAL PRIMARY KEY,
    client_name TEXT NOT NULL,
    app_name TEXT NOT NULL,
    card_number TEXT NOT NULL,
    client_code INTEGER UNIQUE NOT NULL,
    password TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    status INTEGER NOT NULL DEFAULT 1,
    agent_id INTEGER REFERENCES agents(agent_id) ON DELETE SET NULL,
    user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    created_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول محاولات الدخول
CREATE TABLE login_attempts (
    id SERIAL PRIMARY KEY,
    user_type TEXT NOT NULL,
    user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    agent_id INTEGER REFERENCES agents(agent_id) ON DELETE SET NULL,
    device_id TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    success BOOLEAN NOT NULL DEFAULT false,
    timestamp TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- إنشاء جدول Prisma migrations
CREATE TABLE _prisma_migrations (
    id VARCHAR(36) PRIMARY KEY,
    checksum VARCHAR(64) NOT NULL,
    finished_at TIMESTAMP(3),
    migration_name VARCHAR(255) NOT NULL,
    logs TEXT,
    rolled_back_at TIMESTAMP(3),
    started_at TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    applied_steps_count INTEGER NOT NULL DEFAULT 0
);

-- تعيين تسلسل رمز العميل ليبدأ من 1000
ALTER SEQUENCE clients_client_code_seq RESTART WITH 1000;

-- إدخال بيانات المستخدم الأدمن (كلمة المرور: admin123456)
INSERT INTO users (user_id, username, login_name, password, permissions) VALUES 
(100, 'admin', 'admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.', 
'{"isAdmin": true, "clients": {"create": true, "read": true, "update": true, "delete": true}, "agents": {"create": true, "read": true, "update": true, "delete": true}, "users": {"create": true, "read": true, "update": true, "delete": true}, "dashboard": {"read": true}, "security": {"read": true, "manage": true}}');

-- إدخال بيانات المستخدم التجريبي (كلمة المرور: user123456)
INSERT INTO users (user_id, username, login_name, password, permissions) VALUES 
(101, 'مستخدم تجريبي', 'testuser', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvAu.', 
'{"isAdmin": false, "clients": {"create": true, "read": true, "update": true, "delete": false}, "agents": {"create": false, "read": true, "update": false, "delete": false}, "users": {"create": false, "read": false, "update": false, "delete": false}, "dashboard": {"read": true}}');

-- إدخال بيانات الوكلاء التجريبيين
INSERT INTO agents (agent_name, agency_name, agency_type, ip_address) VALUES 
('أحمد محمد', 'وكالة النجاح', 'وكالة تجارية', '*************'),
('فاطمة علي', 'وكالة الأمل', 'وكالة خدمات', '*************');

-- إدخال بيانات العملاء التجريبيين
INSERT INTO clients (client_name, app_name, card_number, client_code, password, ip_address, status, agent_id, user_id) VALUES 
('محمد أحمد', 'تطبيق الدفع', '12345678', 1000, 'client123', '*************', 1, 1, 101),
('سارة محمود', 'تطبيق التحويلات', '87654321', 1001, 'client456', '*************', 1, 2, 100);

-- إدخال سجل migration
INSERT INTO _prisma_migrations (id, checksum, migration_name, applied_steps_count) VALUES 
('20250627000000_init', '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef', '20250627000000_init', 1);
