@echo off
chcp 65001 >nul
title تشغيل نظام إدارة العملاء والوكلاء

echo ========================================
echo    تشغيل نظام إدارة العملاء والوكلاء
echo ========================================

echo.
echo التحقق من إعداد النظام...

if not exist "server\node_modules" (
    echo ⚠ النظام غير مُعد بعد!
    echo يرجى تشغيل auto_setup.bat أولاً
    pause
    exit /b 1
)

if not exist "client\node_modules" (
    echo ⚠ النظام غير مُعد بعد!
    echo يرجى تشغيل auto_setup.bat أولاً
    pause
    exit /b 1
)

echo ✓ النظام مُعد ومجهز

echo.
echo اختر طريقة التشغيل:
echo 1. تشغيل محلي (PostgreSQL)
echo 2. تشغيل Docker
echo 3. خروج
echo.

set /p choice="اختر رقم (1-3): "

if "%choice%"=="1" goto local_run
if "%choice%"=="2" goto docker_run
if "%choice%"=="3" goto exit
goto invalid_choice

:local_run
echo.
echo === التشغيل المحلي ===

echo تشغيل الخادم...
cd server
start "YemClient Server" cmd /k "npm start"

echo انتظار تشغيل الخادم...
timeout /t 5 /nobreak >nul

echo تشغيل العميل...
cd ..\client
start "YemClient Frontend" cmd /k "npm run dev"

echo.
echo ✓ تم تشغيل النظام!
echo.
echo 📊 معلومات الوصول:
echo لوحة التحكم: http://localhost:5173
echo API الخادم: http://localhost:3000
echo.
echo 🔑 بيانات الدخول:
echo Username: admin
echo Password: admin123456
echo.
echo 📋 قاعدة البيانات في pgAdmin:
echo Host: localhost
echo Port: 5432
echo Database: yemclient_db
echo Username: postgres
echo Password: yemen123
echo.
goto end

:docker_run
echo.
echo === تشغيل Docker ===

echo تشغيل النظام...
docker-compose up -d

echo انتظار تشغيل الخدمات...
timeout /t 30 /nobreak >nul

echo.
echo ✓ تم تشغيل النظام!
echo.
echo 📊 معلومات الوصول:
echo لوحة التحكم: http://localhost:5173
echo pgAdmin: http://localhost:5050
echo API الخادم: http://localhost:3000
echo.
echo 🔑 بيانات دخول النظام:
echo Username: admin
echo Password: admin123456
echo.
echo 🔑 بيانات دخول pgAdmin:
echo Email: <EMAIL>
echo Password: admin123456
echo.
echo 📋 قاعدة البيانات في pgAdmin:
echo Host: postgres
echo Port: 5432
echo Database: yemclient_db
echo Username: yemclient_user
echo Password: yemclient_password
echo.
goto end

:invalid_choice
echo اختيار غير صحيح!
pause
goto start

:exit
echo خروج...
goto end

:end
echo.
pause
