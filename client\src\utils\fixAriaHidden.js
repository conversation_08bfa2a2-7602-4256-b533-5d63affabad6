// إصلاح مشكلة aria-hidden في Material-UI
export const fixAriaHidden = () => {
  // مراقبة تغييرات aria-hidden على العنصر الجذر
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'attributes' && mutation.attributeName === 'aria-hidden') {
        const target = mutation.target;
        if (target.id === 'root' && target.getAttribute('aria-hidden') === 'true') {
          // إزالة aria-hidden من العنصر الجذر
          target.removeAttribute('aria-hidden');
          console.log('تم إزالة aria-hidden من العنصر الجذر');
        }
      }
    });
  });

  // بدء مراقبة العنصر الجذر
  const rootElement = document.getElementById('root');
  if (rootElement) {
    observer.observe(rootElement, {
      attributes: true,
      attributeFilter: ['aria-hidden']
    });
  }

  // إزالة aria-hidden إذا كان موجوداً بالفعل
  if (rootElement && rootElement.getAttribute('aria-hidden') === 'true') {
    rootElement.removeAttribute('aria-hidden');
  }

  return observer;
};

// تشغيل الإصلاح عند تحميل الصفحة
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', fixAriaHidden);
  
  // تشغيل فوري إذا كان DOM جاهزاً
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixAriaHidden);
  } else {
    fixAriaHidden();
  }
}
