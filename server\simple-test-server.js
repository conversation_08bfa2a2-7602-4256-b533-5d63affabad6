const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 8080;

// Middleware
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  console.log(`🔄 ${req.method} ${req.path} from ${req.ip}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    server: 'Simple Test Server'
  });
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString(),
    server: 'Simple Test Server'
  });
});

// Mock Dashboard Stats API
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    totalUsers: 4,
    totalClients: 6,
    totalAgents: 5,
    totalDataRecords: 125,
    totalSecurityRecords: 361,
    activeUsers: 4,
    systemHealth: 'excellent',
    timestamp: new Date().toISOString()
  });
});

// Mock Clients API
app.get('/api/clients', (req, res) => {
  const mockClients = [
    {
      id: 1,
      clientCode: '1001',
      clientName: 'شركة اليمن للتجارة',
      appName: 'Yemen Trade App',
      ipAddress: '*************',
      status: 1,
      createdByUserName: 'admin'
    },
    {
      id: 2,
      clientCode: '1002',
      clientName: 'مؤسسة صنعاء التجارية',
      appName: 'Sanaa Commerce',
      ipAddress: '*************',
      status: 1,
      createdByUserName: 'admin'
    },
    {
      id: 3,
      clientCode: '1003',
      clientName: 'شركة عدن للخدمات',
      appName: 'Aden Services',
      ipAddress: '*************',
      status: 1,
      createdByUserName: 'admin'
    }
  ];

  res.json({
    data: mockClients,
    total: mockClients.length,
    page: 1,
    limit: 10,
    totalPages: 1
  });
});

// Mock Data Records API
app.get('/api/data-records', (req, res) => {
  const mockDataRecords = [
    {
      id: 1,
      agentId: 1,
      clientId: 1,
      clientCode: '1001',
      operationDate: new Date().toISOString(),
      operationStatus: 'success',
      agentReference: 'AGT001',
      agentName: 'وكيل صنعاء',
      clientName: 'شركة اليمن للتجارة'
    },
    {
      id: 2,
      agentId: 2,
      clientId: 2,
      clientCode: '1002',
      operationDate: new Date().toISOString(),
      operationStatus: 'success',
      agentReference: 'AGT002',
      agentName: 'وكيل عدن',
      clientName: 'مؤسسة صنعاء التجارية'
    },
    {
      id: 3,
      agentId: 1,
      clientId: 3,
      clientCode: '1003',
      operationDate: new Date().toISOString(),
      operationStatus: 'pending',
      agentReference: 'AGT001',
      agentName: 'وكيل صنعاء',
      clientName: 'شركة عدن للخدمات'
    }
  ];

  res.json({
    dataRecords: mockDataRecords,
    total: mockDataRecords.length,
    pagination: {
      page: 1,
      limit: 10,
      totalPages: 1,
      total: mockDataRecords.length
    }
  });
});

// Mock Security Login Attempts API
app.get('/api/security/login-attempts', (req, res) => {
  const mockAttempts = [
    {
      id: '1',
      type: 'success',
      username: 'admin',
      ip: '************',
      timestamp: new Date().toISOString(),
      userAgent: 'System Login',
      deviceId: 'device001',
      reason: null,
      userType: 'user'
    },
    {
      id: '2',
      type: 'failed',
      username: 'test_user',
      ip: '************',
      timestamp: new Date().toISOString(),
      userAgent: 'System Login',
      deviceId: 'device002',
      reason: 'Authentication failed',
      userType: 'user'
    }
  ];

  res.json({
    attempts: mockAttempts,
    total: mockAttempts.length,
    pagination: {
      page: 1,
      limit: 10,
      totalPages: 1,
      total: mockAttempts.length
    }
  });
});

// Mock Security Stats API
app.get('/api/security/stats', (req, res) => {
  res.json({
    totalAttempts: 361,
    successfulAttempts: 252,
    failedAttempts: 109,
    todayAttempts: 15,
    successRate: '69.8'
  });
});

// Mock Recent Activity API
app.get('/api/dashboard/recent-activity', (req, res) => {
  const activities = [
    {
      id: 1,
      type: 'login_success',
      description: 'تسجيل دخول ناجح - admin',
      timestamp: new Date().toISOString(),
      ip: '************'
    },
    {
      id: 2,
      type: 'login_failed',
      description: 'محاولة دخول فاشلة - test_user',
      timestamp: new Date().toISOString(),
      ip: '************'
    }
  ];

  res.json(activities);
});

// Mock Agents API
app.get('/api/agents', (req, res) => {
  const mockAgents = [
    {
      id: 1,
      agentName: 'وكيل صنعاء',
      loginName: 'sanaa_agent',
      agentCode: 'AGT001',
      status: 1
    },
    {
      id: 2,
      agentName: 'وكيل عدن',
      loginName: 'aden_agent',
      agentCode: 'AGT002',
      status: 1
    }
  ];

  res.json({
    data: mockAgents,
    total: mockAgents.length,
    page: 1,
    limit: 10,
    totalPages: 1
  });
});

// Mock Users API
app.get('/api/users', (req, res) => {
  const mockUsers = [
    {
      id: 1,
      username: 'admin',
      loginName: 'admin',
      permissions: 'all',
      isActive: true,
      device1: 'device001'
    },
    {
      id: 2,
      username: 'user1',
      loginName: 'user1',
      permissions: 'read',
      isActive: true,
      device1: 'device002'
    }
  ];

  res.json({
    data: mockUsers,
    total: mockUsers.length,
    page: 1,
    limit: 10,
    totalPages: 1
  });
});

// Mock Auth API
app.post('/api/auth/login', (req, res) => {
  const { loginName, password } = req.body;
  
  if (loginName === 'admin' && password === 'admin123') {
    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: 1,
        username: 'admin',
        loginName: 'admin',
        permissions: 'all'
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'بيانات الدخول غير صحيحة'
    });
  }
});

// Static files
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found', path: req.path });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Server Error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Simple Test Server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`🏠 Local: http://localhost:${PORT}`);
  console.log(`📊 Mock data ready - APIs working`);
});

server.on('error', (err) => {
  console.error('Server startup error:', err);
});

module.exports = app;
