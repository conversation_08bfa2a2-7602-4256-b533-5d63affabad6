#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 YemClient Server Quick Start\n');

// التحقق من الملفات المطلوبة
const requiredFiles = [
  'production-server.js',
  'ecosystem.config.js',
  'package.json',
  '.env'
];

console.log('📋 Checking required files...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - Missing!`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please ensure all files are present.');
  process.exit(1);
}

// إنشاء مجلد logs
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('📁 Created logs directory');
}

// التحقق من PM2
console.log('\n🔧 Checking PM2...');
try {
  execSync('pm2 --version', { stdio: 'pipe' });
  console.log('✅ PM2 is installed');
} catch (error) {
  console.log('❌ PM2 is not installed. Installing...');
  try {
    execSync('npm install -g pm2', { stdio: 'inherit' });
    console.log('✅ PM2 installed successfully');
  } catch (installError) {
    console.log('❌ Failed to install PM2. Please install manually: npm install -g pm2');
    process.exit(1);
  }
}

// التحقق من قاعدة البيانات
console.log('\n🗄️  Checking database...');
try {
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  
  (async () => {
    try {
      await prisma.$connect();
      await prisma.$queryRaw`SELECT 1`;
      console.log('✅ Database connection successful');
      await prisma.$disconnect();
      
      // بدء الخادم
      startServer();
    } catch (dbError) {
      console.log('❌ Database connection failed:', dbError.message);
      console.log('Please check your DATABASE_URL in .env file');
      process.exit(1);
    }
  })();
  
} catch (error) {
  console.log('❌ Prisma client error:', error.message);
  console.log('Please run: npm run db:generate');
  process.exit(1);
}

function startServer() {
  console.log('\n🚀 Starting server...');
  
  // عرض خيارات التشغيل
  console.log('\nChoose how to start the server:');
  console.log('1. Development mode (nodemon)');
  console.log('2. Production mode (direct)');
  console.log('3. PM2 mode (recommended for production)');
  console.log('4. Test server only');
  
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('\nEnter your choice (1-4): ', (choice) => {
    rl.close();
    
    switch (choice.trim()) {
      case '1':
        startDevelopment();
        break;
      case '2':
        startProduction();
        break;
      case '3':
        startPM2();
        break;
      case '4':
        testServer();
        break;
      default:
        console.log('Invalid choice. Starting in development mode...');
        startDevelopment();
    }
  });
}

function startDevelopment() {
  console.log('\n🔧 Starting in development mode...');
  console.log('Server will restart automatically on file changes');
  console.log('Press Ctrl+C to stop\n');
  
  const server = spawn('node', ['database-only-server.js'], {
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'development' }
  });
  
  server.on('close', (code) => {
    console.log(`\nDevelopment server exited with code ${code}`);
  });
}

function startProduction() {
  console.log('\n🚀 Starting in production mode...');
  console.log('Press Ctrl+C to stop\n');
  
  const server = spawn('node', ['production-server.js'], {
    stdio: 'inherit',
    env: { ...process.env, NODE_ENV: 'production' }
  });
  
  server.on('close', (code) => {
    console.log(`\nProduction server exited with code ${code}`);
  });
}

function startPM2() {
  console.log('\n🔄 Starting with PM2...');
  
  try {
    // إيقاف العملية إذا كانت تعمل
    try {
      execSync('pm2 stop yemclient-server', { stdio: 'pipe' });
      execSync('pm2 delete yemclient-server', { stdio: 'pipe' });
    } catch (e) {
      // العملية غير موجودة، لا مشكلة
    }
    
    // بدء العملية الجديدة
    execSync('pm2 start ecosystem.config.js', { stdio: 'inherit' });
    
    console.log('\n✅ Server started with PM2!');
    console.log('\nUseful PM2 commands:');
    console.log('- pm2 status          : Check server status');
    console.log('- pm2 logs            : View logs');
    console.log('- pm2 monit           : Monitor performance');
    console.log('- pm2 restart all     : Restart server');
    console.log('- pm2 stop all        : Stop server');
    
    // عرض الحالة
    setTimeout(() => {
      try {
        execSync('pm2 status', { stdio: 'inherit' });
      } catch (e) {
        console.log('Could not display PM2 status');
      }
    }, 2000);
    
  } catch (error) {
    console.log('❌ Failed to start with PM2:', error.message);
    console.log('Falling back to production mode...');
    startProduction();
  }
}

function testServer() {
  console.log('\n🧪 Testing server...');
  
  // بدء الخادم في الخلفية
  const server = spawn('node', ['production-server.js'], {
    stdio: 'pipe',
    env: { ...process.env, NODE_ENV: 'test' }
  });
  
  // انتظار بدء الخادم
  setTimeout(() => {
    console.log('Running tests...\n');
    
    const test = spawn('node', ['production-test.js'], {
      stdio: 'inherit'
    });
    
    test.on('close', (code) => {
      console.log(`\nTest completed with code ${code}`);
      
      // إيقاف الخادم
      server.kill('SIGTERM');
      
      setTimeout(() => {
        process.exit(code);
      }, 1000);
    });
    
  }, 3000);
  
  server.on('error', (error) => {
    console.log('❌ Server error:', error.message);
    process.exit(1);
  });
}

// معالجة إشارات الإيقاف
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 Shutting down...');
  process.exit(0);
});
