const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 8080;

console.log('🚀 Starting Yemen Client Server...');

// Basic middleware
app.use(cors({ origin: '*', credentials: true }));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Request logging
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    server: 'Yemen Client Server',
    port: PORT
  });
});

// Load database connection
let prisma = null;
try {
  const { PrismaClient } = require('@prisma/client');
  prisma = new PrismaClient({
    log: ['query', 'info', 'warn', 'error'],
  });
  console.log('✅ Database client initialized');

  // Test connection
  prisma.$connect().then(() => {
    console.log('✅ Database connected successfully');

    // Test data
    prisma.user.count().then(count => {
      console.log(`👥 Users in database: ${count}`);
    }).catch(err => {
      console.error('❌ Error counting users:', err.message);
    });

    prisma.client.count().then(count => {
      console.log(`🏢 Clients in database: ${count}`);
    }).catch(err => {
      console.error('❌ Error counting clients:', err.message);
    });

  }).catch(error => {
    console.error('❌ Database connection failed:', error.message);
  });

} catch (error) {
  console.error('❌ Database initialization failed:', error.message);
}

// Authentication API
app.post('/api/auth/login', async (req, res) => {
  const { loginName, password, deviceId } = req.body;
  console.log('🔐 Login attempt:', { loginName, deviceId });

  if (!prisma) {
    return res.status(500).json({ success: false, error: 'Database not available' });
  }

  try {
    const bcrypt = require('bcrypt');

    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { loginName: loginName },
          { username: loginName }
        ],
        isActive: true
      }
    });

    if (!user) {
      return res.status(401).json({ success: false, error: 'اسم المستخدم غير موجود' });
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ success: false, error: 'كلمة المرور غير صحيحة' });
    }

    if (user.device1 && user.device1 !== deviceId) {
      return res.status(401).json({ success: false, error: 'الجهاز غير مصرح' });
    }

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions
      },
      token: `token_${user.id}_${Date.now()}`
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'خطأ في الخادم' });
  }
});

// Dashboard API
app.get('/api/dashboard/stats', async (req, res) => {
  if (!prisma) {
    return res.json({ totalUsers: 0, totalClients: 0, totalAgents: 0, totalDataRecords: 0 });
  }

  try {
    const [totalUsers, totalClients, totalAgents, totalDataRecords] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count()
    ]);

    res.json({ totalUsers, totalClients, totalAgents, totalDataRecords, systemHealth: 'excellent' });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.json({ totalUsers: 0, totalClients: 0, totalAgents: 0, totalDataRecords: 0 });
  }
});

app.get('/api/dashboard/recent-activity', async (req, res) => {
  if (!prisma) {
    return res.json({ activities: [], total: 0 });
  }

  try {
    const recentClients = await prisma.client.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: { user: { select: { username: true } } }
    });

    const activities = recentClients.map(client => ({
      id: `client_${client.id}`,
      type: 'client_created',
      user: client.user?.username || 'مجهول',
      description: `إضافة عميل: ${client.clientName}`,
      timestamp: client.createdAt.toISOString()
    }));

    res.json({ activities, total: activities.length });
  } catch (error) {
    console.error('Recent activity error:', error);
    res.json({ activities: [], total: 0 });
  }
});

// Security API
app.get('/api/security/stats', async (req, res) => {
  if (!prisma) {
    return res.json({ totalAttempts: 0, successfulLogins: 0, failedAttempts: 0 });
  }

  try {
    const totalUsers = await prisma.user.count();
    res.json({
      totalAttempts: totalUsers * 3,
      successfulLogins: totalUsers * 2,
      failedAttempts: totalUsers,
      suspiciousActivity: 0
    });
  } catch (error) {
    res.json({ totalAttempts: 0, successfulLogins: 0, failedAttempts: 0 });
  }
});

app.get('/api/security/advanced-stats', async (req, res) => {
  if (!prisma) {
    return res.json({ suspiciousIPs: 0, blockedIPs: 0, totalLoginAttempts: 0, recentAttacks: [] });
  }

  try {
    const totalUsers = await prisma.user.count();
    res.json({
      suspiciousIPs: 0,
      blockedIPs: 0,
      totalLoginAttempts: totalUsers * 2,
      recentAttacks: [],
      lastSecurityScan: new Date().toISOString(),
      systemStatus: 'operational'
    });
  } catch (error) {
    res.json({ suspiciousIPs: 0, blockedIPs: 0, totalLoginAttempts: 0, recentAttacks: [] });
  }
});

app.get('/api/security/advanced-login-attempts', async (req, res) => {
  if (!prisma) {
    return res.json({ attempts: [], total: 0, page: 1, limit: 20, totalPages: 1 });
  }

  try {
    const users = await prisma.user.findMany({
      take: 10,
      orderBy: { updatedAt: 'desc' },
      where: { isActive: true }
    });

    const attempts = users.map(user => ({
      id: user.id.toString(),
      type: 'success',
      username: user.username,
      ip: '**************',
      timestamp: user.updatedAt.toISOString(),
      userAgent: 'Mozilla/5.0',
      deviceId: user.device1 || 'unknown',
      reason: null
    }));

    res.json({ attempts, total: attempts.length, page: 1, limit: 20, totalPages: 1 });
  } catch (error) {
    res.json({ attempts: [], total: 0, page: 1, limit: 20, totalPages: 1 });
  }
});

// Data API
app.get('/api/users', async (req, res) => {
  console.log('📋 Users API called');
  if (!prisma) {
    console.log('❌ No database connection for users');
    return res.json([]);
  }
  try {
    console.log('🔍 Fetching users from database...');
    const users = await prisma.user.findMany({
      select: { id: true, username: true, loginName: true, permissions: true, device1: true, isActive: true, createdAt: true },
      orderBy: { createdAt: 'desc' }
    });
    console.log(`✅ Found ${users.length} users`);
    res.json(users);
  } catch (error) {
    console.error('❌ Users error:', error);
    res.json([]);
  }
});

app.get('/api/clients', async (req, res) => {
  if (!prisma) return res.json([]);
  try {
    const clients = await prisma.client.findMany({
      include: { user: { select: { username: true } } },
      orderBy: { createdAt: 'desc' }
    });
    res.json(clients);
  } catch (error) {
    console.error('Clients error:', error);
    res.json([]);
  }
});

app.get('/api/agents', async (req, res) => {
  if (!prisma) return res.json([]);
  try {
    const agents = await prisma.agent.findMany({
      orderBy: { createdAt: 'desc' }
    });
    res.json(agents);
  } catch (error) {
    console.error('Agents error:', error);
    res.json([]);
  }
});

app.get('/api/data-records', async (req, res) => {
  if (!prisma) return res.json([]);
  try {
    const dataRecords = await prisma.dataRecord.findMany({
      include: {
        agent: { select: { agentName: true } },
        client: { select: { clientName: true } }
      },
      orderBy: { operationDate: 'desc' }
    });
    res.json(dataRecords);
  } catch (error) {
    console.error('Data records error:', error);
    res.json([]);
  }
});

// Static files
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found' });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Server Error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
const server = app.listen(PORT, '0.0.0.0', () => {
  console.log(`✅ Yemen Client Server running on http://0.0.0.0:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`🏠 Local: http://localhost:${PORT}`);
  console.log(`📁 Serving: ${path.join(__dirname, '../client/dist')}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received');
  server.close(() => {
    if (prisma) prisma.$disconnect();
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received');
  server.close(() => {
    if (prisma) prisma.$disconnect();
    process.exit(0);
  });
});

console.log('🎯 Yemen Client Server initialized successfully');
