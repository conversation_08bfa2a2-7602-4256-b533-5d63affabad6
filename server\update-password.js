const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')
const prisma = new PrismaClient()

async function updatePassword() {
  try {
    const loginName = 'hash8080'
    const newPassword = 'hash8080'
    
    console.log(`🔧 تعديل كلمة المرور للمستخدم: ${loginName}`)
    console.log(`🔑 كلمة المرور الجديدة: ${newPassword}`)
    
    // البحث عن المستخدم أولاً
    const user = await prisma.user.findUnique({
      where: { loginName }
    })
    
    if (!user) {
      console.error(`❌ لم يتم العثور على المستخدم: ${loginName}`)
      return
    }
    
    console.log(`👤 تم العثور على المستخدم: ${user.username}`)
    
    // تشفير كلمة المرور الجديدة
    const hashedPassword = await bcrypt.hash(newPassword, 10)
    console.log(`🔐 تم تشفير كلمة المرور الجديدة`)
    
    // تحديث كلمة المرور
    await prisma.user.update({
      where: { loginName },
      data: { password: hashedPassword }
    })
    
    console.log(`✅ تم تحديث كلمة المرور بنجاح للمستخدم: ${user.username}`)
    console.log(`🔑 كلمة المرور الجديدة: ${newPassword}`)
    
  } catch (error) {
    console.error('❌ خطأ في تحديث كلمة المرور:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updatePassword()
