const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * إخفاء أسماء الملفات والمسارات في HTML
 */
function obfuscateHTML(htmlContent) {
  // إنشاء خريطة للأسماء المخفية
  const obfuscationMap = new Map();
  
  // توليد أسماء عشوائية
  const generateRandomName = (prefix = '') => {
    return prefix + crypto.randomBytes(8).toString('hex');
  };
  
  // إخفاء أسماء الملفات JS
  htmlContent = htmlContent.replace(/\/assets\/([^"']+\.js)/g, (match, filename) => {
    if (!obfuscationMap.has(filename)) {
      obfuscationMap.set(filename, generateRandomName('app-') + '.js');
    }
    return `/assets/${obfuscationMap.get(filename)}`;
  });
  
  // إخفاء أسماء الملفات CSS
  htmlContent = htmlContent.replace(/\/assets\/([^"']+\.css)/g, (match, filename) => {
    if (!obfuscationMap.has(filename)) {
      obfuscationMap.set(filename, generateRandomName('style-') + '.css');
    }
    return `/assets/${obfuscationMap.get(filename)}`;
  });
  
  // إزالة التعليقات
  htmlContent = htmlContent.replace(/<!--[\s\S]*?-->/g, '');
  
  // إزالة المسافات الزائدة
  htmlContent = htmlContent.replace(/\s+/g, ' ').trim();
  
  // إضافة تعليقات مضللة
  const misleadingComments = [
    '<!-- Generated by WordPress 5.8 -->',
    '<!-- Powered by Apache/2.4.41 -->',
    '<!-- PHP Version 7.4.3 -->',
    '<!-- MySQL Database -->',
    '<!-- Bootstrap 4.6.0 -->'
  ];
  
  const randomComment = misleadingComments[Math.floor(Math.random() * misleadingComments.length)];
  htmlContent = randomComment + '\n' + htmlContent;
  
  return htmlContent;
}

/**
 * إخفاء محتوى JavaScript
 */
function obfuscateJS(jsContent) {
  // إزالة console.log statements
  jsContent = jsContent.replace(/console\.(log|warn|error|info|debug)\([^)]*\);?/g, '');
  
  // إزالة التعليقات
  jsContent = jsContent.replace(/\/\*[\s\S]*?\*\//g, '');
  jsContent = jsContent.replace(/\/\/.*$/gm, '');
  
  // إضافة كود مضلل
  const misleadingCode = `
    // WordPress Core Functions
    var wp_version = "5.8.1";
    var php_version = "7.4.3";
    function wp_init() { return true; }
    function mysql_connect() { return "connected"; }
  `;
  
  return misleadingCode + '\n' + jsContent;
}

/**
 * إخفاء محتوى CSS
 */
function obfuscateCSS(cssContent) {
  // إزالة التعليقات
  cssContent = cssContent.replace(/\/\*[\s\S]*?\*\//g, '');
  
  // ضغط CSS
  cssContent = cssContent.replace(/\s+/g, ' ').trim();
  
  // إضافة تعليق مضلل
  const misleadingComment = '/* Bootstrap 4.6.0 | WordPress Theme */\n';
  
  return misleadingComment + cssContent;
}

/**
 * إنشاء خريطة الملفات المخفية
 */
function createObfuscationMap() {
  const map = new Map();
  
  // أسماء ملفات مضللة
  const fakeFiles = [
    'wp-admin.js',
    'jquery.min.js',
    'bootstrap.bundle.js',
    'wordpress-core.js',
    'mysql-connector.js',
    'php-handler.js',
    'apache-config.js'
  ];
  
  const fakeCSS = [
    'bootstrap.min.css',
    'wordpress-theme.css',
    'admin-style.css',
    'wp-content.css'
  ];
  
  return { fakeFiles, fakeCSS };
}

/**
 * إضافة headers مضللة للملفات
 */
function addMisleadingHeaders(res, filePath) {
  // إضافة headers مضللة
  res.setHeader('X-Powered-By', 'PHP/7.4.3');
  res.setHeader('Server', 'Apache/2.4.41 (Ubuntu)');
  res.setHeader('X-WordPress-Version', '5.8.1');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  
  // إضافة معلومات مضللة للملفات
  if (filePath.endsWith('.js')) {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Generator', 'WordPress 5.8.1');
  }
  
  if (filePath.endsWith('.css')) {
    res.setHeader('X-Theme', 'Twenty Twenty-One');
    res.setHeader('X-CSS-Framework', 'Bootstrap 4.6.0');
  }
}

/**
 * إنشاء ملفات وهمية للإرباك
 */
function createDecoyFiles() {
  const decoyFiles = [
    {
      path: '/wp-admin/admin.php',
      content: '<?php\n// WordPress Administration\nrequire_once("wp-config.php");\n?>'
    },
    {
      path: '/wp-config.php',
      content: '<?php\n// WordPress Configuration\ndefine("DB_NAME", "wordpress");\n?>'
    },
    {
      path: '/phpmyadmin/index.php',
      content: '<?php\n// phpMyAdmin\necho "Access Denied";\n?>'
    },
    {
      path: '/admin/config.php',
      content: '<?php\n// Admin Configuration\ndie("Forbidden");\n?>'
    }
  ];
  
  return decoyFiles;
}

/**
 * إزالة معلومات التطوير من الكود
 */
function removeDevInfo(content) {
  // إزالة أسماء المطورين
  content = content.replace(/\/\/ Created by .*/g, '');
  content = content.replace(/\/\* Author: .*/g, '');
  content = content.replace(/<AUTHOR> '');
  
  // إزالة معلومات النسخة
  content = content.replace(/version\s*[:=]\s*["'][\d.]+["']/gi, 'version: "1.0.0"');
  content = content.replace(/v\d+\.\d+\.\d+/g, 'v1.0.0');
  
  // إزالة مسارات التطوير
  content = content.replace(/\/src\//g, '/assets/');
  content = content.replace(/\/components\//g, '/modules/');
  content = content.replace(/\/utils\//g, '/helpers/');
  
  // إزالة أسماء المتغيرات المكشوفة
  content = content.replace(/yemclient/gi, 'webapp');
  content = content.replace(/yemen/gi, 'system');
  content = content.replace(/client/gi, 'user');
  content = content.replace(/agent/gi, 'partner');
  
  return content;
}

/**
 * إضافة بصمات مضللة
 */
function addFakeFingerprints(content) {
  const fakeFingerprints = [
    '/* jQuery v3.6.0 | (c) OpenJS Foundation */',
    '/* Bootstrap v4.6.0 | MIT License */',
    '/* WordPress v5.8.1 | GPL License */',
    '/* Font Awesome v5.15.4 | SIL OFL 1.1 */'
  ];
  
  const randomFingerprint = fakeFingerprints[Math.floor(Math.random() * fakeFingerprints.length)];
  return randomFingerprint + '\n' + content;
}

/**
 * تشفير أسماء المتغيرات والدوال
 */
function obfuscateVariables(jsContent) {
  // خريطة المتغيرات المشفرة
  const varMap = new Map();
  
  // توليد أسماء مشفرة
  const generateObfuscatedName = () => {
    const chars = 'abcdefghijklmnopqrstuvwxyz';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return '_' + result;
  };
  
  // البحث عن المتغيرات والدوال
  const patterns = [
    /\bfunction\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
    /\bvar\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
    /\blet\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
    /\bconst\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g
  ];
  
  patterns.forEach(pattern => {
    jsContent = jsContent.replace(pattern, (match, varName) => {
      if (!varMap.has(varName)) {
        varMap.set(varName, generateObfuscatedName());
      }
      return match.replace(varName, varMap.get(varName));
    });
  });
  
  return jsContent;
}

module.exports = {
  obfuscateHTML,
  obfuscateJS,
  obfuscateCSS,
  createObfuscationMap,
  addMisleadingHeaders,
  createDecoyFiles,
  removeDevInfo,
  addFakeFingerprints,
  obfuscateVariables
};
