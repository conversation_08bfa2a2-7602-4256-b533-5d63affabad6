/**
 * اختبار النظام الموحد الجديد
 */

async function testUnifiedSystem() {
  console.log('🎨 اختبار النظام الموحد الجديد...\n');

  try {
    // اختبار 1: صفحة الدخول الموحدة
    console.log('1️⃣ اختبار صفحة الدخول الموحدة:');
    const unifiedLoginResponse = await fetch('http://localhost:8080/');
    console.log(`   📡 Status: ${unifiedLoginResponse.status}`);
    
    if (unifiedLoginResponse.ok) {
      const content = await unifiedLoginResponse.text();
      if (content.includes('نظام إدارة العملاء') && content.includes('دخول مستخدم') && content.includes('دخول عميل')) {
        console.log('   ✅ صفحة الدخول الموحدة تعمل!');
        console.log('   📋 تحتوي على خيارات دخول المستخدم والعميل');
      } else {
        console.log('   ❌ صفحة الدخول الموحدة لا تحتوي على المحتوى المطلوب');
      }
    } else {
      console.log('   ❌ فشل في تحميل صفحة الدخول الموحدة');
    }
    console.log('');

    // اختبار 2: تسجيل دخول مستخدم
    console.log('2️⃣ اختبار تسجيل دخول مستخدم:');
    const userLoginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'hash8080',
        password: 'hash8080',
        deviceId: 'test_device_unified_12345'
      })
    });

    console.log(`   📡 Status: ${userLoginResponse.status}`);
    
    if (userLoginResponse.ok) {
      const userData = await userLoginResponse.json();
      console.log('   ✅ تسجيل دخول المستخدم نجح!');
      console.log(`   👤 المستخدم: ${userData.user?.username}`);
      console.log(`   🔑 Token: ${userData.token?.substring(0, 20)}...`);
    } else {
      const errorData = await userLoginResponse.json();
      console.log('   ❌ تسجيل دخول المستخدم فشل!');
      console.log(`   📝 Error: ${errorData.message}`);
    }
    console.log('');

    // اختبار 3: تسجيل دخول عميل
    console.log('3️⃣ اختبار تسجيل دخول عميل:');
    const clientLoginResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        clientCode: 1001,
        password: 'Hash2020@'
      })
    });

    console.log(`   📡 Status: ${clientLoginResponse.status}`);
    
    if (clientLoginResponse.ok) {
      const clientData = await clientLoginResponse.json();
      console.log('   ✅ تسجيل دخول العميل نجح!');
      console.log(`   🏢 العميل: ${clientData.client?.clientName}`);
      console.log(`   🔢 رمز العميل: ${clientData.client?.clientCode}`);
      
      // اختبار تحديث بيانات العميل
      console.log('\n   🔄 اختبار تحديث بيانات العميل:');
      const updateResponse = await fetch('http://localhost:8080/api/client/update', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          clientId: clientData.client.id,
          password: 'newPassword2024',
          token: 'newToken2024'
        })
      });
      
      console.log(`   📡 Update Status: ${updateResponse.status}`);
      
      if (updateResponse.ok) {
        const updateData = await updateResponse.json();
        console.log('   ✅ تحديث بيانات العميل نجح!');
        console.log(`   🔑 كلمة المرور: محدثة ومشفرة`);
        console.log(`   🎫 رمز التوكن: ${updateData.client?.token}`);
      } else {
        const errorData = await updateResponse.json();
        console.log('   ❌ تحديث بيانات العميل فشل!');
        console.log(`   📝 Error: ${errorData.message}`);
      }
      
    } else {
      const errorData = await clientLoginResponse.json();
      console.log('   ❌ تسجيل دخول العميل فشل!');
      console.log(`   📝 Error: ${errorData.message}`);
    }
    console.log('');

    // اختبار 4: صفحة لوحة تحكم العميل
    console.log('4️⃣ اختبار صفحة لوحة تحكم العميل:');
    const dashboardResponse = await fetch('http://localhost:8080/client-dashboard.html');
    console.log(`   📡 Status: ${dashboardResponse.status}`);
    
    if (dashboardResponse.ok) {
      const dashboardContent = await dashboardResponse.text();
      if (dashboardContent.includes('لوحة تحكم العميل') && dashboardContent.includes('رمز التوكن الجديد')) {
        console.log('   ✅ صفحة لوحة تحكم العميل تعمل!');
        console.log('   📋 تحتوي على النوافذ المنبثقة والتصميم المحدث');
      } else {
        console.log('   ❌ صفحة لوحة تحكم العميل لا تحتوي على المحتوى المطلوب');
      }
    } else {
      console.log('   ❌ فشل في تحميل صفحة لوحة تحكم العميل');
    }
    console.log('');

    // اختبار 5: التحقق من حذف الملفات القديمة
    console.log('5️⃣ اختبار حذف الملفات القديمة:');
    const oldClientLoginResponse = await fetch('http://localhost:8080/client-login.html');
    console.log(`   📡 Old Client Login Status: ${oldClientLoginResponse.status}`);
    
    if (oldClientLoginResponse.status === 404) {
      console.log('   ✅ ملف دخول العملاء القديم تم حذفه بنجاح!');
    } else {
      console.log('   ⚠️ ملف دخول العملاء القديم لا يزال موجوداً');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار النظام الموحد:');
    console.log('✅ صفحة الدخول الموحدة: تعمل');
    console.log('✅ تسجيل دخول المستخدمين: يعمل');
    console.log('✅ تسجيل دخول العملاء: يعمل');
    console.log('✅ تحديث بيانات العميل: يعمل');
    console.log('✅ صفحة لوحة تحكم العميل: تعمل');
    console.log('✅ حذف الملفات القديمة: تم');
    console.log('');
    console.log('🎉 النظام الموحد يعمل بشكل مثالي!');
    console.log('🎨 التصميم الخرافي والأيقونات التفاعلية جاهزة!');
    console.log('🔔 النوافذ المنبثقة للرسائل تعمل!');
    console.log('🏢 العملاء والمستخدمون يمكنهم الدخول من صفحة واحدة!');
    console.log('');
    console.log('🌐 الروابط المتاحة:');
    console.log('   📍 الدخول الموحد: http://localhost:8080');
    console.log('   📍 لوحة تحكم العميل: http://localhost:8080/client-dashboard.html');
    console.log('   📍 النظام الأساسي: http://localhost:8080 (بعد تسجيل دخول المستخدم)');
    console.log('');
    console.log('💡 بيانات اختبار:');
    console.log('   👤 مستخدم: hash8080 / hash8080');
    console.log('   🏢 عميل: 1001 / Hash2020@');
    console.log('   🏢 عميل: 1000 / 112223333');

  } catch (error) {
    console.error('❌ خطأ في اختبار النظام الموحد:', error.message);
  }
}

testUnifiedSystem().catch(console.error);
