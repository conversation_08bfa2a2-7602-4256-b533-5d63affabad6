<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 اختبار صفحة البيانات مباشر</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #2196f3;
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        
        button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        
        button:hover {
            background: #45a049;
        }
        
        .result {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        .data-table th {
            background: #4caf50;
            color: white;
        }
        
        .data-table tr:nth-child(even) {
            background: #f2f2f2;
        }
        
        .status-success {
            background: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .status-failed {
            background: #f44336;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .error {
            color: #f44336;
            font-weight: bold;
        }
        
        .success {
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 اختبار صفحة البيانات مباشر</h1>
            <p>فحص API البيانات وعرض السجلات من جدول data_records</p>
        </div>

        <!-- اختبار API -->
        <div class="test-section">
            <h3>🧪 اختبار API البيانات</h3>
            <button onclick="testDataAPI()">📡 اختبار /api/data-records</button>
            <button onclick="testWithAuth()">🔐 اختبار مع Authentication</button>
            <button onclick="clearResults()">🗑️ مسح النتائج</button>
            
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <!-- عرض البيانات -->
        <div class="test-section" id="dataSection" style="display: none;">
            <h3>📋 سجلات البيانات الحقيقية</h3>
            <p id="dataInfo"></p>
            <table class="data-table" id="dataTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>رقم الوكيل</th>
                        <th>اسم الوكيل</th>
                        <th>رمز العميل</th>
                        <th>اسم العميل</th>
                        <th>تاريخ العملية</th>
                        <th>الحالة</th>
                        <th>IP العميل</th>
                        <th>مرجع الوكيل</th>
                    </tr>
                </thead>
                <tbody id="dataBody">
                </tbody>
            </table>
        </div>

        <!-- تشخيص المشكلة -->
        <div class="test-section">
            <h3>🔍 تشخيص المشكلة</h3>
            <div id="diagnosis"></div>
        </div>
    </div>

    <script>
        let allData = null;

        // اختبار API البيانات
        async function testDataAPI() {
            const resultDiv = document.getElementById('apiResult');
            const dataSection = document.getElementById('dataSection');
            const diagnosis = document.getElementById('diagnosis');
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'جاري الاختبار...';
            
            try {
                console.log('🧪 بدء اختبار API البيانات...');
                
                const response = await fetch('/api/data-records?page=1&limit=20');
                
                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', response.headers);
                
                let result = `📡 HTTP Status: ${response.status}\n`;
                result += `📡 Content-Type: ${response.headers.get('content-type')}\n\n`;
                
                if (response.ok) {
                    const data = await response.json();
                    allData = data;
                    
                    result += `✅ تم جلب البيانات بنجاح!\n\n`;
                    result += `📊 إجمالي السجلات: ${data.total}\n`;
                    result += `📄 الصفحة: ${data.pagination?.page}\n`;
                    result += `📋 السجلات في هذه الصفحة: ${data.dataRecords?.length}\n\n`;
                    
                    if (data.dataRecords && data.dataRecords.length > 0) {
                        result += `📋 أول 3 سجلات:\n`;
                        result += `${'='.repeat(50)}\n`;
                        
                        data.dataRecords.slice(0, 3).forEach((record, index) => {
                            result += `${index + 1}. ID: ${record.id}\n`;
                            result += `   الوكيل: ${record.agentName} (ID: ${record.agentId})\n`;
                            result += `   العميل: ${record.clientName} (${record.clientCode})\n`;
                            result += `   التاريخ: ${new Date(record.operationDate).toLocaleString('ar-EG')}\n`;
                            result += `   الحالة: ${record.operationStatus === 1 ? 'نجح' : 'فشل'}\n`;
                            result += `   IP: ${record.clientIpAddress}\n`;
                            result += `   مرجع الوكيل: ${record.agentReference}\n\n`;
                        });
                        
                        // عرض البيانات في الجدول
                        displayDataTable(data.dataRecords);
                        dataSection.style.display = 'block';
                        
                        // تشخيص إيجابي
                        diagnosis.innerHTML = `
                            <div class="success">✅ API البيانات يعمل بشكل ممتاز!</div>
                            <p>📊 البيانات متاحة وحقيقية من قاعدة البيانات</p>
                            <p>🔍 إذا لم تظهر البيانات في صفحة النظام الأساسية، المشكلة في:</p>
                            <ul>
                                <li>🔐 Authentication - تأكد من تسجيل الدخول</li>
                                <li>🌐 CORS - تأكد من إعدادات CORS</li>
                                <li>💻 Frontend - تحقق من أخطاء JavaScript في المتصفح</li>
                                <li>📱 React Query - تحقق من cache أو stale data</li>
                            </ul>
                        `;
                        
                    } else {
                        result += `⚠️ لا توجد سجلات في قاعدة البيانات\n`;
                        diagnosis.innerHTML = `
                            <div class="error">⚠️ لا توجد بيانات في جدول data_records</div>
                            <p>قم بإضافة بعض السجلات أولاً</p>
                        `;
                    }
                    
                } else {
                    const errorText = await response.text();
                    result += `❌ فشل في جلب البيانات!\n`;
                    result += `📝 الخطأ: ${errorText}\n`;
                    
                    diagnosis.innerHTML = `
                        <div class="error">❌ API البيانات لا يعمل</div>
                        <p>Status: ${response.status}</p>
                        <p>Error: ${errorText}</p>
                    `;
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                console.error('❌ خطأ في الاختبار:', error);
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
                
                diagnosis.innerHTML = `
                    <div class="error">❌ خطأ في الاتصال</div>
                    <p>تأكد من أن الخادم يعمل على المنفذ 8080</p>
                    <p>Error: ${error.message}</p>
                `;
            }
        }

        // اختبار مع Authentication
        async function testWithAuth() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = 'جاري اختبار Authentication...';
            
            try {
                // محاولة تسجيل دخول تجريبي
                const loginResponse = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        loginName: 'admin',
                        password: 'admin123',
                        deviceId: 'test_device_' + Date.now()
                    })
                });
                
                let result = `🔐 اختبار Authentication:\n`;
                result += `📡 Login Status: ${loginResponse.status}\n\n`;
                
                if (loginResponse.ok) {
                    const loginData = await loginResponse.json();
                    result += `✅ تسجيل الدخول نجح!\n`;
                    result += `👤 المستخدم: ${loginData.user?.username}\n\n`;
                    
                    // الآن اختبار API البيانات مع token
                    const token = loginData.token;
                    const dataResponse = await fetch('/api/data-records', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });
                    
                    result += `📊 Data API Status: ${dataResponse.status}\n`;
                    
                    if (dataResponse.ok) {
                        const data = await dataResponse.json();
                        result += `✅ API البيانات يعمل مع Authentication!\n`;
                        result += `📋 عدد السجلات: ${data.dataRecords?.length}\n`;
                    } else {
                        result += `❌ API البيانات فشل مع Authentication\n`;
                    }
                    
                } else {
                    const errorData = await loginResponse.json();
                    result += `❌ تسجيل الدخول فشل!\n`;
                    result += `📝 الخطأ: ${errorData.error}\n`;
                }
                
                resultDiv.textContent = result;
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في اختبار Authentication: ${error.message}`;
            }
        }

        // عرض البيانات في الجدول
        function displayDataTable(records) {
            const dataBody = document.getElementById('dataBody');
            const dataInfo = document.getElementById('dataInfo');
            
            dataInfo.innerHTML = `📊 عرض ${records.length} سجل من إجمالي ${allData.total} سجل`;
            
            dataBody.innerHTML = '';
            
            records.forEach(record => {
                const row = document.createElement('tr');
                const date = new Date(record.operationDate).toLocaleDateString('ar-EG');
                const statusClass = record.operationStatus === 1 ? 'status-success' : 'status-failed';
                const statusText = record.operationStatus === 1 ? 'نجح' : 'فشل';
                
                row.innerHTML = `
                    <td>${record.id}</td>
                    <td>${record.agentId}</td>
                    <td>${record.agentName}</td>
                    <td>${record.clientCode}</td>
                    <td>${record.clientName}</td>
                    <td>${date}</td>
                    <td><span class="${statusClass}">${statusText}</span></td>
                    <td>${record.clientIpAddress}</td>
                    <td>${record.agentReference}</td>
                `;
                
                dataBody.appendChild(row);
            });
        }

        // مسح النتائج
        function clearResults() {
            document.getElementById('apiResult').style.display = 'none';
            document.getElementById('dataSection').style.display = 'none';
            document.getElementById('diagnosis').innerHTML = '';
        }

        // تشغيل الاختبار تلقائياً
        window.onload = function() {
            setTimeout(testDataAPI, 1000);
        };
    </script>
</body>
</html>
