import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'
import LoginPage from './pages/LoginPage'
import ClientDashboard from './pages/ClientDashboard'
import LucidArabicLayout from './components/layout/LucidArabicLayout'
import Dashboard from './pages/Dashboard'
import ClientsPage from './pages/ClientsPage'
import AgentsPage from './pages/AgentsPage'
import UsersPage from './pages/UsersPage'
import SecurityPage from './pages/SecurityPage'
import DataRecordsPage from './pages/DataRecordsPage'
import TestIcons from './pages/TestIcons'
import TestButtons from './pages/TestButtons'
import TestPermissions from './pages/TestPermissions'
import ClientLoginTest from './pages/ClientLoginTest'
import LoadingSpinner from './components/common/LoadingSpinner'
import SessionStatus from './components/SessionStatus'
import { useUrlHider } from './utils/UrlHider'
import { useSessionTimeout } from './hooks/useSessionTimeout'
import { useEffect } from 'react'

function App() {
  const { user, loading } = useAuth()
  const { activate } = useUrlHider()

  // تفعيل نظام انتهاء الجلسة للمستخدمين المسجلين (لا يؤثر على العملاء)
  useSessionTimeout()

  // تفعيل إخفاء URL
  useEffect(() => {
    activate()
  }, [activate])

  if (loading) {
    return <LoadingSpinner />
  }

  if (!user) {
    return (
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/client-dashboard" element={<ClientDashboard />} />
        <Route path="/client-login-test" element={<ClientLoginTest />} />
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
    )
  }

  // إذا كان المستخدم عميل، توجيهه لصفحة العميل
  if (user && user.isClient) {
    return (
      <Routes>
        <Route path="/client-dashboard" element={<ClientDashboard />} />
        <Route path="/client-login-test" element={<ClientLoginTest />} />
        <Route path="*" element={<Navigate to="/client-dashboard" replace />} />
      </Routes>
    )
  }

  return (
    <>
      <SessionStatus />
      <LucidArabicLayout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/clients" element={<ClientsPage />} />
          <Route path="/agents" element={<AgentsPage />} />
          <Route path="/users" element={<UsersPage />} />
          <Route path="/data-records" element={<DataRecordsPage />} />
          <Route path="/security" element={<SecurityPage />} />
          <Route path="/test-icons" element={<TestIcons />} />
          <Route path="/test-buttons" element={<TestButtons />} />
          <Route path="/test-permissions" element={<TestPermissions />} />
          <Route path="/client-login-test" element={<ClientLoginTest />} />
        <Route path="/login" element={<Navigate to="/" replace />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </LucidArabicLayout>
    </>
  )
}

export default App
