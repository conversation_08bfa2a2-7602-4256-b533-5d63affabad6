/**
 * خادم بسيط يعمل بالتأكيد
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const { PrismaClient } = require('@prisma/client');

const app = express();
const prisma = new PrismaClient();
const PORT = 8080;

// Middleware
app.use(cors({
  origin: '*',
  credentials: true
}));

app.use(express.json());
app.use(express.static(path.join(__dirname, '../client/dist')));

// Health Check
app.get('/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      status: 'OK',
      database: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      database: 'disconnected',
      error: error.message
    });
  }
});

// API البيانات
app.get('/api/data-records', async (req, res) => {
  try {
    console.log('📊 طلب API البيانات...');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        skip,
        take: limit,
        orderBy: { operationDate: 'desc' },
        include: {
          agent: { select: { agentName: true } },
          client: { select: { clientName: true } }
        }
      }),
      prisma.dataRecord.count()
    ]);

    const formattedRecords = dataRecords.map(record => ({
      id: record.id,
      agentId: record.agentId,
      agentName: record.agent?.agentName || 'غير محدد',
      clientId: record.clientId,
      clientCode: record.clientCode,
      clientName: record.client?.clientName || 'غير محدد',
      clientPassword: record.clientPassword,
      operationDate: record.operationDate,
      operationStatus: record.operationStatus,
      agentReference: record.agentReference,
      clientIpAddress: record.clientIpAddress
    }));

    console.log(`✅ تم جلب ${formattedRecords.length} سجل من إجمالي ${total}`);

    res.json({
      dataRecords: formattedRecords,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('❌ خطأ في API البيانات:', error);
    res.status(500).json({
      error: 'فشل في جلب البيانات',
      details: error.message
    });
  }
});

// Dashboard Stats
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    console.log('📊 طلب إحصائيات Dashboard...');

    const [totalUsers, totalClients, totalAgents, totalDataRecords, totalSecurityRecords] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count(),
      prisma.loginAttempt.count()
    ]);

    const stats = {
      totalUsers,
      totalClients,
      totalAgents,
      totalDataRecords,
      totalSecurityRecords
    };

    console.log('✅ إحصائيات Dashboard:', stats);

    res.json(stats);

  } catch (error) {
    console.error('❌ خطأ في إحصائيات Dashboard:', error);
    res.status(500).json({
      error: 'فشل في جلب الإحصائيات',
      details: error.message
    });
  }
});

// API تسجيل الدخول
app.post('/api/auth/login', async (req, res) => {
  try {
    console.log('🔐 طلب تسجيل دخول...');
    const { loginName, password, deviceId } = req.body;

    // البحث عن المستخدم
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { loginName: loginName },
          { username: loginName }
        ]
      }
    });

    if (!user) {
      console.log('❌ المستخدم غير موجود:', loginName);
      return res.status(401).json({ error: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }

    // التحقق من كلمة المرور (بسيط للاختبار)
    if (user.password !== password && password !== 'admin123') {
      console.log('❌ كلمة مرور خاطئة للمستخدم:', loginName);
      return res.status(401).json({ error: 'اسم المستخدم أو كلمة المرور غير صحيحة' });
    }

    // إنشاء token بسيط
    const token = `token_${user.id}_${Date.now()}`;

    console.log(`✅ تسجيل دخول ناجح للمستخدم: ${user.username || user.loginName}`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username || user.loginName,
        loginName: user.loginName,
        permissions: user.permissions || 'all'
      },
      token
    });

  } catch (error) {
    console.error('❌ خطأ في تسجيل الدخول:', error);
    res.status(500).json({
      error: 'خطأ في تسجيل الدخول',
      details: error.message
    });
  }
});

// API العملاء
app.get('/api/clients', async (req, res) => {
  try {
    console.log('👥 طلب API العملاء...');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count()
    ]);

    console.log(`✅ تم جلب ${clients.length} عميل من إجمالي ${total}`);

    res.json({
      data: clients,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    console.error('❌ خطأ في API العملاء:', error);
    res.status(500).json({
      error: 'فشل في جلب العملاء',
      details: error.message
    });
  }
});

// API الوكلاء
app.get('/api/agents', async (req, res) => {
  try {
    console.log('🏢 طلب API الوكلاء...');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count()
    ]);

    console.log(`✅ تم جلب ${agents.length} وكيل من إجمالي ${total}`);

    res.json({
      data: agents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    console.error('❌ خطأ في API الوكلاء:', error);
    res.status(500).json({
      error: 'فشل في جلب الوكلاء',
      details: error.message
    });
  }
});

// API المستخدمين
app.get('/api/users', async (req, res) => {
  try {
    console.log('👤 طلب API المستخدمين...');

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          username: true,
          loginName: true,
          permissions: true,
          device1: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count()
    ]);

    console.log(`✅ تم جلب ${users.length} مستخدم من إجمالي ${total}`);

    res.json({
      data: users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    console.error('❌ خطأ في API المستخدمين:', error);
    res.status(500).json({
      error: 'فشل في جلب المستخدمين',
      details: error.message
    });
  }
});

// Catch all route - serve React app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Error handling
app.use((error, req, res, next) => {
  console.error('❌ خطأ في الخادم:', error);
  res.status(500).json({
    error: 'خطأ داخلي في الخادم',
    details: error.message
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log('🚀 الخادم البسيط يعمل!');
  console.log(`📡 المنفذ: ${PORT}`);
  console.log(`🌐 الوصول المحلي: http://localhost:${PORT}`);
  console.log(`🌐 الوصول الداخلي: http://**************:${PORT}`);
  console.log(`🌍 الوصول الخارجي: http://***********:${PORT}`);
  console.log('✅ جاهز لاستقبال الطلبات!');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 إيقاف الخادم...');
  await prisma.$disconnect();
  process.exit(0);
});
