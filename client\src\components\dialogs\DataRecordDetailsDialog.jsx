import React from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Typography,
  Box,
  Chip,
  Divider,
  Card,
  CardContent,
  IconButton
} from '@mui/material'
import {
  Person as PersonIcon,
  Business as BusinessIcon,
  Computer as ComputerIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Fingerprint as FingerprintIcon
} from '@mui/icons-material'

const DataRecordDetailsDialog = ({ open, onClose, record }) => {
  if (!record) return null

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-EG', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const getStatusColor = (status) => {
    return status === 1 ? 'success' : 'error'
  }

  const getStatusIcon = (status) => {
    return status === 1 ? <CheckCircleIcon /> : <CancelIcon />
  }

  const getStatusText = (status) => {
    return status === 1 ? 'ناجحة' : 'فاشلة'
  }

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)'
        }
      }}
    >
      <DialogTitle sx={{ 
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        fontSize: '1.5rem',
        fontWeight: 'bold'
      }}>
        <span>📊 تفاصيل سجل البيانات</span>
        <IconButton onClick={onClose} size="small" sx={{ color: 'white' }}>
          <span style={{ fontSize: '20px' }}>✕</span>
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* معلومات السجل الأساسية */}
          <Grid item xs={12}>
            <Card variant="outlined" sx={{ mb: 2 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FingerprintIcon color="primary" />
                  معلومات السجل
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={2}>
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      معرف السجل:
                    </Typography>
                    <Typography variant="h6" color="primary">
                      #{record.id}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      رقم مرجع الوكيل:
                    </Typography>
                    <Chip 
                      label={record.agentReference} 
                      color="primary" 
                      variant="outlined"
                    />
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      حالة العملية:
                    </Typography>
                    <Chip 
                      label={getStatusText(record.operationStatus)}
                      color={getStatusColor(record.operationStatus)}
                      icon={getStatusIcon(record.operationStatus)}
                    />
                  </Grid>
                  
                  <Grid item xs={6} md={3}>
                    <Typography variant="body2" color="text.secondary">
                      تاريخ العملية:
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ScheduleIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        {formatDate(record.operationDate)}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات الوكيل */}
          {record.agent && (
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <BusinessIcon color="primary" />
                    معلومات الوكيل
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        اسم الوكيل:
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {record.agent.agentName}
                      </Typography>
                    </Box>
                    
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        اسم الوكالة:
                      </Typography>
                      <Typography variant="body1">
                        {record.agent.agencyName}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* معلومات العميل */}
          {record.client && (
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PersonIcon color="secondary" />
                    معلومات العميل
                  </Typography>
                  <Divider sx={{ mb: 2 }} />
                  
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        اسم العميل:
                      </Typography>
                      <Typography variant="body1" fontWeight="medium">
                        {record.client.clientName}
                      </Typography>
                    </Box>
                    
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        رمز العميل:
                      </Typography>
                      <Chip 
                        label={record.client.clientCode} 
                        color="info" 
                        variant="outlined" 
                        size="small"
                      />
                    </Box>
                    
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        اسم التطبيق:
                      </Typography>
                      <Typography variant="body1">
                        {record.client.appName}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          )}

          {/* بيانات العميل المحفوظة */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ComputerIcon color="info" />
                  بيانات العميل المحفوظة وقت العملية
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      رمز العميل:
                    </Typography>
                    <Typography variant="body1" fontWeight="medium">
                      {record.clientCode}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      كلمة المرور:
                    </Typography>
                    <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                      {'*'.repeat(record.clientPassword?.length || 0)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      عنوان IP:
                    </Typography>
                    <Typography variant="body1" sx={{ fontFamily: 'monospace' }}>
                      {record.clientIpAddress}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* معلومات التوقيت */}
          <Grid item xs={12}>
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ScheduleIcon color="warning" />
                  معلومات التوقيت
                </Typography>
                <Divider sx={{ mb: 2 }} />
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      تاريخ الإنشاء:
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(record.createdAt)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      تاريخ التحديث:
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(record.updatedAt)}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12} md={4}>
                    <Typography variant="body2" color="text.secondary">
                      تاريخ العملية:
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(record.operationDate)}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 0 }}>
        <Button onClick={onClose} variant="contained" color="primary">
          إغلاق
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default DataRecordDetailsDialog
