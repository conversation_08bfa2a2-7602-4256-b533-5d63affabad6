const { PrismaClient } = require('./server/node_modules/@prisma/client')

const prisma = new PrismaClient()

async function addSequentialNumbers() {
  try {
    console.log('🔧 إضافة أرقام تسلسلية للوكلاء...')
    
    // جلب الوكلاء مرتبين حسب تاريخ الإنشاء
    const agents = await prisma.agent.findMany({
      orderBy: { createdAt: 'asc' },
      select: {
        id: true,
        agentName: true,
        agencyName: true,
        createdAt: true
      }
    })

    console.log('📊 الوكلاء الحاليون:')
    
    for (let i = 0; i < agents.length; i++) {
      const agent = agents[i]
      const sequentialNumber = i + 1
      
      console.log(`${sequentialNumber}. ${agent.agentName} (ID: ${agent.id})`)
      
      // يمكن إضافة حقل agentNumber في قاعدة البيانات
      // await prisma.agent.update({
      //   where: { id: agent.id },
      //   data: { agentNumber: sequentialNumber }
      // })
    }

    console.log('')
    console.log('💡 اقتراح: إضافة حقل agentNumber في قاعدة البيانات')
    console.log('💡 هذا سيحافظ على ID الأصلي ويضيف رقم تسلسلي')

  } catch (error) {
    console.error('❌ خطأ:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addSequentialNumbers()
