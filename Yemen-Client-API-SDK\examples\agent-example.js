// مثال للوكلاء
const YemenClientAgentAPI = require('../YemenClientAPI-Agent.js')

async function main() {
  const agent = new YemenClientAgentAPI(
    'http://185.11.8.26:8080',
    'your_agent_login',
    'your_agent_password'
  )

  try {
    await agent.authenticate()
    const result = await agent.verifyClient('1000', 'ABC12345')
    console.log('Result:', result)
    await agent.logout()
  } catch (error) {
    console.error('Error:', error.message)
  }
}

main()
