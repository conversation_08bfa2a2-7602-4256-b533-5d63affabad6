const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function updateHashdiToDevice1() {
  try {
    console.log('🔄 تحديث المستخدم محمد الحاشدي للنظام الجديد')
    console.log('==========================================')
    
    // البحث عن المستخدم محمد الحاشدي
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        device1: true,
        isActive: true
      }
    })

    if (!user) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }
    
    console.log(`👤 المستخدم: ${user.username}`)
    console.log(`📱 deviceId (قديم): ${user.deviceId || 'فارغ'}`)
    console.log(`📱 device1 (جديد): ${user.device1 || 'فارغ'}`)
    
    // الجهاز المطلوب
    const primaryDevice = 'honbi5nms_1751046183491'
    
    console.log(`📱 الجهاز المطلوب: ${primaryDevice}`)
    
    // تحديث المستخدم
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { 
        device1: primaryDevice,  // الجهاز الجديد في العمود الجديد
        isActive: true
      }
    })
    
    console.log('✅ تم تحديث المستخدم بنجاح!')
    
    // عرض النتيجة
    console.log('')
    console.log('📊 النتيجة بعد التحديث:')
    console.log(`👤 المستخدم: ${updatedUser.username}`)
    console.log(`📱 deviceId (قديم): ${updatedUser.deviceId || 'فارغ'}`)
    console.log(`📱 device1 (جديد): ${updatedUser.device1 || 'فارغ'}`)
    console.log(`✅ نشط: ${updatedUser.isActive ? 'نعم' : 'لا'}`)
    
    // اختبار تسجيل الدخول
    console.log('')
    console.log('🧪 اختبار تسجيل الدخول:')
    
    try {
      const response = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          loginName: 'hash8080',
          password: 'hash8080',
          deviceId: primaryDevice
        })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        console.log('✅ تسجيل الدخول نجح!')
        console.log(`🔑 التوكن: ${result.token ? 'تم إنشاؤه بنجاح' : 'لم يتم إنشاؤه'}`)
      } else {
        console.log('❌ تسجيل الدخول فشل!')
        console.log(`📝 السبب: ${result.error || 'غير محدد'}`)
        console.log(`📝 التفاصيل: ${result.message || 'غير متوفرة'}`)
      }
      
    } catch (error) {
      console.log(`❌ خطأ في اختبار تسجيل الدخول: ${error.message}`)
    }
    
    console.log('')
    console.log('🎉 تم التحديث بنجاح!')
    console.log('📝 النظام الآن يدعم:')
    console.log('   ✅ العمود الجديد device1 (جهاز واحد)')
    console.log('   ✅ العمود القديم deviceId (للتوافق)')
    console.log('   ✅ تسجيل الدخول من أي من العمودين')
    
    console.log('')
    console.log('🧪 أوامر الاختبار:')
    console.log('# تسجيل الدخول:')
    console.log(`curl -X POST http://localhost:8080/api/auth/login \\`)
    console.log(`  -H "Content-Type: application/json" \\`)
    console.log(`  -d '{"loginName":"hash8080","password":"hash8080","deviceId":"${primaryDevice}"}'`)
    
  } catch (error) {
    console.error('❌ خطأ في التحديث:', error)
  } finally {
    await prisma.$disconnect()
  }
}

updateHashdiToDevice1()
