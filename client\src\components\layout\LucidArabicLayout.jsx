import React, { useState, useEffect } from 'react'
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  List,
  Typography,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  Grid
} from '@mui/material'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'
import UserProfileDialog from '../dialogs/UserProfileDialog'

const drawerWidth = 300

const LucidArabicLayout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(true) // حالة إظهار/إخفاء القائمة الجانبية
  const [anchorEl, setAnchorEl] = useState(null)
  const [profileDialogOpen, setProfileDialogOpen] = useState(false) // حالة نافذة الملف الشخصي
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout, hasPermission } = useAuth()

  // إجبار التحديث والأنماط
  useEffect(() => {
    console.log('🎨 تحميل تصميم Lucid العربي')

    // إضافة أنماط CSS مباشرة للجسم
    document.body.style.direction = 'rtl'
    document.body.style.textAlign = 'right'
    document.documentElement.style.direction = 'rtl'
    document.body.style.backgroundColor = '#f8f9fa'

    return () => {
      document.body.style.backgroundColor = ''
    }
  }, [])

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleProfileOpen = () => {
    setProfileDialogOpen(true)
    handleClose()
  }

  const handleLogout = () => {
    logout()
    handleClose()
  }



  // قوائم النظام مع أيقونات 3D احترافية
  const systemMenus = [
    {
      text: 'لوحة التحكم',
      icon: '📊',
      path: '/dashboard',
      permission: null,
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      shadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
    },
    {
      text: 'العملاء',
      icon: '👥',
      path: '/clients',
      permission: { resource: 'clients', action: 'read' },
      gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      shadow: '0 8px 32px rgba(240, 147, 251, 0.3)'
    },
    {
      text: 'الوكلاء',
      icon: '🏢',
      path: '/agents',
      permission: { resource: 'agents', action: 'read' },
      gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      shadow: '0 8px 32px rgba(79, 172, 254, 0.3)'
    },
    {
      text: 'المستخدمين',
      icon: '👤',
      path: '/users',
      permission: { resource: 'users', action: 'read' },
      gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      shadow: '0 8px 32px rgba(67, 233, 123, 0.3)'
    },
    {
      text: 'البيانات',
      icon: '📋',
      path: '/data-records',
      permission: null,
      gradient: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      shadow: '0 8px 32px rgba(250, 112, 154, 0.3)'
    },
    {
      text: 'الأمان',
      icon: '🔒',
      path: '/security',
      permission: { resource: 'security', action: 'read' },
      gradient: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      shadow: '0 8px 32px rgba(168, 237, 234, 0.3)'
    }
  ]

  const getPageTitle = () => {
    const currentItem = systemMenus.find(item =>
      item.path === location.pathname
    )
    return currentItem?.text || 'لوحة التحكم'
  }

  // مكون القائمة الجانبية
  const drawer = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', direction: 'rtl' }}>
      {/* رأس القائمة - معلومات المستخدم مع تصميم 3D */}
      <Box sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white',
        p: 3,
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%)',
          pointerEvents: 'none'
        }
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          mb: 3,
          pb: 3,
          borderBottom: '1px solid rgba(255,255,255,0.2)',
          position: 'relative',
          zIndex: 1
        }}>
          <Avatar sx={{
            width: 64,
            height: 64,
            ml: 2,
            background: 'linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%)',
            fontSize: '28px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.3)',
            border: '2px solid rgba(255,255,255,0.2)',
            fontFamily: 'Khalid-Art-bold, sans-serif'
          }}>
            👤
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="body2" sx={{
              opacity: 0.9,
              fontFamily: 'Khalid-Art-bold, sans-serif',
              fontSize: '14px'
            }}>
              مرحباً،
            </Typography>
            <Typography variant="h6" sx={{
              fontWeight: 700,
              fontFamily: 'Khalid-Art-bold, sans-serif',
              fontSize: '18px',
              textShadow: '0 2px 4px rgba(0,0,0,0.3)'
            }}>
              {user?.username || 'مستخدم'}
            </Typography>
          </Box>
        </Box>

        {/* إحصائيات سريعة مع تأثيرات 3D */}
        <Grid container spacing={2} sx={{ position: 'relative', zIndex: 1 }}>
          <Grid item xs={4}>
            <Box sx={{
              textAlign: 'center',
              p: 1,
              borderRadius: 2,
              background: 'rgba(255,255,255,0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 24px rgba(0,0,0,0.3)'
              }
            }}>
              <Typography variant="h6" sx={{
                fontWeight: 700,
                fontFamily: 'Khalid-Art-bold, sans-serif',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}>5+</Typography>
              <Typography variant="caption" sx={{
                opacity: 0.9,
                fontFamily: 'Khalid-Art-bold, sans-serif',
                fontSize: '11px'
              }}>سنوات خبرة</Typography>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{
              textAlign: 'center',
              p: 1,
              borderRadius: 2,
              background: 'rgba(255,255,255,0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 24px rgba(0,0,0,0.3)'
              }
            }}>
              <Typography variant="h6" sx={{
                fontWeight: 700,
                fontFamily: 'Khalid-Art-bold, sans-serif',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}>400+</Typography>
              <Typography variant="caption" sx={{
                opacity: 0.9,
                fontFamily: 'Khalid-Art-bold, sans-serif',
                fontSize: '11px'
              }}>عميل</Typography>
            </Box>
          </Grid>
          <Grid item xs={4}>
            <Box sx={{
              textAlign: 'center',
              p: 1,
              borderRadius: 2,
              background: 'rgba(255,255,255,0.1)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.2)',
              boxShadow: '0 4px 16px rgba(0,0,0,0.2)',
              transition: 'all 0.3s ease',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 24px rgba(0,0,0,0.3)'
              }
            }}>
              <Typography variant="h6" sx={{
                fontWeight: 700,
                fontFamily: 'Khalid-Art-bold, sans-serif',
                textShadow: '0 2px 4px rgba(0,0,0,0.3)'
              }}>80+</Typography>
              <Typography variant="caption" sx={{
                opacity: 0.9,
                fontFamily: 'Khalid-Art-bold, sans-serif',
                fontSize: '11px'
              }}>وكيل</Typography>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* قائمة احترافية مع تأثيرات 3D */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', pt: 2, px: 1 }}>
        <List sx={{ py: 0 }}>
          {systemMenus.map((item) => {
            if (item.permission && !hasPermission(item.permission.resource, item.permission.action)) {
              return null
            }

            const isActive = location.pathname === item.path

            return (
              <ListItem key={item.path} disablePadding sx={{ mb: 1.5 }}>
                <ListItemButton
                  onClick={() => navigate(item.path)}
                  sx={{
                    borderRadius: 3,
                    py: 2,
                    px: 2,
                    background: isActive ? item.gradient : 'rgba(255,255,255,0.8)',
                    color: isActive ? 'white' : '#2c3e50',
                    boxShadow: isActive ? item.shadow : '0 4px 16px rgba(0,0,0,0.1)',
                    border: isActive ? 'none' : '1px solid rgba(0,0,0,0.05)',
                    backdropFilter: 'blur(10px)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    direction: 'rtl',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      background: isActive ? 'none' : 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.6) 100%)',
                      pointerEvents: 'none'
                    },
                    '&:hover': {
                      transform: 'translateY(-4px) scale(1.02)',
                      boxShadow: isActive ?
                        `0 12px 40px ${item.shadow.split('rgba(')[1].split(')')[0]}, 0.4)` :
                        '0 8px 32px rgba(0,0,0,0.15)',
                      background: isActive ? item.gradient : 'rgba(255,255,255,0.95)'
                    },
                    '&:active': {
                      transform: 'translateY(-2px) scale(1.01)'
                    }
                  }}
                >
                  <ListItemIcon sx={{
                    minWidth: 50,
                    marginLeft: '16px',
                    marginRight: 0,
                    position: 'relative',
                    zIndex: 1
                  }}>
                    <Box sx={{
                      width: 40,
                      height: 40,
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      background: isActive ?
                        'rgba(255,255,255,0.2)' :
                        item.gradient,
                      boxShadow: isActive ?
                        '0 4px 16px rgba(255,255,255,0.3)' :
                        '0 4px 16px rgba(0,0,0,0.2)',
                      transition: 'all 0.3s ease'
                    }}>
                      <span style={{
                        fontSize: '20px',
                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))',
                        transform: 'perspective(100px) rotateX(10deg)'
                      }}>
                        {item.icon}
                      </span>
                    </Box>
                  </ListItemIcon>
                  <ListItemText
                    primary={item.text}
                    sx={{
                      textAlign: 'right',
                      position: 'relative',
                      zIndex: 1,
                      '& .MuiListItemText-primary': {
                        fontWeight: 700,
                        fontSize: '16px',
                        textAlign: 'right',
                        fontFamily: 'Khalid-Art-bold, sans-serif',
                        textShadow: isActive ? '0 2px 4px rgba(0,0,0,0.3)' : 'none',
                        letterSpacing: '0.5px'
                      }
                    }}
                  />
                </ListItemButton>
              </ListItem>
            )
          })}
        </List>
      </Box>
    </Box>
  )

  return (
    <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      direction: 'rtl',
      backgroundColor: '#f8f9fa',
      position: 'relative',
      width: '100vw',
      overflow: 'hidden'
    }}>
      {/* شريط علوي */}
      <AppBar
        position="fixed"
        sx={{
          width: '100vw',
          right: 0,
          left: 0,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          direction: 'rtl'
        }}
      >
        <Toolbar sx={{ direction: 'rtl' }}>
          {/* زر إخفاء/إظهار القائمة الجانبية للشاشات الكبيرة */}
          <Tooltip title={sidebarOpen ? "إخفاء القائمة" : "إظهار القائمة"}>
            <IconButton
              color="inherit"
              aria-label="toggle sidebar"
              onClick={handleSidebarToggle}
              sx={{
                ml: 2,
                display: { xs: 'none', sm: 'block' },
                backgroundColor: 'rgba(255,255,255,0.1)',
                '&:hover': {
                  backgroundColor: 'rgba(255,255,255,0.2)',
                }
              }}
            >
              <span style={{ fontSize: '24px' }}>
                {sidebarOpen ? '◀' : '▶'}
              </span>
            </IconButton>
          </Tooltip>

          {/* زر القائمة للموبايل */}
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ ml: 2, display: { sm: 'none' } }}
          >
            <span style={{ fontSize: '24px' }}>☰</span>
          </IconButton>

          <Typography variant="h6" noWrap component="div" sx={{
            flexGrow: 1,
            fontWeight: 700,
            textAlign: 'right',
            fontFamily: 'Khalid-Art-bold, sans-serif',
            fontSize: '20px',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)',
            letterSpacing: '0.5px'
          }}>
            {getPageTitle()}
          </Typography>

          <Tooltip title="الإشعارات">
            <IconButton size="large" color="inherit">
              <Badge badgeContent={0} color="error">
                <span style={{ fontSize: '24px' }}>🔔</span>
              </Badge>
            </IconButton>
          </Tooltip>

          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
          >
            <span style={{ fontSize: '24px' }}>👤</span>
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'left',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
            sx={{ direction: 'rtl' }}
          >
            <MenuItem onClick={handleProfileOpen} sx={{ direction: 'rtl', textAlign: 'right' }}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>👤</span>
              </ListItemIcon>
              الملف الشخصي
            </MenuItem>
            <MenuItem onClick={handleLogout} sx={{ direction: 'rtl', textAlign: 'right' }}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>🚪</span>
              </ListItemIcon>
              تسجيل الخروج
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* القائمة الجانبية على اليمين */}
      <Drawer
        variant="permanent"
        sx={{
          width: sidebarOpen ? drawerWidth : 0,
          flexShrink: 0,
          transition: 'width 0.3s ease-in-out',
          '& .MuiDrawer-paper': {
            width: sidebarOpen ? drawerWidth : 0,
            boxSizing: 'border-box',
            position: 'fixed',
            right: 0,
            left: 'auto',
            top: 0,
            height: '100vh',
            direction: 'rtl',
            zIndex: 1100,
            borderLeft: sidebarOpen ? '1px solid #e0e0e0' : 'none',
            borderRight: 'none',
            overflow: 'hidden',
            transition: 'width 0.3s ease-in-out'
          },
          display: { xs: 'none', sm: 'block' }
        }}
      >
        {sidebarOpen && drawer}
      </Drawer>

      {/* القائمة المؤقتة للموبايل */}
      <Drawer
        variant="temporary"
        anchor="right"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
        sx={{
          display: { xs: 'block', sm: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            direction: 'rtl'
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* المحتوى الرئيسي - يتوسع عند إخفاء القائمة */}
      <Box
        component="main"
        sx={{
          position: 'fixed',
          top: '64px',
          right: sidebarOpen ? `${drawerWidth}px` : '0px',
          left: 0,
          bottom: 0,
          backgroundColor: '#f8f9fa',
          direction: 'rtl',
          overflow: 'auto',
          width: sidebarOpen ? `calc(100vw - ${drawerWidth}px)` : '100vw',
          height: 'calc(100vh - 64px)',
          transition: 'right 0.3s ease-in-out, width 0.3s ease-in-out'
        }}
      >
        <Box sx={{
          p: 2,
          direction: 'rtl',
          width: '100%',
          height: '100%',
          minHeight: 'calc(100vh - 64px - 32px)'
        }}>
          {children}
        </Box>
      </Box>

      {/* نافذة الملف الشخصي */}
      <UserProfileDialog
        open={profileDialogOpen}
        onClose={() => setProfileDialogOpen(false)}
      />
    </Box>
  )
}

export default LucidArabicLayout
