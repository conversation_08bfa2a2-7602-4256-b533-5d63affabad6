const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function fixDevice1DirectSQL() {
  try {
    console.log('🔧 إصلاح العمود device1 باستخدام SQL مباشر')
    console.log('==========================================')
    
    // التحقق من وجود العمود device1
    console.log('📝 الخطوة 1: التحقق من وجود العمود device1...')
    
    try {
      const result = await prisma.$queryRaw`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'device1'
      `
      
      if (result.length > 0) {
        console.log('✅ العمود device1 موجود في قاعدة البيانات')
      } else {
        console.log('❌ العمود device1 غير موجود، سيتم إنشاؤه...')
        await prisma.$executeRaw`ALTER TABLE "users" ADD COLUMN "device1" TEXT;`
        console.log('✅ تم إنشاء العمود device1')
      }
    } catch (error) {
      console.log('⚠️ خطأ في التحقق من العمود:', error.message)
    }
    
    // قراءة البيانات الحالية باستخدام SQL مباشر
    console.log('📝 الخطوة 2: قراءة البيانات الحالية...')
    
    const users = await prisma.$queryRaw`
      SELECT user_id, username, login_name, device_id, device1, is_active
      FROM users 
      WHERE login_name = 'hash8080'
    `
    
    if (users.length === 0) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }
    
    const user = users[0]
    console.log(`👤 المستخدم: ${user.username}`)
    console.log(`📱 device_id (قديم): ${user.device_id || 'فارغ'}`)
    console.log(`📱 device1 (جديد): ${user.device1 || 'فارغ'}`)
    
    // تحديث العمود device1 إذا كان فارغاً
    if (!user.device1 && user.device_id) {
      console.log('📝 الخطوة 3: تحديث العمود device1...')
      
      // أخذ الجهاز الأول من device_id
      const primaryDevice = user.device_id.includes(',') 
        ? user.device_id.split(',')[0].trim()
        : user.device_id
      
      console.log(`📱 الجهاز الأساسي: ${primaryDevice}`)
      
      await prisma.$executeRaw`
        UPDATE users 
        SET device1 = ${primaryDevice}
        WHERE user_id = ${user.user_id}
      `
      
      console.log('✅ تم تحديث العمود device1')
    }
    
    // قراءة البيانات بعد التحديث
    console.log('📝 الخطوة 4: التحقق من النتيجة...')
    
    const updatedUsers = await prisma.$queryRaw`
      SELECT user_id, username, login_name, device_id, device1, is_active
      FROM users 
      WHERE login_name = 'hash8080'
    `
    
    const updatedUser = updatedUsers[0]
    console.log('')
    console.log('📊 النتيجة النهائية:')
    console.log(`👤 المستخدم: ${updatedUser.username}`)
    console.log(`📱 device_id (قديم): ${updatedUser.device_id || 'فارغ'}`)
    console.log(`📱 device1 (جديد): ${updatedUser.device1 || 'فارغ'}`)
    console.log(`✅ نشط: ${updatedUser.is_active ? 'نعم' : 'لا'}`)
    
    // اختبار تسجيل الدخول
    console.log('')
    console.log('🧪 اختبار تسجيل الدخول:')
    
    const testDevices = [
      updatedUser.device_id,
      updatedUser.device1
    ].filter(Boolean)
    
    for (const deviceId of testDevices) {
      console.log(`📱 اختبار الجهاز: ${deviceId}`)
      
      try {
        const response = await fetch('http://localhost:8080/api/auth/login', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            loginName: 'hash8080',
            password: 'hash8080',
            deviceId: deviceId
          })
        })
        
        const result = await response.json()
        
        if (response.ok) {
          console.log(`   ✅ نجح - التوكن: ${result.token ? 'تم إنشاؤه' : 'لم يتم إنشاؤه'}`)
        } else {
          console.log(`   ❌ فشل - ${result.error || 'خطأ غير محدد'}`)
        }
        
      } catch (error) {
        console.log(`   ❌ خطأ في الاتصال: ${error.message}`)
      }
    }
    
    console.log('')
    console.log('🎉 تم الانتهاء من الإصلاح!')
    
  } catch (error) {
    console.error('❌ خطأ في الإصلاح:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixDevice1DirectSQL()
