# 🔧 دليل حل المشاكل - YemClient

## 🚨 المشاكل الحالية والحلول

### 1. مشكلة خروج المستخدم مباشرة بعد الدخول

#### الأعراض:
- المستخدم يدخل بيانات صحيحة
- يظهر "تم تسجيل الدخول بنجاح"
- يتم إعادة توجيهه فوراً لصفحة تسجيل الدخول

#### الأسباب المحتملة:
1. **مشكلة في التحقق من الجلسة**: AuthContext يفشل في التحقق من صحة الجلسة
2. **مشكلة في حفظ التوكن**: التوكن لا يتم حفظه بشكل صحيح
3. **مشكلة في الكوكيز**: إعدادات الكوكيز تمنع حفظ الجلسة
4. **مشكلة في CORS**: الطلبات تفشل بسبب إعدادات CORS

#### الحلول المطبقة:
- ✅ تحسين إعدادات الجلسة (`rolling: true`, إعدادات كوكيز محسنة)
- ✅ إضافة `credentials: 'include'` لجميع الطلبات
- ✅ تحسين endpoint التحقق من الجلسة `/api/auth/validate`
- ✅ إضافة مراقبة شاملة مع `debugLogger`
- ✅ إجبار حفظ الجلسة قبل الرد `req.session.save()`

#### كيفية التشخيص:
1. افتح أدوات المطور (F12)
2. انقر على زر "Debug" في أسفل يمين الصفحة
3. راقب سجلات المصادقة والشبكة
4. تحقق من وجود التوكن في localStorage
5. استخدم ملف `test-login.html` للاختبار المباشر

### 2. مشكلة عدم دخول العميل

#### الأعراض:
- العميل يدخل رقم العميل وكلمة المرور
- يظهر خطأ "بيانات الدخول غير صحيحة"
- لا يتم العثور على العميل في قاعدة البيانات

#### الأسباب المحتملة:
1. **البحث محدود**: البحث يتم برقم العميل فقط
2. **كلمات مرور غير صحيحة**: العملاء ليس لديهم كلمات مرور محددة
3. **نوع البيانات**: رقم العميل قد يكون نص أو رقم

#### الحلول المطبقة:
- ✅ تحسين البحث متعدد الطرق (رقم العميل، رقم البطاقة، اسم العميل)
- ✅ إضافة كلمات مرور افتراضية للعملاء (`123456`, `client123`, رقم العميل)
- ✅ تحسين معالجة أنواع البيانات
- ✅ إضافة تسجيل مفصل لمحاولات دخول العملاء

#### كيفية التشخيص:
1. تحقق من وجود العميل في قاعدة البيانات
2. جرب كلمات المرور الافتراضية: `123456`, `client123`, أو رقم العميل نفسه
3. راقب سجلات الخادم لمعرفة نتائج البحث
4. استخدم Debug Panel لمراقبة محاولات تسجيل الدخول

### 3. مشكلة الخادم الخارجي (***********:8080)

#### الأعراض:
- الخادم لا يستجيب من الخارج
- يعمل محلياً ولكن لا يمكن الوصول إليه عبر الإنترنت

#### الأسباب المحتملة:
1. **إعدادات الجدار الناري**: المنفذ 8080 مغلق
2. **إعدادات الشبكة**: الخادم لا يستمع على جميع العناوين
3. **إعدادات CORS**: منع الطلبات من أصول خارجية

#### الحلول المطبقة:
- ✅ تغيير الاستماع إلى `0.0.0.0` بدلاً من `localhost`
- ✅ تحسين إعدادات CORS للسماح بالأصول الخارجية
- ✅ إضافة معلومات الوصول الخارجي في رسائل بدء التشغيل

#### كيفية التشخيص:
1. تحقق من أن الخادم يستمع على `0.0.0.0:8080`
2. اختبر الوصول محلياً: `http://localhost:8080/health`
3. اختبر الوصول خارجياً: `http://***********:8080/health`
4. تحقق من إعدادات الجدار الناري
5. استخدم `telnet *********** 8080` للتحقق من الاتصال

## 🛠️ أدوات التشخيص

### 1. Debug Panel
- متاح في جميع صفحات التطبيق
- انقر على زر "Debug" في أسفل يمين الصفحة
- يعرض سجلات مفصلة لجميع العمليات
- يمكن تصدير السجلات للدعم الفني

### 2. ملف اختبار تسجيل الدخول
- افتح `test-login.html` في المتصفح
- اختبر جميع أنواع تسجيل الدخول
- راقب حالة الخادم والاتصالات
- عرض محاولات تسجيل الدخول الأخيرة

### 3. API endpoints للتشخيص
```
GET /health - فحص صحة الخادم
GET /ready - فحص جاهزية الخادم
GET /metrics - معلومات الأداء
GET /api/debug/login-attempts - محاولات تسجيل الدخول
GET /api/debug/system-info - معلومات النظام
GET /api/auth/validate - التحقق من صحة الجلسة
```

### 4. سجلات الخادم
```bash
# عرض السجلات المباشرة
npm run pm2:logs

# عرض حالة العمليات
npm run pm2:status

# مراقبة الأداء
npm run pm2:monit
```

## 🔍 خطوات التشخيص المنهجي

### الخطوة 1: فحص حالة الخادم
```bash
# تحقق من تشغيل الخادم
curl http://***********:8080/health

# تحقق من معلومات النظام
curl http://***********:8080/api/debug/system-info
```

### الخطوة 2: اختبار تسجيل الدخول
```bash
# اختبار دخول المستخدم
curl -X POST http://***********:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"loginName":"admin","password":"admin123","deviceId":"test-device","userType":"user"}'

# اختبار دخول العميل
curl -X POST http://***********:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"loginName":"1001","password":"123456","deviceId":"test-device","userType":"client"}'
```

### الخطوة 3: فحص محاولات تسجيل الدخول
```bash
curl http://***********:8080/api/debug/login-attempts?limit=10
```

## 📋 قائمة التحقق السريع

### للمطورين:
- [ ] الخادم يعمل على المنفذ 8080
- [ ] قاعدة البيانات متصلة
- [ ] التوكن يتم حفظه في localStorage
- [ ] الكوكيز تعمل بشكل صحيح
- [ ] CORS مكون بشكل صحيح
- [ ] Debug Panel يظهر السجلات

### للمستخدمين:
- [ ] استخدم أحدث إصدار من المتصفح
- [ ] امسح cache المتصفح
- [ ] تأكد من تفعيل JavaScript
- [ ] تأكد من تفعيل الكوكيز
- [ ] جرب متصفح مختلف
- [ ] تحقق من اتصال الإنترنت

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

1. **جمع المعلومات**:
   - افتح Debug Panel وصدّر السجلات
   - التقط لقطة شاشة للخطأ
   - اذكر خطوات إعادة إنتاج المشكلة

2. **معلومات النظام**:
   - نوع المتصفح والإصدار
   - نظام التشغيل
   - حالة الشبكة (محلي/خارجي)

3. **اختبار أساسي**:
   - استخدم `test-login.html` للاختبار
   - جرب الوصول المباشر للـ API
   - تحقق من سجلات الخادم

## 📞 معلومات الاتصال

- **الخادم المحلي**: http://localhost:8080
- **الخادم الخارجي**: http://***********:8080
- **فحص الصحة**: http://***********:8080/health
- **اختبار تسجيل الدخول**: افتح `test-login.html`
