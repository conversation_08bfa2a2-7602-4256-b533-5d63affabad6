const http = require('http');

function testEndpoint(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsed
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

async function runBasicTests() {
  console.log('🧪 Basic Server Tests Starting...\n');

  const tests = [
    { name: 'Health Check', path: '/health' },
    { name: 'Security Stats', path: '/api/security/stats' },
    { name: 'Security Login Attempts', path: '/api/security/login-attempts' },
    { name: 'External API Health', path: '/api/external/health' },
    { name: 'External API Stats', path: '/api/external/stats' },
    { name: 'Users API', path: '/api/users' },
    { name: 'Clients API', path: '/api/clients' },
    { name: 'Agents API', path: '/api/agents' },
    { name: 'Data Records API', path: '/api/data-records' }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      const result = await testEndpoint(test.path);

      if (result.status === 200) {
        console.log(`✅ ${test.name}: Status ${result.status}`);
        if (result.data && typeof result.data === 'object') {
          console.log(`   Keys: ${Object.keys(result.data).slice(0, 5).join(', ')}`);
        }
        passed++;
      } else {
        console.log(`❌ ${test.name}: Status ${result.status}`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
      failed++;
    }
    console.log('');
  }

  // اختبار POST - تسجيل الدخول
  try {
    console.log('Testing: User Login (POST)');
    const loginResult = await testEndpoint('/api/auth/login', 'POST', {
      loginName: 'admin',
      password: 'admin123456',
      deviceId: 'test-device'
    });

    if (loginResult.status === 200 || loginResult.status === 401) {
      console.log(`✅ User Login: Status ${loginResult.status}`);
      if (loginResult.data.success) {
        console.log(`   User: ${loginResult.data.user?.username || 'Unknown'}`);
      } else {
        console.log(`   Message: ${loginResult.data.message}`);
      }
      passed++;
    } else {
      console.log(`❌ User Login: Status ${loginResult.status}`);
      failed++;
    }
  } catch (error) {
    console.log(`❌ User Login: ${error.message}`);
    failed++;
  }

  console.log('');

  // اختبار POST - إنشاء عميل
  try {
    console.log('Testing: Create Client (POST)');
    const clientResult = await testEndpoint('/api/clients', 'POST', {
      clientName: 'عميل تجريبي',
      appName: 'تطبيق تجريبي',
      cardNumber: '12345678',
      password: 'test123456',
      ipAddress: '*************',
      userId: 1
    });

    if (clientResult.status === 201 || clientResult.status === 400 || clientResult.status === 409) {
      console.log(`✅ Create Client: Status ${clientResult.status}`);
      if (clientResult.data.success) {
        console.log(`   Client: ${clientResult.data.client?.clientName || 'Unknown'}`);
      } else {
        console.log(`   Message: ${clientResult.data.error || clientResult.data.message}`);
      }
      passed++;
    } else {
      console.log(`❌ Create Client: Status ${clientResult.status}`);
      failed++;
    }
  } catch (error) {
    console.log(`❌ Create Client: ${error.message}`);
    failed++;
  }

  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(2)}%`);
}

runBasicTests().catch(console.error);
