import React, { useState } from 'react'
import {
  Box,
  Drawer,
  AppBar,
  <PERSON><PERSON><PERSON>,
  List,
  Typography,
  Divider,
  IconButton,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  Container,
  Paper
} from '@mui/material'
import {
  Menu as MenuIcon,
  Dashboard,
  People,
  Business,
  SupervisorAccount,
  Security,
  AccountCircle,
  Logout,
  Notifications,
  Storage as DataIcon
} from '@mui/icons-material'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext'

const drawerWidth = 280

const DashboardLayout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [anchorEl, setAnchorEl] = useState(null)

  // تسجيل تحديث التخطيط
  React.useEffect(() => {
    console.log('🎨 تم تحميل التخطيط العربي الجديد')
    console.log('📱 القائمة على اليمين')
    console.log('🌐 اتجاه RTL مفعل')
  }, [])
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout, hasPermission } = useAuth()

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleLogout = () => {
    logout()
    handleClose()
  }

  const menuItems = [
    {
      text: 'لوحة التحكم',
      icon: '📊',
      path: '/dashboard',
      permission: null
    },
    {
      text: 'العملاء',
      icon: '👥',
      path: '/clients',
      permission: { resource: 'clients', action: 'read' }
    },
    {
      text: 'الوكلاء',
      icon: '🏢',
      path: '/agents',
      permission: { resource: 'agents', action: 'read' }
    },
    {
      text: 'المستخدمين',
      icon: '👤',
      path: '/users',
      permission: { resource: 'users', action: 'read' }
    },
    {
      text: 'البيانات',
      icon: '📋',
      path: '/data-records',
      permission: null // متاح للجميع للعرض
    },
    {
      text: 'الأمان',
      icon: '🔒',
      path: '/security',
      permission: { resource: 'security', action: 'read' }
    }
  ]

  const drawer = (
    <div>
      <Toolbar sx={{
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <Typography variant="h6" noWrap component="div" sx={{ fontWeight: 700 }}>
          نظام إدارة العملاء
        </Typography>
      </Toolbar>
      <Divider />

      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Avatar sx={{
          width: 64,
          height: 64,
          mx: 'auto',
          mb: 1,
          bgcolor: 'primary.main'
        }}>
          {user?.username?.charAt(0)}
        </Avatar>
        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
          {user?.username}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {user?.permissions?.isAdmin ? 'مدير النظام' : 'مستخدم'}
        </Typography>
      </Box>

      <Divider />

      <List>
        {menuItems.map((item) => {
          const isActive = location.pathname === item.path

          return (
            <ListItem key={item.text} disablePadding>
              <ListItemButton
                onClick={() => navigate(item.path)}
                sx={{
                  mx: 1,
                  borderRadius: 2,
                  mb: 0.5,
                  backgroundColor: isActive ? 'primary.main' : 'transparent',
                  color: isActive ? 'white' : 'inherit',
                  '&:hover': {
                    backgroundColor: isActive ? 'primary.dark' : 'action.hover',
                  }
                }}
              >
                <ListItemIcon sx={{
                  color: isActive ? 'white' : 'inherit',
                  minWidth: 40
                }}>
                  <span style={{ fontSize: '20px' }}>{item.icon}</span>
                </ListItemIcon>
                <ListItemText
                  primary={item.text}
                  primaryTypographyProps={{
                    fontWeight: isActive ? 600 : 400
                  }}
                />
              </ListItemButton>
            </ListItem>
          )
        })}
      </List>
    </div>
  )

  return (
    <Box sx={{
      display: 'flex',
      direction: 'rtl',
      minHeight: '100vh',
      backgroundColor: '#f8f9fa'
    }}>
      <AppBar
        position="fixed"
        sx={{
          width: '100%',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
          zIndex: (theme) => theme.zIndex.drawer + 1
        }}
      >
        <Toolbar>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {menuItems.find(item => item.path === location.pathname)?.text || 'لوحة التحكم'}
          </Typography>

          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="end"
            onClick={handleDrawerToggle}
            sx={{ ml: 2, display: { sm: 'none' } }}
          >
            <span style={{ fontSize: '24px' }}>☰</span>
          </IconButton>

          <Tooltip title="الإشعارات">
            <IconButton color="inherit">
              <Badge badgeContent={0} color="error">
                <span style={{ fontSize: '24px' }}>🔔</span>
              </Badge>
            </IconButton>
          </Tooltip>

          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
          >
            <span style={{ fontSize: '24px' }}>👤</span>
          </IconButton>

          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleClose}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>👤</span>
              </ListItemIcon>
              الملف الشخصي
            </MenuItem>
            <MenuItem onClick={handleLogout}>
              <ListItemIcon>
                <span style={{ fontSize: '18px' }}>🚪</span>
              </ListItemIcon>
              تسجيل الخروج
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      <Box
        component="nav"
        sx={{
          width: { sm: drawerWidth },
          flexShrink: { sm: 0 }
        }}
      >
        <Drawer
          variant="temporary"
          anchor="right"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: 'rtl'
            },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          anchor="right"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: drawerWidth,
              direction: 'rtl',
              position: 'fixed',
              height: '100%',
              top: 0,
              right: 0
            },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>

      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          minHeight: '100vh',
          backgroundColor: '#f8f9fa',
          direction: 'rtl',
          marginRight: { sm: `${drawerWidth}px` },
          marginLeft: { sm: 0 }
        }}
      >
        <Toolbar />
        <Container maxWidth="xl" sx={{ py: 3 }}>
          <Paper
            elevation={1}
            sx={{
              p: 3,
              borderRadius: 2,
              backgroundColor: 'white',
              minHeight: 'calc(100vh - 120px)'
            }}
          >
            {children}
          </Paper>
        </Container>
      </Box>
    </Box>
  )
}

export default DashboardLayout
