const { Client } = require('pg');
const bcrypt = require('bcryptjs');

async function addUser() {
    const client = new Client({
        host: 'localhost',
        port: 5432,
        database: 'yemclient_db',
        user: 'postgres',
        password: 'yemen123'
    });

    try {
        console.log('🔍 الاتصال بقاعدة البيانات...');
        await client.connect();
        console.log('✅ تم الاتصال بنجاح');

        // تشفير كلمة المرور
        const password = 'Hash8080';
        const hashedPassword = await bcrypt.hash(password, 12);
        console.log('🔐 تم تشفير كلمة المرور');

        // التحقق من عدم وجود المستخدم
        const checkUser = await client.query(
            'SELECT user_id, username, login_name FROM users WHERE login_name = $1',
            ['hash8080']
        );

        if (checkUser.rows.length > 0) {
            console.log('⚠️ المستخدم موجود مسبقاً!');
            console.log('معلومات المستخدم الحالي:');
            console.log(`- ID: ${checkUser.rows[0].user_id}`);
            console.log(`- الاسم: ${checkUser.rows[0].username}`);
            console.log(`- اسم المستخدم: ${checkUser.rows[0].login_name}`);
            return;
        }

        // إنشاء المستخدم الجديد
        const permissions = JSON.stringify({
            isAdmin: true,
            clients: {
                create: true,
                read: true,
                update: true,
                delete: true
            },
            agents: {
                create: true,
                read: true,
                update: true,
                delete: true
            },
            users: {
                create: true,
                read: true,
                update: true,
                delete: true
            },
            dashboard: {
                read: true
            },
            security: {
                read: true,
                manage: true
            }
        });

        const insertResult = await client.query(`
            INSERT INTO users (username, device_id, login_name, password, permissions, is_active, created_at, updated_at) 
            VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING user_id, username, login_name, is_active, created_at
        `, [
            'محمد الحاشدي',
            null,
            'hash8080',
            hashedPassword,
            permissions,
            true
        ]);

        const newUser = insertResult.rows[0];
        
        console.log('✅ تم إنشاء المستخدم بنجاح!');
        console.log('');
        console.log('📋 معلومات المستخدم الجديد:');
        console.log(`- ID: ${newUser.user_id}`);
        console.log(`- الاسم: ${newUser.username}`);
        console.log(`- اسم المستخدم: ${newUser.login_name}`);
        console.log(`- نشط: ${newUser.is_active ? 'نعم' : 'لا'}`);
        console.log(`- تاريخ الإنشاء: ${newUser.created_at}`);
        console.log('');
        console.log('🔑 بيانات تسجيل الدخول:');
        console.log('- اسم المستخدم: hash8080');
        console.log('- كلمة المرور: Hash8080');
        console.log('- الصلاحيات: أدمن كامل الصلاحيات');

        // عرض جميع المستخدمين
        const allUsers = await client.query(`
            SELECT 
                user_id,
                username,
                login_name,
                is_active,
                created_at,
                CASE 
                    WHEN permissions::text LIKE '%"isAdmin": true%' THEN 'أدمن'
                    ELSE 'مستخدم عادي'
                END as user_type
            FROM users 
            ORDER BY created_at DESC
        `);

        console.log('');
        console.log('👥 جميع المستخدمين في النظام:');
        allUsers.rows.forEach(user => {
            console.log(`- ${user.username} (${user.login_name}) - ${user.user_type} - ${user.is_active ? 'نشط' : 'غير نشط'}`);
        });

    } catch (error) {
        console.error('❌ خطأ في إنشاء المستخدم:', error.message);
        
        if (error.code === '23505') {
            console.log('⚠️ اسم المستخدم موجود مسبقاً');
        }
    } finally {
        await client.end();
        console.log('');
        console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
    }
}

// تشغيل الدالة
addUser();
