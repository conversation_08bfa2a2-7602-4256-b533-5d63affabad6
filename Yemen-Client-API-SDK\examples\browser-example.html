<!DOCTYPE html>
<html>
<head>
    <title>Yemen Client API - Browser Example</title>
</head>
<body>
    <h1>Yemen Client Management System</h1>
    <script src="../YemenClientAPI-Agent.js"></script>
    <script>
        async function testAPI() {
            const agent = new YemenClientAgentAPI(
                'http://***********:8080',
                'agent001',
                'agent123'
            )
            try {
                await agent.authenticate()
                console.log('Agent authenticated!')
            } catch (error) {
                console.error('Error:', error)
            }
        }
        testAPI()
    </script>
</body>
</html>
