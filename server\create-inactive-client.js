/**
 * إنشاء عميل غير نشط للاختبار
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createInactiveClient() {
  try {
    console.log('🔧 إنشاء عميل غير نشط للاختبار...\n');

    // التحقق من وجود العميل 1005
    const existingClient = await prisma.client.findFirst({
      where: { clientCode: 1005 }
    });

    if (existingClient) {
      console.log('📋 العميل 1005 موجود بالفعل');
      console.log(`   الاسم: ${existingClient.clientName}`);
      console.log(`   الحالة: ${existingClient.status === 1 ? 'نشط' : 'غير نشط'}`);
      
      if (existingClient.status === 0) {
        console.log('✅ العميل 1005 غير نشط بالفعل - جاهز للاختبار');
        return;
      } else {
        console.log('🔄 تحديث العميل 1005 ليصبح غير نشط...');
        
        await prisma.client.update({
          where: { id: existingClient.id },
          data: { status: 0 }
        });
        
        console.log('✅ تم تحديث العميل 1005 ليصبح غير نشط');
        return;
      }
    }

    // إنشاء عميل جديد غير نشط
    console.log('🆕 إنشاء عميل جديد غير نشط...');
    
    const hashedPassword = await bcrypt.hash('TEST1005', 10);
    
    const newClient = await prisma.client.create({
      data: {
        clientName: 'عميل تجريبي غير نشط',
        appName: 'تطبيق تجريبي',
        cardNumber: '12345678',
        clientCode: 1005,
        password: hashedPassword,
        token: 'TEST1005',
        ipAddress: '*************',
        status: 0, // غير نشط
        userId: 5 // admin user
      }
    });

    console.log('✅ تم إنشاء العميل غير النشط بنجاح!');
    console.log(`   ID: ${newClient.id}`);
    console.log(`   الاسم: ${newClient.clientName}`);
    console.log(`   الكود: ${newClient.clientCode}`);
    console.log(`   التوكن: ${newClient.token}`);
    console.log(`   الحالة: ${newClient.status === 1 ? 'نشط' : 'غير نشط'}`);

  } catch (error) {
    console.error('❌ خطأ في إنشاء العميل غير النشط:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

createInactiveClient().catch(console.error);
