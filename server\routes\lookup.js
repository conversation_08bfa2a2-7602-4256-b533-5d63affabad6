const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على قائمة الوكلاء للاختيار
router.get('/agents', authenticateToken, async (req, res) => {
  try {
    const agents = await prisma.agent.findMany({
      select: {
        id: true,
        agentName: true,
        agencyName: true,
        isActive: true
      },
      where: {
        isActive: true // الوكلاء النشطين فقط
      },
      orderBy: {
        agentName: 'asc'
      }
    });

    res.json(agents);
  } catch (error) {
    console.error('Get agents lookup error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على قائمة المستخدمين للاختيار
router.get('/users', authenticateToken, async (req, res) => {
  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true
      },
      where: {
        isActive: true // المستخدمين النشطين فقط
      },
      orderBy: {
        username: 'asc'
      }
    });

    res.json(users);
  } catch (error) {
    console.error('Get users lookup error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على معلومات وكيل محدد
router.get('/agents/:id', authenticateToken, async (req, res) => {
  try {
    const agent = await prisma.agents.findUnique({
      where: { agent_id: parseInt(req.params.id) },
      select: {
        agent_id: true,
        agent_name: true,
        agency_name: true,
        status: true
      }
    });

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    res.json(agent);
  } catch (error) {
    console.error('Get agent error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على معلومات مستخدم محدد
router.get('/users/:id', authenticateToken, async (req, res) => {
  try {
    const user = await prisma.users.findUnique({
      where: { user_id: parseInt(req.params.id) },
      select: {
        user_id: true,
        username: true,
        login_name: true,
        is_active: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
