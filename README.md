# نظام إدارة العملاء والوكلاء

نظام شامل لإدارة العملاء والوكلاء مع واجهة عربية احترافية.

## المتطلبات

- Node.js (الإصدار 16 أو أحدث)
- PostgreSQL (الإصدار 12 أو أحدث)
- npm أو yarn

## التثبيت والتشغيل

### 1. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة البيانات
createdb yemclient_db

# تشغيل سكريبت إنشاء الجداول
psql -U postgres -d yemclient_db -f create_database.sql
```

### 2. إعد<PERSON> الخادم
```bash
cd server
npm install
npm start
```

### 3. إعد<PERSON> العميل
```bash
cd client
npm install
npm run build
```

### 4. تشغيل النظام
```bash
# تشغيل سريع
./start_system.bat

# أو تشغيل يدوي
cd server && npm start
```

## الوصول للنظام

- **المنفذ الداخلي**: http://localhost:8080
- **المنفذ الخارجي**: http://***********:3000

## المزايا

- ✅ واجهة عربية احترافية
- ✅ إدارة العملاء والوكلاء
- ✅ نظام مستخدمين متقدم
- ✅ سجلات البيانات التلقائية
- ✅ نظام أمان شامل
- ✅ تصميم متجاوب

## الملفات الأساسية

### الخادم (server/)
- `server.js` - الخادم الرئيسي
- `package.json` - تبعيات الخادم
- `prisma/` - إعدادات قاعدة البيانات

### العميل (client/)
- `src/` - كود المصدر
- `public/` - الملفات العامة
- `package.json` - تبعيات العميل

### قاعدة البيانات
- `create_database.sql` - سكريبت إنشاء الجداول
- `init.sql` - البيانات الأولية

### التشغيل
- `start_system.bat` - تشغيل النظام
- `package.json` - إعدادات المشروع الرئيسية

## الدعم

للدعم الفني، يرجى مراجعة الملفات المرفقة أو التواصل مع فريق التطوير.
