/**
 * اختبار APIs الخارجية للمطورين
 */

async function testExternalAPIs() {
  console.log('🔧 اختبار APIs الخارجية للمطورين...\n');

  try {
    // اختبار 1: Health Check
    console.log('1️⃣ اختبار External Health Check:');
    const healthResponse = await fetch('http://localhost:8080/api/external/health');
    console.log(`   📡 Status: ${healthResponse.status}`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('   ✅ External Health Check يعمل!');
      console.log(`   📊 Status: ${healthData.status}`);
      console.log(`   💾 Database: ${healthData.data?.database}`);
      console.log(`   📅 Timestamp: ${healthData.data?.timestamp}`);
      console.log(`   🔢 Version: ${healthData.data?.version}`);
    } else {
      console.log('   ❌ External Health Check فشل!');
    }
    console.log('');

    // اختبار 2: Verify Direct - بيانات صحيحة
    console.log('2️⃣ اختبار Verify Direct (بيانات صحيحة):');
    const correctData = {
      agent_login_name: 'agent001',
      agent_login_password: 'agent123',
      client_code: 1004,
      client_token: 'UNdZqPVxrxAX'
    };

    console.log(`   📤 Request: ${JSON.stringify(correctData, null, 2)}`);
    
    const verifyResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(correctData)
    });

    console.log(`   📡 Status: ${verifyResponse.status}`);
    
    if (verifyResponse.ok) {
      const verifyData = await verifyResponse.json();
      console.log('   ✅ Verify Direct نجح!');
      console.log(`   📊 Status: ${verifyData.status}`);
      console.log(`   👤 Client Status: ${verifyData.client_status === 1 ? 'نشط' : 'غير نشط'}`);
    } else {
      const errorData = await verifyResponse.json();
      console.log('   ❌ Verify Direct فشل!');
      console.log(`   📝 Error: ${errorData.status}`);
      console.log(`   📝 Message: ${errorData.message}`);
    }
    console.log('');

    // اختبار 3: Verify Direct - وكيل خاطئ
    console.log('3️⃣ اختبار Verify Direct (وكيل خاطئ):');
    const wrongAgentData = {
      agent_login_name: 'wrong_agent',
      agent_login_password: 'wrong_password',
      client_code: 1004,
      client_token: 'UNdZqPVxrxAX'
    };

    const wrongAgentResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(wrongAgentData)
    });

    console.log(`   📡 Status: ${wrongAgentResponse.status}`);
    
    if (!wrongAgentResponse.ok) {
      const errorData = await wrongAgentResponse.json();
      console.log('   ✅ النظام يرفض الوكيل الخاطئ بشكل صحيح!');
      console.log(`   📝 Error Status: ${errorData.status}`);
    } else {
      console.log('   ⚠️ النظام قبل الوكيل الخاطئ (مشكلة أمنية)!');
    }
    console.log('');

    // اختبار 4: Verify Direct - عميل خاطئ
    console.log('4️⃣ اختبار Verify Direct (عميل خاطئ):');
    const wrongClientData = {
      agent_login_name: 'agent001',
      agent_login_password: 'agent123',
      client_code: 9999,
      client_token: 'WRONG_TOKEN'
    };

    const wrongClientResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(wrongClientData)
    });

    console.log(`   📡 Status: ${wrongClientResponse.status}`);
    
    if (!wrongClientResponse.ok) {
      const errorData = await wrongClientResponse.json();
      console.log('   ✅ النظام يرفض العميل الخاطئ بشكل صحيح!');
      console.log(`   📝 Error Status: ${errorData.status}`);
    } else {
      console.log('   ⚠️ النظام قبل العميل الخاطئ (مشكلة أمنية)!');
    }
    console.log('');

    // اختبار 5: Verify Direct - بيانات ناقصة
    console.log('5️⃣ اختبار Verify Direct (بيانات ناقصة):');
    const incompleteData = {
      agent_login_name: 'agent001',
      // نقص كلمة المرور
      client_code: 1004,
      client_token: 'UNdZqPVxrxAX'
    };

    const incompleteResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(incompleteData)
    });

    console.log(`   📡 Status: ${incompleteResponse.status}`);
    
    if (!incompleteResponse.ok) {
      const errorData = await incompleteResponse.json();
      console.log('   ✅ النظام يرفض البيانات الناقصة بشكل صحيح!');
      console.log(`   📝 Error Status: ${errorData.status}`);
      console.log(`   📝 Error Code: ${errorData.error_code}`);
    } else {
      console.log('   ⚠️ النظام قبل البيانات الناقصة (مشكلة)!');
    }
    console.log('');

    // اختبار 6: اختبار عملاء مختلفين
    console.log('6️⃣ اختبار عملاء مختلفين:');
    
    const testClients = [
      { code: 1000, token: 'ABC12345', name: 'محمد علي الحاشدي' },
      { code: 1004, token: 'UNdZqPVxrxAX', name: 'تجربة جديدة' },
      { code: 1005, token: 'TEST1005', name: 'عميل تجريبي' }
    ];

    for (const client of testClients) {
      console.log(`   🧪 اختبار العميل ${client.code} (${client.name}):`);
      
      const clientTestData = {
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: client.code,
        client_token: client.token
      };

      const clientResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(clientTestData)
      });

      console.log(`      📡 Status: ${clientResponse.status}`);
      
      if (clientResponse.ok) {
        const clientData = await clientResponse.json();
        console.log(`      ✅ نجح - Status: ${clientData.status}, Client Status: ${clientData.client_status}`);
      } else {
        const errorData = await clientResponse.json();
        console.log(`      ❌ فشل - Error: ${errorData.status}`);
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار APIs الخارجية:');
    console.log('✅ External Health Check: متاح');
    console.log('✅ Verify Direct API: متاح');
    console.log('✅ التحقق من الوكيل: يعمل');
    console.log('✅ التحقق من العميل: يعمل');
    console.log('✅ رفض البيانات الخاطئة: يعمل');
    console.log('✅ رفض البيانات الناقصة: يعمل');
    console.log('\n🎉 جميع APIs الخارجية تعمل بشكل مثالي!');
    console.log('🔧 المطورون يمكنهم الآن استخدام النظام!');

  } catch (error) {
    console.error('❌ خطأ في اختبار APIs الخارجية:', error.message);
  }
}

testExternalAPIs().catch(console.error);
