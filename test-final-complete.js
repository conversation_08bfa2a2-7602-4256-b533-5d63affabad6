/**
 * اختبار نهائي شامل لجميع المشاكل المُصلحة
 */

async function testFinalComplete() {
  console.log('🎯 اختبار نهائي شامل لجميع المشاكل المُصلحة...\n');

  let totalTests = 0;
  let passedTests = 0;

  try {
    // اختبار 1: APIs المفقودة
    console.log('1️⃣ اختبار APIs المفقودة:');
    totalTests++;
    
    const missingAPIs = [
      { name: 'الوكلاء', url: '/api/agents?page=1&limit=10' },
      { name: 'المستخدمين', url: '/api/users?page=1&limit=10&sortField=createdAt&sortOrder=desc' },
      { name: 'الأنشطة الحديثة', url: '/api/dashboard/recent-activity' },
      { name: 'إحصائيات الأمان', url: '/api/security/stats' }
    ];

    let missingAPIsPassed = true;
    
    for (const api of missingAPIs) {
      const response = await fetch(`http://localhost:8080${api.url}`);
      console.log(`   📡 ${api.name}: ${response.status}`);
      
      if (!response.ok) {
        missingAPIsPassed = false;
        console.log(`   ❌ ${api.name} فشل`);
      } else {
        console.log(`   ✅ ${api.name} نجح`);
      }
    }
    
    if (missingAPIsPassed) {
      passedTests++;
      console.log('   🎉 جميع APIs المفقودة تعمل!');
    } else {
      console.log('   ❌ بعض APIs المفقودة لا تعمل');
    }
    console.log('');

    // اختبار 2: External APIs للمطورين
    console.log('2️⃣ اختبار External APIs للمطورين:');
    totalTests++;
    
    // Health Check
    const healthResponse = await fetch('http://localhost:8080/api/external/health');
    console.log(`   📡 Health Check: ${healthResponse.status}`);
    
    // Verify Direct - عميل نشط
    const activeResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      })
    });
    console.log(`   📡 Verify Direct (نشط): ${activeResponse.status}`);
    
    // Verify Direct - عميل غير نشط
    const inactiveResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1005,
        client_token: '2NPYjXzuQeu7'
      })
    });
    console.log(`   📡 Verify Direct (غير نشط): ${inactiveResponse.status}`);
    
    if (healthResponse.ok && activeResponse.ok && inactiveResponse.ok) {
      const inactiveData = await inactiveResponse.json();
      if (inactiveData.status === 'success' && inactiveData.client_status === 0) {
        passedTests++;
        console.log('   ✅ External APIs تعمل وتعرض حالة العميل غير النشط بشكل صحيح!');
      } else {
        console.log('   ⚠️ External APIs تعمل لكن لا تعرض حالة العميل غير النشط بشكل صحيح');
      }
    } else {
      console.log('   ❌ بعض External APIs لا تعمل');
    }
    console.log('');

    // اختبار 3: حالة العميل غير النشط
    console.log('3️⃣ اختبار حالة العميل غير النشط:');
    totalTests++;
    
    const inactiveTestResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1005,
        client_token: '2NPYjXzuQeu7'
      })
    });

    console.log(`   📡 Status: ${inactiveTestResponse.status}`);
    
    if (inactiveTestResponse.ok) {
      const inactiveTestData = await inactiveTestResponse.json();
      console.log(`   📊 Response: ${JSON.stringify(inactiveTestData)}`);
      
      if (inactiveTestData.status === 'success' && inactiveTestData.client_status === 0) {
        passedTests++;
        console.log('   ✅ النظام يعرض حالة العميل غير النشط بشكل صحيح!');
        console.log('   📝 الرد الصحيح: {"status":"success","client_status":0}');
      } else {
        console.log('   ❌ النظام لا يعرض حالة العميل غير النشط بشكل صحيح');
        console.log(`   📝 الرد الحالي: ${JSON.stringify(inactiveTestData)}`);
      }
    } else {
      console.log('   ❌ فشل في التحقق من العميل غير النشط');
    }
    console.log('');

    // اختبار 4: Dashboard Stats
    console.log('4️⃣ اختبار Dashboard Stats:');
    totalTests++;
    
    const statsResponse = await fetch('http://localhost:8080/api/dashboard/stats');
    console.log(`   📡 Status: ${statsResponse.status}`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log(`   📊 إجمالي المستخدمين: ${statsData.totalUsers}`);
      console.log(`   📊 إجمالي العملاء: ${statsData.totalClients}`);
      console.log(`   📊 إجمالي الوكلاء: ${statsData.totalAgents}`);
      console.log(`   📊 إجمالي سجلات البيانات: ${statsData.totalDataRecords}`);
      
      if (statsData.totalUsers > 0 && statsData.totalClients > 0 && statsData.totalAgents > 0) {
        passedTests++;
        console.log('   ✅ Dashboard Stats يعمل ويعرض بيانات حقيقية!');
      } else {
        console.log('   ⚠️ Dashboard Stats يعمل لكن البيانات قد تكون فارغة');
      }
    } else {
      console.log('   ❌ Dashboard Stats لا يعمل');
    }
    console.log('');

    // اختبار 5: Data Records API
    console.log('5️⃣ اختبار Data Records API:');
    totalTests++;
    
    const dataResponse = await fetch('http://localhost:8080/api/data-records?page=1&limit=5');
    console.log(`   📡 Status: ${dataResponse.status}`);
    
    if (dataResponse.ok) {
      const dataData = await dataResponse.json();
      console.log(`   📊 إجمالي السجلات: ${dataData.total}`);
      console.log(`   📋 السجلات في هذه الصفحة: ${dataData.dataRecords?.length}`);
      
      if (dataData.total > 0 && dataData.dataRecords && dataData.dataRecords.length > 0) {
        passedTests++;
        console.log('   ✅ Data Records API يعمل ويعرض بيانات حقيقية!');
        console.log(`   📋 أول سجل: ID ${dataData.dataRecords[0].id}`);
      } else {
        console.log('   ⚠️ Data Records API يعمل لكن لا توجد بيانات');
      }
    } else {
      console.log('   ❌ Data Records API لا يعمل');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص الاختبار النهائي الشامل:');
    console.log(`📊 إجمالي الاختبارات: ${totalTests}`);
    console.log(`✅ الاختبارات الناجحة: ${passedTests}`);
    console.log(`❌ الاختبارات الفاشلة: ${totalTests - passedTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('');

    if (passedTests === totalTests) {
      console.log('🎉 جميع المشاكل تم إصلاحها بنجاح!');
      console.log('✅ APIs المفقودة: مُصلحة');
      console.log('✅ External APIs للمطورين: تعمل');
      console.log('✅ حالة العميل غير النشط: تُعرض بشكل صحيح');
      console.log('✅ Dashboard Stats: يعمل');
      console.log('✅ Data Records: يعمل');
      console.log('');
      console.log('🚀 النظام جاهز 100% للاستخدام!');
      console.log('🔧 المطورون يمكنهم استخدام النظام بثقة!');
      console.log('🏠 النظام الأساسي يعمل بدون أخطاء!');
    } else {
      console.log('⚠️ بعض المشاكل لا تزال موجودة:');
      
      if (passedTests < totalTests) {
        console.log('🔧 يرجى مراجعة الاختبارات الفاشلة أعلاه');
      }
    }

    console.log('\n📊 تفاصيل النتائج:');
    console.log('1️⃣ APIs المفقودة (الوكلاء، المستخدمين، الأنشطة، الأمان)');
    console.log('2️⃣ External APIs للمطورين (Health Check، Verify Direct)');
    console.log('3️⃣ حالة العميل غير النشط (client_status: 0)');
    console.log('4️⃣ Dashboard Stats (إحصائيات النظام)');
    console.log('5️⃣ Data Records API (بيانات السجلات)');

  } catch (error) {
    console.error('❌ خطأ في الاختبار النهائي الشامل:', error.message);
  }
}

testFinalComplete().catch(console.error);
