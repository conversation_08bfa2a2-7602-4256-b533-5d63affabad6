# 🏢 كود الوكلاء فقط - نظام التحقق من العملاء
# Yemen Client Management System - Agents Only Code

========================================
⚠️ تنبيه مهم:
========================================

✅ الوصول للـ API: الوكلاء فقط
❌ العملاء: لا يملكون حسابات للوصول
🎯 الغرض: الوكلاء يتحققون من بيانات العملاء

========================================
🏢 حسابات الوكلاء المعتمدة:
========================================

1. وكيل الغراسي:
   اسم المستخدم: alghurasi
   كلمة المرور: alghurasi123

2. وكيل المترب:
   اسم المستخدم: almutarib
   كلمة المرور: almutarib123

========================================
🚀 كيفية الاستخدام:
========================================

عنوان الخادم: http://***********:8080/api/external/verify-direct
طريقة الطلب: POST
نوع المحتوى: application/json

البيانات المطلوبة:
- agent_login_name: اسم المستخدم للوكيل
- agent_login_password: كلمة مرور الوكيل  
- client_code: رمز العميل المراد التحقق منه
- client_token: توكن العميل المراد التحقق منه

النتائج:
✅ {"status":"success","client_status":1} = العميل نشط
✅ {"status":"success","client_status":0} = العميل غير نشط
❌ {"status":"error"} = بيانات الوكيل خاطئة أو العميل غير موجود

========================================
💻 أكواد للوكلاء:
========================================

# ========================================
# 1. كود cURL (سطر الأوامر):
# ========================================

# وكيل الغراسي يتحقق من العميل 1000:
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "alghurasi",
    "agent_login_password": "alghurasi123",
    "client_code": "1000",
    "client_token": "ABC12345"
  }'

# وكيل المترب يتحقق من العميل 1001:
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "almutarib",
    "agent_login_password": "almutarib123",
    "client_code": "1001",
    "client_token": "XYZ67890"
  }'

# ========================================
# 2. كود PHP (لأنظمة الوكلاء):
# ========================================

<?php
class AgentVerifier {
    private $serverUrl = 'http://***********:8080/api/external/verify-direct';
    private $agentUser;
    private $agentPass;
    
    public function __construct($agentUser, $agentPass) {
        $this->agentUser = $agentUser;
        $this->agentPass = $agentPass;
    }
    
    public function verifyClient($clientCode, $clientToken) {
        $data = array(
            'agent_login_name' => $this->agentUser,
            'agent_login_password' => $this->agentPass,
            'client_code' => $clientCode,
            'client_token' => $clientToken
        );
        
        $options = array(
            'http' => array(
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => json_encode($data),
                'timeout' => 30
            )
        );
        
        $context = stream_context_create($options);
        $result = file_get_contents($this->serverUrl, false, $context);
        
        if ($result === FALSE) {
            return array('status' => 'error');
        }
        
        return json_decode($result, true);
    }
}

// استخدام وكيل الغراسي:
$alGhurasi = new AgentVerifier('alghurasi', 'alghurasi123');
$result = $alGhurasi->verifyClient('1000', 'ABC12345');

if ($result['status'] == 'success') {
    if ($result['client_status'] == 1) {
        echo "العميل 1000 نشط - يمكن المتابعة";
    } else {
        echo "العميل 1000 غير نشط - لا يمكن المتابعة";
    }
} else {
    echo "فشل التحقق - تحقق من بيانات الوكيل أو العميل";
}

// استخدام وكيل المترب:
$alMutarib = new AgentVerifier('almutarib', 'almutarib123');
$result = $alMutarib->verifyClient('1001', 'XYZ67890');

if ($result['status'] == 'success') {
    echo "العميل متحقق منه - الحالة: " . ($result['client_status'] == 1 ? 'نشط' : 'غير نشط');
} else {
    echo "فشل التحقق";
}
?>

# ========================================
# 3. كود JavaScript (لأنظمة الوكلاء):
# ========================================

class AgentVerifier {
    constructor(agentUser, agentPass) {
        this.agentUser = agentUser;
        this.agentPass = agentPass;
        this.serverUrl = 'http://***********:8080/api/external/verify-direct';
    }
    
    async verifyClient(clientCode, clientToken) {
        try {
            const response = await fetch(this.serverUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    agent_login_name: this.agentUser,
                    agent_login_password: this.agentPass,
                    client_code: clientCode,
                    client_token: clientToken
                })
            });

            const result = await response.json();
            
            if (result.status === 'success') {
                return {
                    success: true,
                    clientActive: result.client_status === 1
                };
            } else {
                return {
                    success: false,
                    error: 'فشل التحقق'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: 'خطأ في الاتصال'
            };
        }
    }
}

// استخدام وكيل الغراسي:
const alGhurasi = new AgentVerifier('alghurasi', 'alghurasi123');

const result = await alGhurasi.verifyClient('1000', 'ABC12345');

if (result.success) {
    if (result.clientActive) {
        console.log('العميل 1000 نشط - يمكن المتابعة');
    } else {
        console.log('العميل 1000 غير نشط - لا يمكن المتابعة');
    }
} else {
    console.log('فشل التحقق:', result.error);
}

// استخدام وكيل المترب:
const alMutarib = new AgentVerifier('almutarib', 'almutarib123');

const result2 = await alMutarib.verifyClient('1001', 'XYZ67890');
console.log('نتيجة التحقق:', result2);

# ========================================
# 4. كود Python (لأنظمة الوكلاء):
# ========================================

import requests
import json

class AgentVerifier:
    def __init__(self, agent_user, agent_pass):
        self.agent_user = agent_user
        self.agent_pass = agent_pass
        self.server_url = 'http://***********:8080/api/external/verify-direct'
    
    def verify_client(self, client_code, client_token):
        data = {
            'agent_login_name': self.agent_user,
            'agent_login_password': self.agent_pass,
            'client_code': client_code,
            'client_token': client_token
        }
        
        try:
            response = requests.post(self.server_url, json=data, timeout=30)
            result = response.json()
            
            if result['status'] == 'success':
                return {
                    'success': True,
                    'client_active': result['client_status'] == 1
                }
            else:
                return {
                    'success': False,
                    'error': 'فشل التحقق'
                }
        except:
            return {
                'success': False,
                'error': 'خطأ في الاتصال'
            }

# استخدام وكيل الغراسي:
al_ghurasi = AgentVerifier('alghurasi', 'alghurasi123')
result = al_ghurasi.verify_client('1000', 'ABC12345')

if result['success']:
    if result['client_active']:
        print('العميل 1000 نشط - يمكن المتابعة')
    else:
        print('العميل 1000 غير نشط - لا يمكن المتابعة')
else:
    print(f'فشل التحقق: {result["error"]}')

# استخدام وكيل المترب:
al_mutarib = AgentVerifier('almutarib', 'almutarib123')
result2 = al_mutarib.verify_client('1001', 'XYZ67890')
print(f'نتيجة التحقق: {result2}')

# ========================================
# 5. كود C# (لأنظمة الوكلاء):
# ========================================

using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class AgentVerifier
{
    private static readonly HttpClient client = new HttpClient();
    private readonly string agentUser;
    private readonly string agentPass;
    private readonly string serverUrl = "http://***********:8080/api/external/verify-direct";
    
    public AgentVerifier(string agentUser, string agentPass)
    {
        this.agentUser = agentUser;
        this.agentPass = agentPass;
    }
    
    public async Task<dynamic> VerifyClient(string clientCode, string clientToken)
    {
        try
        {
            var data = new {
                agent_login_name = agentUser,
                agent_login_password = agentPass,
                client_code = clientCode,
                client_token = clientToken
            };

            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(serverUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            var parsed = JsonConvert.DeserializeObject<dynamic>(result);

            if (parsed.status == "success")
            {
                return new {
                    success = true,
                    clientActive = (int)parsed.client_status == 1
                };
            }
            else
            {
                return new {
                    success = false,
                    error = "فشل التحقق"
                };
            }
        }
        catch
        {
            return new {
                success = false,
                error = "خطأ في الاتصال"
            };
        }
    }
}

// استخدام وكيل الغراسي:
var alGhurasi = new AgentVerifier("alghurasi", "alghurasi123");
var result = await alGhurasi.VerifyClient("1000", "ABC12345");

if (result.success)
{
    if (result.clientActive)
    {
        Console.WriteLine("العميل 1000 نشط - يمكن المتابعة");
    }
    else
    {
        Console.WriteLine("العميل 1000 غير نشط - لا يمكن المتابعة");
    }
}
else
{
    Console.WriteLine($"فشل التحقق: {result.error}");
}

// استخدام وكيل المترب:
var alMutarib = new AgentVerifier("almutarib", "almutarib123");
var result2 = await alMutarib.VerifyClient("1001", "XYZ67890");
Console.WriteLine($"نتيجة التحقق: {result2}");

========================================
🎯 سيناريوهات الاستخدام:
========================================

1. 🏢 وكيل الغراسي يريد التحقق من العميل:
   - يستخدم بياناته: alghurasi / alghurasi123
   - يدخل رمز العميل وتوكنه
   - يحصل على حالة العميل (نشط أو غير نشط)

2. 🏢 وكيل المترب يريد التحقق من العميل:
   - يستخدم بياناته: almutarib / almutarib123
   - يدخل رمز العميل وتوكنه
   - يحصل على حالة العميل (نشط أو غير نشط)

3. ❌ العميل لا يستطيع الوصول:
   - العملاء لا يملكون حسابات
   - لا يمكنهم استخدام الـ API مباشرة
   - بياناتهم تُستخدم للتحقق فقط

========================================
🔒 الأمان:
========================================

✅ فقط الوكلاء المعتمدون يمكنهم الوصول
✅ كل وكيل يستخدم بياناته الخاصة
✅ العملاء محميون - لا وصول مباشر لهم
✅ جميع العمليات مسجلة ومراقبة

========================================
📋 ملخص للوكلاء:
========================================

🎯 الغرض: التحقق من حالة العملاء
🔑 الوصول: الوكلاء فقط
📊 النتيجة: حالة العميل (1 أو 0) أو خطأ
🌐 العنوان: http://***********:8080/api/external/verify-direct

🏢 الوكلاء المعتمدون:
  • الغراسي: alghurasi / alghurasi123
  • المترب: almutarib / almutarib123

🎉 بسيط وآمن ومخصص للوكلاء فقط!
