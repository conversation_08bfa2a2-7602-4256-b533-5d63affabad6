# 🚀 تعليمات تشغيل الخادم
# Yemen Client Management System - Server Setup Instructions

========================================
✅ تم تشغيل الخادم بنجاح:
========================================

## 📊 حالة الخادم:
✅ **الخادم يعمل:** working-server.js
✅ **المنفذ:** 8080
✅ **العنوان المحلي:** http://localhost:8080
✅ **العنوان الخارجي:** http://***********:8080

## 📋 معلومات قاعدة البيانات:
✅ **الاتصال:** متصل بنجاح
✅ **الوكلاء:** 5
✅ **العملاء:** 6
✅ **المستخدمون:** 4

========================================
🔧 طرق تشغيل الخادم:
========================================

## الطريقة الأولى: ملف Batch (الأسهل)
```bash
# انقر نقراً مزدوجاً على:
C:\yemclinet\تشغيل-الخادم.bat
```

## الطريقة الثانية: سطر الأوامر
```bash
# افتح Command Prompt أو PowerShell
cd C:\yemclinet
node server\working-server.js
```

## الطريقة الثالثة: الخادم البسيط (للاختبار)
```bash
cd C:\yemclinet
node خادم-بسيط.js
```

========================================
🧪 اختبار الخادم:
========================================

## 1. صفحة الاختبار التفاعلية:
📄 **افتح:** `اختبار-الخادم.html`
**المميزات:**
- فحص الاتصال بالخادم
- اختبار تسجيل الدخول
- فحص حقل isActive
- تشخيص المشاكل

## 2. اختبار سريع في المتصفح:
```
http://localhost:8080/health
```

## 3. اختبار API:
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"loginName": "hash8080", "password": "hash8080", "deviceId": "koadbqwog_1751136029819"}'
```

========================================
🔍 التحقق من حل مشكلة isActive:
========================================

## الخطوات:
1. **تشغيل الخادم:** استخدم `تشغيل-الخادم.bat`
2. **اختبار API:** افتح `اختبار-الخادم.html`
3. **فحص isActive:** انقر "فحص isActive"
4. **النتيجة المتوقعة:** `isActive: true`

## إذا كان isActive موجود:
✅ **المشكلة محلولة!**
- امسح localStorage
- سجل دخول جديد
- افتح الملف الشخصي
- تحقق من عرض "نشط ✅"

## إذا كان isActive غير موجود:
❌ **الخادم لم يتم تحديثه**
- تأكد من تشغيل working-server.js
- أعد تشغيل الخادم
- اختبر مرة أخرى

========================================
🛠️ حل المشاكل الشائعة:
========================================

## المشكلة: الخادم لا يبدأ
### الأسباب المحتملة:
- المنفذ 8080 مستخدم
- مشكلة في Node.js
- مكتبات مفقودة

### الحلول:
```bash
# 1. تحقق من المنفذ
netstat -ano | findstr :8080

# 2. قتل العملية إذا لزم الأمر
taskkill /PID [رقم_العملية] /F

# 3. تثبيت المكتبات
cd C:\yemclinet\server
npm install

# 4. تشغيل الخادم
node working-server.js
```

## المشكلة: خطأ في قاعدة البيانات
### الحلول:
```bash
# 1. تحقق من Prisma
cd C:\yemclinet\server
npx prisma generate

# 2. تحقق من قاعدة البيانات
npx prisma db push
```

## المشكلة: فشل في الاتصال
### الحلول:
1. **تحقق من الخادم:** هل يعمل؟
2. **تحقق من المنفذ:** 8080
3. **تحقق من جدار الحماية:** Windows Firewall
4. **جرب العنوان المحلي:** localhost:8080

========================================
📁 ملفات الخادم:
========================================

## الخوادم المتاحة:
1. **working-server.js** - الخادم الرئيسي (محدث)
2. **complete-server.js** - خادم كامل (محدث)
3. **main-server.js** - خادم أساسي (محدث)
4. **خادم-بسيط.js** - خادم اختبار (جديد)

## ملفات التشغيل:
1. **تشغيل-الخادم.bat** - تشغيل تلقائي
2. **اختبار-الخادم.html** - اختبار تفاعلي

## ملفات الإصلاح:
1. **إصلاح-localStorage.html** - مسح البيانات القديمة
2. **اختبار-حالة-المستخدم.html** - اختبار شامل

========================================
🎯 الخطوات التالية:
========================================

## بعد تشغيل الخادم:
1. **اختبر الخادم:** `اختبار-الخادم.html`
2. **تحقق من isActive:** يجب أن يكون `true`
3. **امسح localStorage:** `إصلاح-localStorage.html`
4. **سجل دخول جديد:** للنظام
5. **افتح الملف الشخصي:** تحقق من "نشط ✅"

## إذا كانت المشكلة مستمرة:
1. **تحقق من الخادم:** هل يرسل isActive؟
2. **امسح localStorage:** بيانات قديمة
3. **أعد تشغيل المتصفح:** تحديث الجلسة
4. **جرب متصفح آخر:** للتأكد

========================================
📞 للدعم:
========================================

## معلومات مفيدة للدعم:
- **نظام التشغيل:** Windows Server 2019
- **Node.js:** مثبت
- **قاعدة البيانات:** PostgreSQL
- **المنفذ:** 8080
- **المشكلة:** isActive لا يظهر في الملف الشخصي

## ملفات الاختبار:
- `اختبار-الخادم.html` - اختبار شامل
- `إصلاح-localStorage.html` - إصلاح البيانات
- `تشغيل-الخادم.bat` - تشغيل سهل

========================================
🎉 الخلاصة:
========================================

✅ **الخادم يعمل بنجاح**
✅ **تم تحديث جميع الخوادم لإرسال isActive**
✅ **أدوات الاختبار والإصلاح متاحة**
✅ **تعليمات شاملة للتشغيل والصيانة**

🎯 **الآن:**
1. اختبر الخادم
2. تحقق من isActive
3. امسح localStorage إذا لزم الأمر
4. سجل دخول جديد
5. تحقق من الملف الشخصي

🚀 **النظام جاهز للاستخدام!**
