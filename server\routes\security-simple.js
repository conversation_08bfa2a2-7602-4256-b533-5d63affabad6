const express = require('express')
const router = express.Router()

// إحصائيات الأمان
router.get('/stats', (req, res) => {
  console.log('📊 Security stats requested - simple route')
  res.json({
    successfulLogins: 15,
    failedLogins: 3,
    totalAttempts: 18,
    blockedIPs: 0,
    activeDevices: 1,
    suspiciousActivity: 0,
    uniqueIPs: 2,
    lastSecurityScan: new Date().toISOString(),
    last24Hours: {
      successfulLogins: 15,
      failedAttempts: 3
    }
  })
})

// محاولات تسجيل الدخول
router.get('/login-attempts', (req, res) => {
  console.log('📋 Login attempts requested - simple route')
  res.json({
    attempts: [
      {
        id: 1,
        type: 'success',
        username: 'hash8080',
        ip: '*************',
        userAgent: 'Browser',
        timestamp: new Date().toISOString(),
        deviceId: 'honbi5nms_1751046183491',
        reason: null
      }
    ],
    total: 1,
    page: 1,
    limit: 20,
    totalPages: 1
  })
})

module.exports = router
