const { PrismaClient } = require('./server/node_modules/@prisma/client')

const prisma = new PrismaClient()

async function cleanupDatabase() {
  try {
    console.log('🧹 تنظيف قاعدة البيانات...')
    console.log('=' .repeat(50))
    
    // 1. فحص العمليات المعلقة أو المكررة
    console.log('🔍 فحص العمليات المكررة...')
    
    const duplicateOperations = await prisma.dataRecord.groupBy({
      by: ['agentId', 'clientId', 'operationDate'],
      having: {
        id: {
          _count: {
            gt: 1
          }
        }
      },
      _count: {
        id: true
      }
    })

    if (duplicateOperations.length > 0) {
      console.log(`❌ تم العثور على ${duplicateOperations.length} عملية مكررة`)
      duplicateOperations.forEach((dup, index) => {
        console.log(`  ${index + 1}. الوكيل ${dup.agentId} - العميل ${dup.clientId} - ${dup._count.id} مرات`)
      })
    } else {
      console.log('✅ لا توجد عمليات مكررة')
    }

    // 2. فحص العمليات اليتيمة (بدون وكيل)
    console.log('')
    console.log('🔍 فحص العمليات اليتيمة...')
    
    const orphanOperations = await prisma.dataRecord.findMany({
      where: {
        agent: null
      },
      select: {
        id: true,
        agentId: true,
        clientCode: true,
        operationDate: true
      }
    })

    if (orphanOperations.length > 0) {
      console.log(`❌ تم العثور على ${orphanOperations.length} عملية يتيمة`)
      orphanOperations.forEach((op, index) => {
        console.log(`  ${index + 1}. العملية ${op.id} - الوكيل ${op.agentId} (غير موجود)`)
      })
    } else {
      console.log('✅ لا توجد عمليات يتيمة')
    }

    // 3. فحص محاولات تسجيل الدخول القديمة
    console.log('')
    console.log('🔍 فحص محاولات تسجيل الدخول القديمة...')
    
    const oldAttempts = await prisma.loginAttempt.count({
      where: {
        createdAt: {
          lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // أقدم من 30 يوم
        }
      }
    })

    console.log(`📊 محاولات تسجيل الدخول القديمة (أكثر من 30 يوم): ${oldAttempts}`)

    // 4. إحصائيات عامة
    console.log('')
    console.log('📊 إحصائيات قاعدة البيانات:')
    console.log('=' .repeat(30))
    
    const stats = {
      agents: await prisma.agent.count(),
      clients: await prisma.client.count(),
      users: await prisma.user.count(),
      dataRecords: await prisma.dataRecord.count(),
      loginAttempts: await prisma.loginAttempt.count()
    }

    console.log(`👥 الوكلاء: ${stats.agents}`)
    console.log(`👤 العملاء: ${stats.clients}`)
    console.log(`🔐 المستخدمين: ${stats.users}`)
    console.log(`📊 العمليات: ${stats.dataRecords}`)
    console.log(`🔑 محاولات تسجيل الدخول: ${stats.loginAttempts}`)

    // 5. اقتراحات التنظيف
    console.log('')
    console.log('💡 اقتراحات التنظيف:')
    console.log('=' .repeat(30))
    
    if (oldAttempts > 1000) {
      console.log('🗑️  حذف محاولات تسجيل الدخول القديمة (أكثر من 30 يوم)')
    }
    
    if (orphanOperations.length > 0) {
      console.log('🗑️  حذف العمليات اليتيمة (بدون وكيل)')
    }
    
    if (duplicateOperations.length > 0) {
      console.log('🗑️  حذف العمليات المكررة (الاحتفاظ بالأحدث)')
    }

    console.log('')
    console.log('⚠️  لن أنفذ عمليات الحذف تلقائياً')
    console.log('💡 راجع النتائج واتخذ القرار المناسب')

  } catch (error) {
    console.error('❌ خطأ في تنظيف قاعدة البيانات:', error)
  } finally {
    await prisma.$disconnect()
  }
}

cleanupDatabase()
