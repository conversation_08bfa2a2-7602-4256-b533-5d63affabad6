{"name": "yemclient-system", "version": "1.0.0", "description": "نظام إدارة العملاء والوكلاء - نظام شامل مع واجهة عربية احترافية", "main": "server/server.js", "scripts": {"start": "cd server && npm start", "dev": "cd server && npm run dev", "build": "cd client && npm run build", "install-all": "cd server && npm install && cd ../client && npm install", "setup": "npm run install-all && npm run build", "db:generate": "cd server && npx prisma generate", "db:migrate": "cd server && npx prisma migrate dev", "db:studio": "cd server && npx prisma studio"}, "keywords": ["client-management", "agent-management", "arabic-ui", "react", "nodejs", "postgresql", "material-ui", "rtl-layout"], "author": "Yemen Client System", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "local"}}