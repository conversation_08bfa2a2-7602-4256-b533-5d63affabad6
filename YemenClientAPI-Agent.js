/**
 * مكتبة ربط نظام إدارة العملاء اليمني - للوكلاء
 * Yemen Client Management System - Agent SDK
 *
 * @version 1.0.0
 * <AUTHOR> Client Management Team
 * @license MIT
 */

class YemenClientAgentAPI {
  /**
   * إنشاء مثيل جديد من API الوكيل
   * @param {string} baseUrl - عنوان الخادم الأساسي
   * @param {string} loginName - اسم تسجيل الدخول للوكيل
   * @param {string} loginPassword - كلمة مرور الوكيل
   */
  constructor(baseUrl, loginName, loginPassword) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // إزالة / من النهاية
    this.apiUrl = `${this.baseUrl}/api/external`
    this.loginName = loginName
    this.loginPassword = loginPassword
    this.token = null
    this.tokenExpiry = null
    this.agentInfo = null
  }

  /**
   * تسجيل دخول الوكيل والحصول على التوكن
   * @returns {Promise<Object>} معلومات الوكيل والتوكن
   */
  async authenticate() {
    try {
      const response = await this._makeRequest('POST', '/agent/auth', {
        login_name: this.loginName,
        login_password: this.loginPassword
      })

      if (response.status === 'success') {
        this.token = response.data.token
        this.tokenExpiry = new Date(response.data.expires_at)
        this.agentInfo = {
          id: response.data.agent_id,
          name: response.data.agent_name,
          type: response.data.agency_type
        }

        console.log('✅ Agent authenticated successfully:', this.agentInfo.name)
        return response.data
      } else {
        throw new Error(response.message || 'Authentication failed')
      }
    } catch (error) {
      console.error('❌ Authentication error:', error.message)
      throw error
    }
  }

  /**
   * التحقق من صحة بيانات العميل
   * @param {string|number} clientCode - رمز العميل
   * @param {string} clientToken - توكن العميل
   * @returns {Promise<Object>} بيانات العميل
   */
  async verifyClient(clientCode, clientToken) {
    await this._ensureAuthenticated()

    try {
      const response = await this._makeRequest('POST', '/client/verify', {
        client_code: clientCode.toString(),
        token: clientToken
      }, true)

      if (response.status === 'success') {
        console.log('✅ Client verified:', response.data.client_name)
        return response.data
      } else {
        console.log('❌ Client verification failed:', response.message)
        return { error: response.message, code: response.error_code }
      }
    } catch (error) {
      console.error('❌ Client verification error:', error.message)
      throw error
    }
  }

  /**
   * الحصول على إحصائيات الوكيل
   * @returns {Promise<Object>} إحصائيات الوكيل
   */
  async getStats() {
    await this._ensureAuthenticated()

    try {
      const response = await this._makeRequest('GET', '/agent/stats', null, true)

      if (response.status === 'success') {
        return response.data
      } else {
        throw new Error(response.message || 'Failed to get stats')
      }
    } catch (error) {
      console.error('❌ Stats error:', error.message)
      throw error
    }
  }

  /**
   * الحصول على سجل العمليات
   * @param {number} page - رقم الصفحة (افتراضي: 1)
   * @param {number} limit - عدد النتائج (افتراضي: 10)
   * @returns {Promise<Object>} سجل العمليات
   */
  async getOperations(page = 1, limit = 10) {
    await this._ensureAuthenticated()

    try {
      const response = await this._makeRequest('GET', `/agent/operations?page=${page}&limit=${limit}`, null, true)

      if (response.status === 'success') {
        return response.data
      } else {
        throw new Error(response.message || 'Failed to get operations')
      }
    } catch (error) {
      console.error('❌ Operations error:', error.message)
      throw error
    }
  }

  /**
   * تسجيل خروج الوكيل
   * @returns {Promise<boolean>} نجح تسجيل الخروج أم لا
   */
  async logout() {
    if (!this.token) {
      return true
    }

    try {
      const response = await this._makeRequest('POST', '/agent/logout', {}, true)

      this.token = null
      this.tokenExpiry = null
      this.agentInfo = null

      console.log('✅ Agent logged out successfully')
      return response.status === 'success'
    } catch (error) {
      console.error('❌ Logout error:', error.message)
      return false
    }
  }

  /**
   * فحص حالة النظام
   * @returns {Promise<Object>} حالة النظام
   */
  async checkHealth() {
    try {
      const response = await this._makeRequest('GET', '/health')
      return response
    } catch (error) {
      console.error('❌ Health check error:', error.message)
      throw error
    }
  }

  /**
   * التحقق من صحة التوكن وتجديده إذا لزم الأمر
   * @private
   */
  async _ensureAuthenticated() {
    if (!this.token || (this.tokenExpiry && new Date() >= this.tokenExpiry)) {
      console.log('🔄 Token expired, re-authenticating...')
      await this.authenticate()
    }
  }

  /**
   * إرسال طلب HTTP
   * @private
   * @param {string} method - نوع الطلب
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {boolean} requireAuth - يتطلب مصادقة
   * @returns {Promise<Object>} الاستجابة
   */
  async _makeRequest(method, endpoint, data = null, requireAuth = false) {
    const url = `${this.apiUrl}${endpoint}`
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'YemenClientAPI-Agent/1.0.0'
      }
    }

    if (requireAuth && this.token) {
      options.headers['Authorization'] = `Bearer ${this.token}`
    }

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data)
    }

    // استخدام fetch في Node.js أو المتصفح
    let fetch
    if (typeof window !== 'undefined') {
      // في المتصفح
      fetch = window.fetch
    } else {
      // في Node.js
      try {
        const nodeFetch = require('node-fetch')
        fetch = nodeFetch.default || nodeFetch
      } catch (e) {
        throw new Error('node-fetch is required for Node.js. Install it with: npm install node-fetch')
      }
    }

    const response = await fetch(url, options)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * الحصول على معلومات الوكيل الحالي
   * @returns {Object|null} معلومات الوكيل
   */
  getAgentInfo() {
    return this.agentInfo
  }

  /**
   * التحقق من حالة تسجيل الدخول
   * @returns {boolean} مسجل دخول أم لا
   */
  isAuthenticated() {
    return !!(this.token && this.tokenExpiry && new Date() < this.tokenExpiry)
  }
}

// تصدير للاستخدام في Node.js والمتصفح
if (typeof module !== 'undefined' && module.exports) {
  module.exports = YemenClientAgentAPI
} else if (typeof window !== 'undefined') {
  window.YemenClientAgentAPI = YemenClientAgentAPI
}

/**
 * مثال على الاستخدام:
 *
 * // إنشاء مثيل جديد
 * const agent = new YemenClientAgentAPI(
 *   'http://185.11.8.26:8080',
 *   'agent001',
 *   'agent123'
 * )
 *
 * // تسجيل الدخول
 * await agent.authenticate()
 *
 * // التحقق من عميل
 * const clientData = await agent.verifyClient('1000', 'ABC12345')
 *
 * // الحصول على الإحصائيات
 * const stats = await agent.getStats()
 *
 * // تسجيل الخروج
 * await agent.logout()
 */
