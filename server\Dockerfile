# استخدام Node.js 18 Alpine
FROM node:18-alpine

# تعيين مجلد العمل
WORKDIR /app

# نسخ ملفات package
COPY package*.json ./

# تثبيت المتطلبات
RUN npm ci --only=production

# نسخ باقي الملفات
COPY . .

# إنشاء Prisma client
RUN npx prisma generate

# إنشاء مستخدم غير root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# تغيير ملكية الملفات
RUN chown -R nodejs:nodejs /app
USER nodejs

# كشف المنفذ
EXPOSE 3000

# أمر البداية
CMD ["sh", "-c", "npx prisma migrate deploy && npm run db:seed && npm start"]
