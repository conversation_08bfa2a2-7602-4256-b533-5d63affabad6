// اختبار حالة المستخدم في الملف الشخصي
// Test User Status in Profile

const testUserStatus = async () => {
  console.log('🧪 اختبار حالة المستخدم في الملف الشخصي')
  console.log('=' .repeat(60))
  
  try {
    // محاكاة تسجيل الدخول
    console.log('🔐 محاكاة تسجيل الدخول...')
    
    const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        loginName: 'hash8080',
        password: 'hash8080',
        deviceId: 'test_device_123'
      })
    })
    
    const loginResult = await loginResponse.json()
    
    console.log('📊 نتيجة تسجيل الدخول:')
    console.log(`   الحالة: ${loginResponse.ok ? 'نجح' : 'فشل'}`)
    console.log(`   الكود: ${loginResponse.status}`)
    
    if (loginResponse.ok && loginResult.user) {
      console.log('')
      console.log('👤 بيانات المستخدم المستلمة:')
      console.log(`   الرقم: ${loginResult.user.id}`)
      console.log(`   الاسم: ${loginResult.user.username}`)
      console.log(`   اسم الدخول: ${loginResult.user.loginName}`)
      console.log(`   الحالة: ${loginResult.user.isActive !== undefined ? (loginResult.user.isActive ? 'نشط ✅' : 'غير نشط ❌') : 'غير محدد ⚠️'}`)
      console.log(`   مدير النظام: ${loginResult.user.permissions?.isAdmin ? 'نعم ✅' : 'لا ❌'}`)
      
      console.log('')
      console.log('🔍 تحليل المشكلة:')
      
      if (loginResult.user.isActive === undefined) {
        console.log('❌ المشكلة: isActive غير موجود في بيانات المستخدم')
        console.log('🔧 الحل: تم إضافة isActive إلى جميع خوادم تسجيل الدخول')
      } else if (loginResult.user.isActive === true) {
        console.log('✅ تم حل المشكلة: isActive موجود ويظهر كـ نشط')
      } else {
        console.log('⚠️ المستخدم غير نشط في قاعدة البيانات')
      }
      
      // محاكاة عرض الملف الشخصي
      console.log('')
      console.log('📋 محاكاة عرض الملف الشخصي:')
      console.log('┌─────────────────────────────────────┐')
      console.log('│ 👤 الملف الشخصي                    │')
      console.log('├─────────────────────────────────────┤')
      console.log(`│ رقم المستخدم: ${loginResult.user.id}                     │`)
      console.log(`│ اسم المستخدم: ${loginResult.user.username}           │`)
      console.log(`│ اسم الدخول: ${loginResult.user.loginName}             │`)
      console.log(`│ الحالة: ${loginResult.user.isActive ? 'نشط ✅' : 'غير نشط ❌'}              │`)
      console.log(`│ النوع: ${loginResult.user.permissions?.isAdmin ? 'مدير النظام' : 'مستخدم عادي'}        │`)
      console.log('└─────────────────────────────────────┘')
      
    } else {
      console.log('❌ فشل في تسجيل الدخول:')
      console.log(`   الخطأ: ${loginResult.error || 'غير محدد'}`)
    }
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message)
  }
}

// تشغيل الاختبار
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch')
  testUserStatus()
} else {
  // Browser environment
  console.log('🌐 تشغيل الاختبار في المتصفح...')
  testUserStatus()
}
