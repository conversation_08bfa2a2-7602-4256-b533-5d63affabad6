const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function fixMultiDeviceSystem() {
  try {
    console.log('🔧 إصلاح نظام الأجهزة المتعددة')
    console.log('===============================')
    
    // البحث عن المستخدم محمد الحاشدي
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        isActive: true
      }
    })

    if (!user) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }
    
    console.log(`👤 المستخدم: ${user.username}`)
    console.log(`📱 الجهاز الحالي: ${user.deviceId || 'غير محدد'}`)
    
    // الأجهزة المطلوبة
    const device1 = 'honbi5nms_1751046183491'  // الجهاز القديم
    const device2 = 'b78dex9jv_1751070014503'  // الجهاز الجديد
    
    console.log(`📱 الجهاز الأول: ${device1}`)
    console.log(`📱 الجهاز الثاني: ${device2}`)
    
    // إنشاء قائمة الأجهزة الصحيحة
    const correctDeviceList = `${device1},${device2}`
    
    console.log(`📱 قائمة الأجهزة المطلوبة: ${correctDeviceList}`)
    
    // تحديث المستخدم
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { 
        deviceId: correctDeviceList,
        isActive: true
      }
    })
    
    console.log('✅ تم تحديث المستخدم بنجاح!')
    
    // اختبار تسجيل الدخول لكل جهاز
    console.log('')
    console.log('🧪 اختبار تسجيل الدخول للجهازين:')
    
    // اختبار الجهاز الأول
    try {
      const response1 = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          loginName: 'hash8080',
          password: 'hash8080',
          deviceId: device1
        })
      })
      
      const result1 = await response1.json()
      console.log(`📱 الجهاز الأول: ${response1.ok ? '✅ نجح' : '❌ فشل'} - ${result1.error || 'تم بنجاح'}`)
      
    } catch (error) {
      console.log(`📱 الجهاز الأول: ❌ خطأ في الاتصال - ${error.message}`)
    }
    
    // اختبار الجهاز الثاني
    try {
      const response2 = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          loginName: 'hash8080',
          password: 'hash8080',
          deviceId: device2
        })
      })
      
      const result2 = await response2.json()
      console.log(`📱 الجهاز الثاني: ${response2.ok ? '✅ نجح' : '❌ فشل'} - ${result2.error || 'تم بنجاح'}`)
      
    } catch (error) {
      console.log(`📱 الجهاز الثاني: ❌ خطأ في الاتصال - ${error.message}`)
    }
    
    // عرض النتيجة النهائية
    const finalDevices = updatedUser.deviceId.split(',').map(id => id.trim())
    
    console.log('')
    console.log('🎉 النتيجة النهائية:')
    console.log(`👤 المستخدم: ${updatedUser.username}`)
    console.log(`📱 عدد الأجهزة: ${finalDevices.length}`)
    console.log(`📱 قائمة الأجهزة:`)
    finalDevices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device}`)
    })
    
  } catch (error) {
    console.error('❌ خطأ في إصلاح نظام الأجهزة:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixMultiDeviceSystem()
