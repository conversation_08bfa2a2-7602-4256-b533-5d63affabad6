/**
 * اختبار شامل لنظام API الخارجي
 * Yemen Client Management System - External API Test
 */

const axios = require('axios')

// إعدادات الاختبار
const API_BASE_URL = 'http://localhost:8080/api/external'
const TEST_AGENT = {
  login_name: 'agent001',
  login_password: 'agent123'
}
const TEST_CLIENT = {
  client_code: '1000',
  token: 'ABC12345'
}

// متغيرات عامة
let agentToken = null

/**
 * اختبار صحة API
 */
async function testHealthCheck() {
  console.log('\n🔍 Testing API Health Check...')
  try {
    const response = await axios.get(`${API_BASE_URL}/health`)
    console.log('✅ Health Check:', response.data)
    return true
  } catch (error) {
    console.error('❌ Health Check Failed:', error.message)
    return false
  }
}

/**
 * اختبار مصادقة الوكيل
 */
async function testAgentAuth() {
  console.log('\n🔐 Testing Agent Authentication...')
  try {
    const response = await axios.post(`${API_BASE_URL}/agent/auth`, TEST_AGENT)
    console.log('✅ Agent Auth Success:', response.data)
    
    if (response.data.status === 'success') {
      agentToken = response.data.data.token
      console.log('🎫 Token received:', agentToken.substring(0, 20) + '...')
      return true
    }
    return false
  } catch (error) {
    console.error('❌ Agent Auth Failed:', error.response?.data || error.message)
    return false
  }
}

/**
 * اختبار مصادقة خاطئة
 */
async function testInvalidAuth() {
  console.log('\n🚫 Testing Invalid Authentication...')
  try {
    const response = await axios.post(`${API_BASE_URL}/agent/auth`, {
      login_name: 'invalid_agent',
      login_password: 'wrong_password'
    })
    console.log('❌ Should have failed but got:', response.data)
    return false
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Invalid Auth correctly rejected:', error.response.data)
      return true
    }
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

/**
 * اختبار التحقق من العميل
 */
async function testClientVerification() {
  console.log('\n👤 Testing Client Verification...')
  if (!agentToken) {
    console.error('❌ No agent token available')
    return false
  }

  try {
    const response = await axios.post(
      `${API_BASE_URL}/client/verify`,
      TEST_CLIENT,
      {
        headers: {
          'Authorization': `Bearer ${agentToken}`,
          'Content-Type': 'application/json'
        }
      }
    )
    console.log('✅ Client Verification:', response.data)
    return true
  } catch (error) {
    console.error('❌ Client Verification Failed:', error.response?.data || error.message)
    return false
  }
}

/**
 * اختبار عميل غير موجود
 */
async function testInvalidClient() {
  console.log('\n🚫 Testing Invalid Client...')
  if (!agentToken) {
    console.error('❌ No agent token available')
    return false
  }

  try {
    const response = await axios.post(
      `${API_BASE_URL}/client/verify`,
      {
        client_code: '9999',
        token: 'INVALID'
      },
      {
        headers: {
          'Authorization': `Bearer ${agentToken}`,
          'Content-Type': 'application/json'
        }
      }
    )
    console.log('❌ Should have failed but got:', response.data)
    return false
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Invalid Client correctly rejected:', error.response.data)
      return true
    }
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

/**
 * اختبار إحصائيات الوكيل
 */
async function testAgentStats() {
  console.log('\n📊 Testing Agent Statistics...')
  if (!agentToken) {
    console.error('❌ No agent token available')
    return false
  }

  try {
    const response = await axios.get(
      `${API_BASE_URL}/agent/stats`,
      {
        headers: {
          'Authorization': `Bearer ${agentToken}`
        }
      }
    )
    console.log('✅ Agent Stats:', response.data)
    return true
  } catch (error) {
    console.error('❌ Agent Stats Failed:', error.response?.data || error.message)
    return false
  }
}

/**
 * اختبار سجل العمليات
 */
async function testAgentOperations() {
  console.log('\n📋 Testing Agent Operations...')
  if (!agentToken) {
    console.error('❌ No agent token available')
    return false
  }

  try {
    const response = await axios.get(
      `${API_BASE_URL}/agent/operations?page=1&limit=5`,
      {
        headers: {
          'Authorization': `Bearer ${agentToken}`
        }
      }
    )
    console.log('✅ Agent Operations:', response.data)
    return true
  } catch (error) {
    console.error('❌ Agent Operations Failed:', error.response?.data || error.message)
    return false
  }
}

/**
 * اختبار الوصول بدون توكن
 */
async function testUnauthorizedAccess() {
  console.log('\n🚫 Testing Unauthorized Access...')
  try {
    const response = await axios.get(`${API_BASE_URL}/agent/stats`)
    console.log('❌ Should have failed but got:', response.data)
    return false
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Unauthorized access correctly rejected:', error.response.data)
      return true
    }
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

/**
 * اختبار تسجيل الخروج
 */
async function testAgentLogout() {
  console.log('\n🚪 Testing Agent Logout...')
  if (!agentToken) {
    console.error('❌ No agent token available')
    return false
  }

  try {
    const response = await axios.post(
      `${API_BASE_URL}/agent/logout`,
      {},
      {
        headers: {
          'Authorization': `Bearer ${agentToken}`
        }
      }
    )
    console.log('✅ Agent Logout:', response.data)
    return true
  } catch (error) {
    console.error('❌ Agent Logout Failed:', error.response?.data || error.message)
    return false
  }
}

/**
 * تشغيل جميع الاختبارات
 */
async function runAllTests() {
  console.log('🚀 Starting Yemen Client Management API Tests...')
  console.log('=' .repeat(50))

  const tests = [
    { name: 'Health Check', func: testHealthCheck },
    { name: 'Agent Authentication', func: testAgentAuth },
    { name: 'Invalid Authentication', func: testInvalidAuth },
    { name: 'Client Verification', func: testClientVerification },
    { name: 'Invalid Client', func: testInvalidClient },
    { name: 'Agent Statistics', func: testAgentStats },
    { name: 'Agent Operations', func: testAgentOperations },
    { name: 'Unauthorized Access', func: testUnauthorizedAccess },
    { name: 'Agent Logout', func: testAgentLogout }
  ]

  let passed = 0
  let failed = 0

  for (const test of tests) {
    try {
      const result = await test.func()
      if (result) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      console.error(`❌ Test "${test.name}" crashed:`, error.message)
      failed++
    }
    
    // انتظار قصير بين الاختبارات
    await new Promise(resolve => setTimeout(resolve, 500))
  }

  console.log('\n' + '=' .repeat(50))
  console.log('📊 Test Results:')
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`)
  
  if (failed === 0) {
    console.log('🎉 All tests passed! API is working correctly.')
  } else {
    console.log('⚠️  Some tests failed. Please check the API implementation.')
  }
}

// تشغيل الاختبارات
if (require.main === module) {
  runAllTests().catch(console.error)
}

module.exports = {
  testHealthCheck,
  testAgentAuth,
  testClientVerification,
  runAllTests
}
