/**
 * تحديث مستخدم admin ليكون اسم الدخول admin
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function updateAdminUser() {
  console.log('🔧 تحديث مستخدم admin...\n');

  try {
    await prisma.$connect();
    console.log('✅ الاتصال بقاعدة البيانات نجح\n');

    // البحث عن المستخدم admin
    const adminUser = await prisma.user.findFirst({
      where: {
        username: 'admin'
      }
    });

    if (adminUser) {
      console.log('📝 تحديث بيانات المستخدم admin...');
      console.log(`   👤 الاسم الحالي: ${adminUser.username}`);
      console.log(`   🔑 اسم الدخول الحالي: ${adminUser.loginName}`);
      
      // تحديث اسم الدخول وكلمة المرور
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      await prisma.user.update({
        where: { id: adminUser.id },
        data: {
          loginName: 'admin',  // تغيير اسم الدخول إلى admin
          password: hashedPassword,
          isActive: true,
          permissions: {
            isAdmin: true,
            clients: { read: true, create: true, update: true, delete: true },
            agents: { read: true, create: true, update: true, delete: true },
            users: { read: true, create: true, update: true, delete: true },
            dataRecords: { read: true, create: true, update: true, delete: true },
            security: { read: true, create: true, update: true, delete: true }
          }
        }
      });
      
      console.log('✅ تم تحديث المستخدم admin بنجاح!');
      console.log('   🔑 اسم الدخول الجديد: admin');
      console.log('   🔒 كلمة المرور الجديدة: admin123');
      
    } else {
      console.log('❌ لم يتم العثور على مستخدم admin');
      
      // إنشاء مستخدم admin جديد
      console.log('📝 إنشاء مستخدم admin جديد...');
      
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const newAdmin = await prisma.user.create({
        data: {
          username: 'admin',
          loginName: 'admin',
          password: hashedPassword,
          isActive: true,
          permissions: {
            isAdmin: true,
            clients: { read: true, create: true, update: true, delete: true },
            agents: { read: true, create: true, update: true, delete: true },
            users: { read: true, create: true, update: true, delete: true },
            dataRecords: { read: true, create: true, update: true, delete: true },
            security: { read: true, create: true, update: true, delete: true }
          }
        }
      });
      
      console.log('✅ تم إنشاء مستخدم admin جديد!');
      console.log(`   👤 ID: ${newAdmin.id}`);
    }

    // اختبار تسجيل الدخول
    console.log('\n🧪 اختبار تسجيل الدخول...');
    
    const testUser = await prisma.user.findFirst({
      where: {
        loginName: 'admin'
      }
    });

    if (testUser) {
      const isPasswordValid = await bcrypt.compare('admin123', testUser.password);
      console.log(`🔑 اختبار admin / admin123: ${isPasswordValid ? '✅ صحيح' : '❌ خاطئ'}`);
      
      if (isPasswordValid) {
        console.log('\n🎉 معلومات تسجيل الدخول الصحيحة:');
        console.log('   👤 اسم المستخدم: admin');
        console.log('   🔑 كلمة المرور: admin123');
        
        // تسجيل محاولة دخول ناجحة
        await prisma.loginAttempt.create({
          data: {
            userId: testUser.id,
            ipAddress: '127.0.0.1',
            deviceId: 'admin_setup',
            success: true,
            userType: 'user'
          }
        });
        
        console.log('✅ تم تسجيل محاولة دخول ناجحة');
      }
    }

    // عرض جميع المستخدمين
    console.log('\n📋 جميع المستخدمين في النظام:');
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true
      },
      orderBy: { id: 'asc' }
    });

    allUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.username} (${user.loginName}) - ${user.isActive ? 'نشط' : 'غير نشط'}`);
    });

    console.log('\n✅ انتهى التحديث!');

  } catch (error) {
    console.error('❌ خطأ في تحديث المستخدم:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateAdminUser().catch(console.error);
