/**
 * اختبار Device Validation
 */

async function testDeviceValidation() {
  console.log('🔒 اختبار Device Validation...\n');

  try {
    // اختبار 1: تسجيل دخول hash8080 بجهاز صحيح
    console.log('1️⃣ اختبار hash8080 بجهاز صحيح:');
    const correctDevice = 'honbi5nms_1751046183491'; // الجهاز المحفوظ في قاعدة البيانات
    
    const correctResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'hash8080',
        password: 'hash8080',
        deviceId: correctDevice
      })
    });

    console.log(`   📡 Status: ${correctResponse.status}`);
    
    if (correctResponse.ok) {
      const data = await correctResponse.json();
      console.log('   ✅ تسجيل الدخول نجح مع الجهاز الصحيح!');
      console.log(`   👤 المستخدم: ${data.user?.username}`);
    } else {
      const errorData = await correctResponse.json();
      console.log('   ❌ تسجيل الدخول فشل مع الجهاز الصحيح!');
      console.log(`   📝 الخطأ: ${errorData.message || errorData.error}`);
    }
    console.log('');

    // اختبار 2: تسجيل دخول hash8080 بجهاز خاطئ
    console.log('2️⃣ اختبار hash8080 بجهاز خاطئ:');
    const wrongDevice = 'fixed_device_1751402952196'; // جهاز غير مسجل
    
    const wrongResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'hash8080',
        password: 'hash8080',
        deviceId: wrongDevice
      })
    });

    console.log(`   📡 Status: ${wrongResponse.status}`);
    
    if (wrongResponse.ok) {
      console.log('   ⚠️ تسجيل الدخول نجح مع الجهاز الخاطئ (غير متوقع)!');
    } else {
      const errorData = await wrongResponse.json();
      console.log('   ✅ تسجيل الدخول فشل مع الجهاز الخاطئ (كما هو متوقع)');
      console.log(`   📝 الرسالة: ${errorData.message}`);
      if (errorData.authorizedDevices) {
        console.log(`   📱 الأجهزة المصرحة: ${errorData.authorizedDevices.join(', ')}`);
      }
      if (errorData.currentDevice) {
        console.log(`   📱 الجهاز الحالي: ${errorData.currentDevice}`);
      }
    }
    console.log('');

    // اختبار 3: تسجيل دخول admin بجهاز صحيح
    console.log('3️⃣ اختبار admin بجهاز صحيح:');
    const adminDevice = 'gtx755747_1751237475723'; // الجهاز المحفوظ لـ admin
    
    const adminResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'admin',
        password: 'admin123',
        deviceId: adminDevice
      })
    });

    console.log(`   📡 Status: ${adminResponse.status}`);
    
    if (adminResponse.ok) {
      const data = await adminResponse.json();
      console.log('   ✅ تسجيل دخول admin نجح مع الجهاز الصحيح!');
      console.log(`   👤 المستخدم: ${data.user?.username}`);
    } else {
      const errorData = await adminResponse.json();
      console.log('   ❌ تسجيل دخول admin فشل مع الجهاز الصحيح!');
      console.log(`   📝 الخطأ: ${errorData.message || errorData.error}`);
    }
    console.log('');

    // اختبار 4: تسجيل دخول testuser (بدون أجهزة مسجلة)
    console.log('4️⃣ اختبار testuser (بدون أجهزة مسجلة):');
    const newDevice = 'new_device_' + Date.now();
    
    const testuserResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'testuser',
        password: 'testuser123',
        deviceId: newDevice
      })
    });

    console.log(`   📡 Status: ${testuserResponse.status}`);
    
    if (testuserResponse.ok) {
      const data = await testuserResponse.json();
      console.log('   ✅ تسجيل دخول testuser نجح وتم تسجيل الجهاز!');
      console.log(`   👤 المستخدم: ${data.user?.username}`);
      console.log(`   📱 الجهاز الجديد: ${newDevice}`);
    } else {
      const errorData = await testuserResponse.json();
      console.log('   ❌ تسجيل دخول testuser فشل!');
      console.log(`   📝 الخطأ: ${errorData.message || errorData.error}`);
    }
    console.log('');

    // اختبار 5: تسجيل دخول بدون Device ID
    console.log('5️⃣ اختبار تسجيل دخول بدون Device ID:');
    
    const noDeviceResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'admin',
        password: 'admin123'
        // لا يوجد deviceId
      })
    });

    console.log(`   📡 Status: ${noDeviceResponse.status}`);
    
    if (noDeviceResponse.ok) {
      console.log('   ⚠️ تسجيل الدخول نجح بدون Device ID (غير متوقع)!');
    } else {
      const errorData = await noDeviceResponse.json();
      console.log('   ✅ تسجيل الدخول فشل بدون Device ID (كما هو متوقع)');
      console.log(`   📝 الرسالة: ${errorData.message}`);
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار Device Validation:');
    console.log('✅ الجهاز الصحيح: يجب أن يسمح بالدخول');
    console.log('❌ الجهاز الخاطئ: يجب أن يرفض الدخول');
    console.log('📱 بدون أجهزة مسجلة: يجب أن يسجل الجهاز الجديد');
    console.log('🚫 بدون Device ID: يجب أن يرفض الدخول');

  } catch (error) {
    console.error('❌ خطأ في اختبار Device Validation:', error.message);
  }
}

testDeviceValidation().catch(console.error);
