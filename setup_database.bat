@echo off
echo ========================================
echo    إعداد قاعدة بيانات YemClient
echo ========================================

echo.
echo 1. إنشاء قاعدة البيانات...
createdb -U postgres yemclient_db
if %errorlevel% neq 0 (
    echo خطأ: فشل في إنشاء قاعدة البيانات
    echo تأكد من تشغيل PostgreSQL وصحة كلمة المرور
    pause
    exit /b 1
)

echo.
echo 2. تثبيت متطلبات الخادم...
cd server
call npm install
if %errorlevel% neq 0 (
    echo خطأ: فشل في تثبيت متطلبات الخادم
    pause
    exit /b 1
)

echo.
echo 3. إنشاء الجداول...
call npx prisma migrate dev --name init
if %errorlevel% neq 0 (
    echo خطأ: فشل في إنشاء الجداول
    pause
    exit /b 1
)

echo.
echo 4. إدخال البيانات التجريبية...
call npm run db:seed
if %errorlevel% neq 0 (
    echo خطأ: فشل في إدخال البيانات التجريبية
    pause
    exit /b 1
)

echo.
echo 5. تثبيت متطلبات العميل...
cd ..\client
call npm install
if %errorlevel% neq 0 (
    echo خطأ: فشل في تثبيت متطلبات العميل
    pause
    exit /b 1
)

cd ..

echo.
echo ========================================
echo       تم إعداد قاعدة البيانات بنجاح!
echo ========================================
echo.
echo للتشغيل:
echo   npm run dev
echo.
echo للوصول:
echo   لوحة التحكم: http://localhost:5173
echo   بيانات الدخول: admin / admin123456
echo.
pause
