/**
 * فحص شامل لقاعدة البيانات والخادم
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function checkDatabase() {
  console.log('🔍 فحص شامل لقاعدة البيانات...\n');

  try {
    // 1. فحص الاتصال
    console.log('1️⃣ فحص الاتصال بقاعدة البيانات:');
    await prisma.$connect();
    console.log('   ✅ الاتصال نجح\n');

    // 2. فحص المستخدمين
    console.log('2️⃣ فحص جدول المستخدمين:');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        device1: true,
        isActive: true,
        permissions: true
      }
    });
    
    console.log(`   📊 عدد المستخدمين: ${users.length}`);
    
    if (users.length > 0) {
      console.log('   👥 المستخدمين:');
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.username} (${user.loginName})`);
        console.log(`      🔒 كلمة المرور: ${user.password ? 'موجودة' : 'مفقودة'}`);
        console.log(`      📱 الجهاز: ${user.device1 || 'غير محدد'}`);
        console.log(`      ✅ نشط: ${user.isActive ? 'نعم' : 'لا'}`);
      });
    }
    console.log('');

    // 3. فحص العملاء
    console.log('3️⃣ فحص جدول العملاء:');
    const clientCount = await prisma.client.count();
    console.log(`   📊 عدد العملاء: ${clientCount}\n`);

    // 4. فحص الوكلاء
    console.log('4️⃣ فحص جدول الوكلاء:');
    const agentCount = await prisma.agent.count();
    console.log(`   📊 عدد الوكلاء: ${agentCount}\n`);

    // 5. فحص سجلات البيانات
    console.log('5️⃣ فحص جدول سجلات البيانات:');
    const dataRecordCount = await prisma.dataRecord.count();
    console.log(`   📊 عدد سجلات البيانات: ${dataRecordCount}\n`);

    // 6. فحص محاولات الدخول
    console.log('6️⃣ فحص جدول محاولات الدخول:');
    const loginAttemptCount = await prisma.loginAttempt.count();
    console.log(`   📊 عدد محاولات الدخول: ${loginAttemptCount}\n`);

    // 7. اختبار كلمات المرور
    if (users.length > 0) {
      console.log('7️⃣ اختبار كلمات المرور:');
      const testUser = users[0];
      console.log(`   🧪 اختبار مع: ${testUser.username}`);
      
      const passwords = ['admin123', 'hash8080', 'yemen123', 'admin'];
      
      for (const pwd of passwords) {
        try {
          const isValid = await bcrypt.compare(pwd, testUser.password);
          console.log(`   🔑 "${pwd}": ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
          if (isValid) break;
        } catch (error) {
          console.log(`   ❌ خطأ في "${pwd}": ${error.message}`);
        }
      }
    }

    console.log('\n✅ انتهى الفحص!');

  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDatabase().catch(console.error);
