const { PrismaClient } = require('./server/node_modules/@prisma/client')

const prisma = new PrismaClient()

async function resetAgentIds() {
  try {
    console.log('⚠️  تحذير: هذه العملية ستعيد ترقيم جميع الوكلاء!')
    console.log('⚠️  هذا قد يؤثر على العلاقات في قاعدة البيانات!')
    console.log('')
    console.log('🔍 الوكلاء الحاليون:')
    
    // جلب الوكلاء الحاليين
    const currentAgents = await prisma.agent.findMany({
      orderBy: { id: 'asc' },
      select: {
        id: true,
        agentName: true,
        agencyName: true
      }
    })

    currentAgents.forEach((agent, index) => {
      console.log(`  ${index + 1}. ${agent.agentName} (ID الحالي: ${agent.id})`)
    })

    console.log('')
    console.log('📋 الخطة المقترحة:')
    console.log('  1. الغراسي: ID = 1')
    console.log('  2. المترب: ID = 2') 
    console.log('  3. وكيل اختبار: ID = 3')
    console.log('')
    console.log('❌ لن أنفذ هذه العملية تلقائياً لأنها خطيرة!')
    console.log('💡 إذا كنت تريد تنفيذها، يجب عمل نسخة احتياطية أولاً')

  } catch (error) {
    console.error('❌ خطأ:', error)
  } finally {
    await prisma.$disconnect()
  }
}

resetAgentIds()
