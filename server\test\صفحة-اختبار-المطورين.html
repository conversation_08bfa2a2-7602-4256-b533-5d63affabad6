<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 صفحة اختبار المطورين - نظام التحقق من العملاء</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 32px;
        }
        
        .subtitle {
            margin: 15px 0 0 0;
            font-size: 18px;
            opacity: 0.9;
        }
        
        .api-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #17a2b8;
        }
        
        .api-url {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .test-data {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #6c757d;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        input, select, button, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 0;
            transition: all 0.3s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-btn {
            padding: 15px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
            text-align: center;
            font-size: 14px;
        }
        
        .test-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .test-agent-error {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .test-client-error {
            background: linear-gradient(45deg, #fd7e14, #e55a00);
            color: white;
        }
        
        .test-inactive {
            background: linear-gradient(45deg, #6c757d, #5a6268);
            color: white;
        }
        
        .result {
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .agent-error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .client-error {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        
        .loading {
            background: #e2e3e5;
            color: #495057;
            border: 2px solid #ced4da;
        }
        
        .code-example {
            background: #2c3e50;
            color: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 15px 0;
            overflow-x: auto;
        }
        
        .copy-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin: 5px 0;
            width: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 صفحة اختبار المطورين</h1>
            <p class="subtitle">نظام التحقق من العملاء - اختبار شامل للـ API</p>
        </div>

        <!-- معلومات الـ API -->
        <div class="api-info">
            <h3>📡 معلومات الـ API</h3>
            <div class="api-url">
                http://***********:8081/api/external/verify-direct
            </div>
            <p><strong>طريقة الطلب:</strong> POST</p>
            <p><strong>نوع المحتوى:</strong> application/json</p>
            <p><strong>المهلة الزمنية:</strong> 30 ثانية</p>
        </div>

        <!-- البيانات التجريبية -->
        <div class="test-data">
            <h3>🔐 البيانات التجريبية</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4>🏢 الوكيل التجريبي:</h4>
                    <p><strong>اسم المستخدم:</strong> testuser</p>
                    <p><strong>كلمة المرور:</strong> test123</p>
                    <p><strong>الاسم:</strong> فحص تجريبي</p>
                </div>
                <div>
                    <h4>👥 العملاء التجريبيون:</h4>
                    <p><strong>1004 + TEST1004:</strong> نشط</p>
                    <p><strong>1005 + TEST1005:</strong> غير نشط</p>
                    <p><strong>9999 + DUMMY999:</strong> نشط</p>
                    <p><strong>1000 + ABC12345:</strong> نشط</p>
                </div>
            </div>
        </div>

        <!-- بيانات الاختبار -->
        <div class="section">
            <h3>📝 بيانات الاختبار</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                <div class="form-group">
                    <label>اسم المستخدم للوكيل:</label>
                    <input type="text" id="agentUser" value="testuser">
                </div>
                <div class="form-group">
                    <label>كلمة مرور الوكيل:</label>
                    <input type="password" id="agentPass" value="test123">
                </div>
                <div class="form-group">
                    <label>رمز العميل:</label>
                    <input type="text" id="clientCode" value="1005">
                </div>
                <div class="form-group">
                    <label>توكن العميل:</label>
                    <input type="text" id="clientToken" value="2NPYjXzuQeu7">
                </div>
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="section">
            <h3>⚡ اختبارات سريعة</h3>
            <div class="test-buttons">
                <button class="test-btn test-success" onclick="testSuccess()">
                    ✅ عميل نشط (1004)
                </button>
                <button class="test-btn test-inactive" onclick="testInactive()">
                    ⚠️ عميل غير نشط (1005)
                </button>
                <button class="test-btn test-success" onclick="testAnother()">
                    ✅ عميل آخر (1000)
                </button>
                <button class="test-btn test-agent-error" onclick="testAgentError()">
                    🔐 خطأ كلمة المرور
                </button>
                <button class="test-btn test-client-error" onclick="testClientError()">
                    👤 عميل غير موجود
                </button>
                <button class="test-btn test-client-error" onclick="testTokenError()">
                    🔑 توكن خاطئ
                </button>
            </div>
        </div>

        <!-- زر التحقق العام -->
        <div class="section">
            <button onclick="verifyClient()" id="verifyBtn">
                🔍 تنفيذ التحقق بالبيانات الحالية
            </button>
        </div>

        <!-- النتيجة -->
        <div class="section">
            <h3>📊 نتيجة الاختبار</h3>
            <div id="result" class="result info">
اختر نوع الاختبار أو أدخل البيانات وانقر على "تنفيذ التحقق"...

🎯 الاختبارات المتاحة:
• عميل نشط: نتيجة success مع client_status = 1
• عميل غير نشط: نتيجة success مع client_status = 0  
• خطأ الوكيل: نتيجة agent_error
• خطأ العميل: نتيجة client_error
            </div>
        </div>

        <!-- أمثلة الكود -->
        <div class="section">
            <h3>💻 أمثلة الكود للنسخ</h3>
            
            <h4>cURL:</h4>
            <div class="code-example" id="curlCode">
curl -X POST http://***********:8081/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "testuser",
    "agent_login_password": "test123",
    "client_code": "1004",
    "client_token": "TEST1004"
  }'
            </div>
            <button class="copy-btn" onclick="copyCode('curlCode')">📋 نسخ cURL</button>

            <h4>JavaScript:</h4>
            <div class="code-example" id="jsCode">
const response = await fetch('http://***********:8081/api/external/verify-direct', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        agent_login_name: 'testuser',
        agent_login_password: 'test123',
        client_code: '1004',
        client_token: 'TEST1004'
    })
});

const result = await response.json();
console.log(result);
            </div>
            <button class="copy-btn" onclick="copyCode('jsCode')">📋 نسخ JavaScript</button>

            <h4>PHP:</h4>
            <div class="code-example" id="phpCode">
$data = array(
    'agent_login_name' => 'testuser',
    'agent_login_password' => 'test123',
    'client_code' => '1004',
    'client_token' => 'TEST1004'
);

$options = array(
    'http' => array(
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    )
);

$context = stream_context_create($options);
$result = file_get_contents('http://***********:8081/api/external/verify-direct', false, $context);
$response = json_decode($result, true);
            </div>
            <button class="copy-btn" onclick="copyCode('phpCode')">📋 نسخ PHP</button>
        </div>

        <!-- معلومات الردود -->
        <div class="section">
            <h3>📋 شرح الردود</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
                <div>
                    <h4>✅ النجاح:</h4>
                    <p><code>{"status":"success","client_status":1}</code> = العميل نشط</p>
                    <p><code>{"status":"success","client_status":0}</code> = العميل غير نشط</p>
                </div>
                <div>
                    <h4>❌ الأخطاء:</h4>
                    <p><code>{"status":"agent_error"}</code> = خطأ في بيانات الوكيل</p>
                    <p><code>{"status":"client_error"}</code> = خطأ في بيانات العميل</p>
                    <p><code>{"status":"error"}</code> = خطأ في الخادم</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث أمثلة الكود
        function updateCodeExamples() {
            const agentUser = document.getElementById('agentUser').value;
            const agentPass = document.getElementById('agentPass').value;
            const clientCode = document.getElementById('clientCode').value;
            const clientToken = document.getElementById('clientToken').value;

            // تحديث cURL
            document.getElementById('curlCode').textContent = `curl -X POST http://***********:8081/api/external/verify-direct \\
  -H "Content-Type: application/json" \\
  -d '{
    "agent_login_name": "${agentUser}",
    "agent_login_password": "${agentPass}",
    "client_code": "${clientCode}",
    "client_token": "${clientToken}"
  }'`;

            // تحديث JavaScript
            document.getElementById('jsCode').textContent = `const response = await fetch('http://***********:8081/api/external/verify-direct', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        agent_login_name: '${agentUser}',
        agent_login_password: '${agentPass}',
        client_code: '${clientCode}',
        client_token: '${clientToken}'
    })
});

const result = await response.json();
console.log(result);`;

            // تحديث PHP
            document.getElementById('phpCode').textContent = `$data = array(
    'agent_login_name' => '${agentUser}',
    'agent_login_password' => '${agentPass}',
    'client_code' => '${clientCode}',
    'client_token' => '${clientToken}'
);

$options = array(
    'http' => array(
        'header' => "Content-type: application/json\\r\\n",
        'method' => 'POST',
        'content' => json_encode($data)
    )
);

$context = stream_context_create($options);
$result = file_get_contents('http://***********:8081/api/external/verify-direct', false, $context);
$response = json_decode($result, true);`;
        }

        // نسخ الكود
        function copyCode(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                alert('تم نسخ الكود!');
            }).catch(() => {
                // طريقة بديلة للنسخ
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('تم نسخ الكود!');
            });
        }

        // عرض النتيجة
        function showResult(message, type = 'info') {
            const element = document.getElementById('result');
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // التحقق من العميل
        async function verifyClient() {
            showResult('⏳ جاري التحقق...', 'loading');
            
            try {
                const agentUser = document.getElementById('agentUser').value;
                const agentPass = document.getElementById('agentPass').value;
                const clientCode = document.getElementById('clientCode').value;
                const clientToken = document.getElementById('clientToken').value;

                const startTime = Date.now();

                const response = await fetch('http://***********:8081/api/external/verify-direct', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_login_name: agentUser,
                        agent_login_password: agentPass,
                        client_code: clientCode,
                        client_token: clientToken
                    })
                });

                const endTime = Date.now();
                const duration = endTime - startTime;

                const result = await response.json();
                console.log('النتيجة:', result);

                let message = `📊 نتيجة الاختبار:\n`;
                message += `⏱️ وقت الاستجابة: ${duration}ms\n`;
                message += `🌐 حالة HTTP: ${response.status}\n\n`;

                switch (result.status) {
                    case 'success':
                        if (result.client_status === 1) {
                            message += `✅ نجح التحقق - العميل نشط\n\n`;
                            message += `📋 التفاصيل:\n`;
                            message += `• الوكيل: ${agentUser} ✅\n`;
                            message += `• العميل: ${clientCode} ✅\n`;
                            message += `• التوكن: ${clientToken} ✅\n`;
                            message += `• حالة العميل: نشط (1) ✅\n\n`;
                            message += `📤 الرد: {"status":"success","client_status":1}`;
                            showResult(message, 'success');
                        } else {
                            message += `⚠️ نجح التحقق - العميل غير نشط\n\n`;
                            message += `📋 التفاصيل:\n`;
                            message += `• الوكيل: ${agentUser} ✅\n`;
                            message += `• العميل: ${clientCode} ✅\n`;
                            message += `• التوكن: ${clientToken} ✅\n`;
                            message += `• حالة العميل: غير نشط (0) ⚠️\n\n`;
                            message += `📤 الرد: {"status":"success","client_status":0}`;
                            showResult(message, 'success');
                        }
                        break;
                    case 'agent_error':
                        message += `🔐 خطأ في بيانات الوكيل\n\n`;
                        message += `📋 التفاصيل:\n`;
                        message += `• اسم المستخدم: ${agentUser} ❌\n`;
                        message += `• كلمة المرور: خاطئة ❌\n`;
                        message += `• السبب: بيانات الوكيل غير صحيحة\n\n`;
                        message += `📤 الرد: {"status":"agent_error"}`;
                        showResult(message, 'agent-error');
                        break;
                    case 'client_error':
                        message += `👤 خطأ في بيانات العميل\n\n`;
                        message += `📋 التفاصيل:\n`;
                        message += `• الوكيل: ${agentUser} ✅\n`;
                        message += `• العميل: ${clientCode} ❌\n`;
                        message += `• التوكن: ${clientToken} ❌\n`;
                        message += `• السبب: العميل غير موجود أو التوكن خاطئ\n\n`;
                        message += `📤 الرد: {"status":"client_error"}`;
                        showResult(message, 'client-error');
                        break;
                    case 'error':
                        message += `❌ خطأ في الخادم\n\n`;
                        message += `📋 التفاصيل:\n`;
                        message += `• السبب: خطأ عام في النظام\n`;
                        message += `• الحل: حاول مرة أخرى أو اتصل بالدعم\n\n`;
                        message += `📤 الرد: {"status":"error"}`;
                        showResult(message, 'error');
                        break;
                    default:
                        message += `❓ رد غير متوقع\n\n`;
                        message += `📤 الرد: ${JSON.stringify(result)}`;
                        showResult(message, 'error');
                }

            } catch (error) {
                console.error('خطأ:', error);
                const message = `❌ خطأ في الاتصال\n\n📋 التفاصيل:\n• ${error.message}\n\n🔧 تحقق من:\n• الاتصال بالإنترنت\n• عنوان الخادم\n• إعدادات جدار الحماية`;
                showResult(message, 'error');
            }
        }

        // اختبارات سريعة
        function testSuccess() {
            document.getElementById('agentUser').value = 'testuser';
            document.getElementById('agentPass').value = 'test123';
            document.getElementById('clientCode').value = '1004';
            document.getElementById('clientToken').value = 'TEST1004';
            updateCodeExamples();
            verifyClient();
        }

        function testInactive() {
            document.getElementById('agentUser').value = 'testuser';
            document.getElementById('agentPass').value = 'test123';
            document.getElementById('clientCode').value = '1005';
            document.getElementById('clientToken').value = '2NPYjXzuQeu7';
            updateCodeExamples();
            verifyClient();
        }

        function testAnother() {
            document.getElementById('agentUser').value = 'testuser';
            document.getElementById('agentPass').value = 'test123';
            document.getElementById('clientCode').value = '1000';
            document.getElementById('clientToken').value = 'ABC12345';
            updateCodeExamples();
            verifyClient();
        }

        function testAgentError() {
            document.getElementById('agentUser').value = 'testuser';
            document.getElementById('agentPass').value = 'WRONG_PASSWORD';
            document.getElementById('clientCode').value = '1004';
            document.getElementById('clientToken').value = 'TEST1004';
            updateCodeExamples();
            verifyClient();
        }

        function testClientError() {
            document.getElementById('agentUser').value = 'testuser';
            document.getElementById('agentPass').value = 'test123';
            document.getElementById('clientCode').value = '8888';
            document.getElementById('clientToken').value = 'INVALID';
            updateCodeExamples();
            verifyClient();
        }

        function testTokenError() {
            document.getElementById('agentUser').value = 'testuser';
            document.getElementById('agentPass').value = 'test123';
            document.getElementById('clientCode').value = '1004';
            document.getElementById('clientToken').value = 'WRONG_TOKEN';
            updateCodeExamples();
            verifyClient();
        }

        // تحديث الكود عند تحميل الصفحة
        window.onload = function() {
            updateCodeExamples();
            
            // تحديث الكود عند تغيير القيم
            const inputs = ['agentUser', 'agentPass', 'clientCode', 'clientToken'];
            inputs.forEach(id => {
                document.getElementById(id).addEventListener('input', updateCodeExamples);
            });
        };
    </script>
</body>
</html>
