const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // إنشاء مستخدم الأدمن
  const adminPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'admin123456', 12);

  const adminUser = await prisma.user.upsert({
    where: { loginName: process.env.ADMIN_LOGIN_NAME || 'admin' },
    update: {},
    create: {
      id: 100, // بداية من 100 كما هو مطلوب
      username: process.env.ADMIN_USERNAME || 'admin',
      loginName: process.env.ADMIN_LOGIN_NAME || 'admin',
      password: adminPassword,
      permissions: {
        isAdmin: true,
        clients: {
          create: true,
          read: true,
          update: true,
          delete: true
        },
        agents: {
          create: true,
          read: true,
          update: true,
          delete: true
        },
        users: {
          create: true,
          read: true,
          update: true,
          delete: true
        },
        dashboard: {
          read: true
        },
        security: {
          read: true,
          manage: true
        }
      }
    }
  });

  console.log('✅ Admin user created:', {
    id: adminUser.id,
    username: adminUser.username,
    loginName: adminUser.loginName
  });

  // إنشاء بعض البيانات التجريبية

  // إنشاء وكلاء تجريبيين
  const agent1 = await prisma.agent.upsert({
    where: { id: 1 },
    update: {},
    create: {
      agentName: 'أحمد محمد',
      agencyName: 'وكالة النجاح',
      agencyType: 'وكالة تجارية',
      ipAddress: '*************'
    }
  });

  const agent2 = await prisma.agent.upsert({
    where: { id: 2 },
    update: {},
    create: {
      agentName: 'فاطمة علي',
      agencyName: 'وكالة الأمل',
      agencyType: 'وكالة خدمات',
      ipAddress: '*************'
    }
  });

  console.log('✅ Sample agents created');

  // إنشاء مستخدم تجريبي
  const userPassword = await bcrypt.hash('user123456', 12);
  const testUser = await prisma.user.upsert({
    where: { loginName: 'testuser' },
    update: {},
    create: {
      id: 101,
      username: 'مستخدم تجريبي',
      loginName: 'testuser',
      password: userPassword,
      permissions: {
        isAdmin: false,
        clients: {
          create: true,
          read: true,
          update: true,
          delete: false
        },
        agents: {
          create: false,
          read: true,
          update: false,
          delete: false
        },
        users: {
          create: false,
          read: false,
          update: false,
          delete: false
        },
        dashboard: {
          read: true
        }
      }
    }
  });

  console.log('✅ Test user created:', {
    id: testUser.id,
    username: testUser.username,
    loginName: testUser.loginName
  });

  // إنشاء عملاء تجريبيين
  const client1 = await prisma.client.upsert({
    where: { clientCode: 1000 },
    update: {},
    create: {
      clientName: 'محمد أحمد',
      appName: 'تطبيق الدفع',
      cardNumber: '12345678',
      clientCode: 1000,
      password: 'client123',
      ipAddress: '*************',
      status: 1,
      dataId: null, // تم تغيير من agentId إلى dataId
      userId: testUser.id
    }
  });

  const client2 = await prisma.client.upsert({
    where: { clientCode: 1001 },
    update: {},
    create: {
      clientName: 'سارة محمود',
      appName: 'تطبيق التحويلات',
      cardNumber: '87654321',
      clientCode: 1001,
      password: 'client456',
      ipAddress: '*************',
      status: 1,
      dataId: null, // تم تغيير من agentId إلى dataId
      userId: adminUser.id
    }
  });

  console.log('✅ Sample clients created');

  // إنشاء سجلات بيانات تجريبية
  const dataRecord1 = await prisma.dataRecord.create({
    data: {
      agentId: agent1.id,
      clientId: client1.id,
      agentReference: 1001,
      operationStatus: 1, // ناجحة
      operationDate: new Date()
    }
  });

  const dataRecord2 = await prisma.dataRecord.create({
    data: {
      agentId: agent2.id,
      clientId: client2.id,
      agentReference: 1002,
      operationStatus: 0, // فاشلة
      operationDate: new Date()
    }
  });

  console.log('✅ Sample data records created');

  // إنشاء المستخدم hash8080
  const hashUser = await prisma.user.upsert({
    where: { loginName: 'hash8080' },
    update: {},
    create: {
      username: 'محمد الحاشدي',
      loginName: 'hash8080',
      password: await bcrypt.hash('Hash8080', 10),
      permissions: {
        isAdmin: true,
        clients: { create: true, read: true, update: true, delete: true },
        agents: { create: true, read: true, update: true, delete: true },
        users: { create: true, read: true, update: true, delete: true },
        dashboard: { read: true },
        security: { read: true, manage: true }
      },
      isActive: true
    }
  });

  console.log('✅ Hash user created:', {
    id: hashUser.id,
    username: hashUser.username,
    loginName: hashUser.loginName
  });

  console.log('🎉 Database seeding completed successfully!');
  console.log('\n📋 Login credentials:');
  console.log('Admin User:');
  console.log(`  Login Name: ${adminUser.loginName}`);
  console.log(`  Password: ${process.env.ADMIN_PASSWORD || 'admin123456'}`);
  console.log('\nHash User:');
  console.log(`  Login Name: ${hashUser.loginName}`);
  console.log(`  Password: Hash8080`);
  console.log('\nTest User:');
  console.log(`  Login Name: ${testUser.loginName}`);
  console.log(`  Password: user123456`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
