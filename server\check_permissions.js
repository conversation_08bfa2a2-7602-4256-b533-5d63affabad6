const { Client } = require('pg');

async function checkPermissions() {
    const client = new Client({
        host: 'localhost',
        port: 5432,
        database: 'yemclient_db',
        user: 'postgres',
        password: 'yemen123'
    });

    try {
        console.log('🔍 الاتصال بقاعدة البيانات...');
        await client.connect();
        console.log('✅ تم الاتصال بنجاح');

        // التحقق من صلاحيات المستخدم hash8080
        const userResult = await client.query(`
            SELECT 
                user_id,
                username,
                login_name,
                permissions,
                is_active
            FROM users 
            WHERE login_name = $1
        `, ['hash8080']);

        if (userResult.rows.length === 0) {
            console.log('❌ المستخدم غير موجود!');
            return;
        }

        const user = userResult.rows[0];
        console.log('👤 معلومات المستخدم:');
        console.log(`- ID: ${user.user_id}`);
        console.log(`- الاسم: ${user.username}`);
        console.log(`- اسم المستخدم: ${user.login_name}`);
        console.log(`- نشط: ${user.is_active}`);
        console.log('');
        console.log('🔐 الصلاحيات الحالية:');
        console.log(JSON.stringify(user.permissions, null, 2));

        // تحديث الصلاحيات لتكون صحيحة
        const correctPermissions = {
            isAdmin: true,
            clients: {
                create: true,
                read: true,
                update: true,
                delete: true
            },
            agents: {
                create: true,
                read: true,
                update: true,
                delete: true
            },
            users: {
                create: true,
                read: true,
                update: true,
                delete: true
            },
            dashboard: {
                read: true
            },
            security: {
                read: true,
                manage: true
            }
        };

        console.log('');
        console.log('🔄 تحديث الصلاحيات...');
        
        await client.query(`
            UPDATE users 
            SET permissions = $1, updated_at = CURRENT_TIMESTAMP
            WHERE login_name = $2
        `, [JSON.stringify(correctPermissions), 'hash8080']);

        console.log('✅ تم تحديث الصلاحيات بنجاح!');

        // التحقق من التحديث
        const updatedResult = await client.query(`
            SELECT permissions FROM users WHERE login_name = $1
        `, ['hash8080']);

        console.log('');
        console.log('🔐 الصلاحيات بعد التحديث:');
        console.log(JSON.stringify(updatedResult.rows[0].permissions, null, 2));

        // عرض جميع المستخدمين وصلاحياتهم
        const allUsers = await client.query(`
            SELECT 
                user_id,
                username,
                login_name,
                is_active,
                permissions
            FROM users 
            ORDER BY user_id
        `);

        console.log('');
        console.log('👥 جميع المستخدمين وصلاحياتهم:');
        allUsers.rows.forEach(user => {
            const isAdmin = user.permissions?.isAdmin ? 'أدمن' : 'مستخدم عادي';
            console.log(`\n- ${user.username} (${user.login_name})`);
            console.log(`  النوع: ${isAdmin}`);
            console.log(`  نشط: ${user.is_active ? 'نعم' : 'لا'}`);
            
            if (user.permissions?.clients) {
                console.log(`  صلاحيات العملاء: إنشاء=${user.permissions.clients.create}, قراءة=${user.permissions.clients.read}, تحديث=${user.permissions.clients.update}, حذف=${user.permissions.clients.delete}`);
            }
            if (user.permissions?.agents) {
                console.log(`  صلاحيات الوكلاء: إنشاء=${user.permissions.agents.create}, قراءة=${user.permissions.agents.read}, تحديث=${user.permissions.agents.update}, حذف=${user.permissions.agents.delete}`);
            }
            if (user.permissions?.users) {
                console.log(`  صلاحيات المستخدمين: إنشاء=${user.permissions.users.create}, قراءة=${user.permissions.users.read}, تحديث=${user.permissions.users.update}, حذف=${user.permissions.users.delete}`);
            }
        });

    } catch (error) {
        console.error('❌ خطأ:', error.message);
    } finally {
        await client.end();
        console.log('');
        console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
    }
}

// تشغيل الدالة
checkPermissions();
