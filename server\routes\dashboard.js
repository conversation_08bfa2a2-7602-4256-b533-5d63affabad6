const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// إحصائيات لوحة التحكم
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const [
      totalClients,
      activeClients,
      blockedClients,
      totalAgents,
      activeAgents,
      totalUsers,
      activeUsers,
      recentLoginAttempts
    ] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.client.count({ where: { status: 2 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } }),
      prisma.user.count(),
      prisma.user.count({ where: { isActive: true } }),
      prisma.loginAttempt.count({
        where: {
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // آخر 24 ساعة
          }
        }
      })
    ]);

    res.json({
      clients: {
        total: totalClients,
        active: activeClients,
        blocked: blockedClients
      },
      agents: {
        total: totalAgents,
        active: activeAgents,
        inactive: totalAgents - activeAgents
      },
      users: {
        total: totalUsers,
        active: activeUsers,
        inactive: totalUsers - activeUsers
      },
      security: {
        recentLoginAttempts
      }
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// محاولات الدخول الأخيرة
router.get('/login-attempts', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, success, userType } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (success !== undefined) {
      where.success = success === 'true';
    }

    if (userType) {
      where.userType = userType;
    }

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: {
            select: { id: true, username: true, loginName: true }
          },
          agent: {
            select: { id: true, agentName: true, agencyName: true }
          }
        },
        orderBy: { timestamp: 'desc' }
      }),
      prisma.loginAttempt.count({ where })
    ]);

    res.json({
      attempts,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Login attempts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الأنشطة الأخيرة
router.get('/recent-activity', authenticateToken, async (req, res) => {
  try {
    const [recentClients, recentAgents, recentUsers] = await Promise.all([
      prisma.client.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          clientName: true,
          appName: true,
          status: true,
          createdAt: true
        }
      }),
      prisma.agent.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          agentName: true,
          agencyName: true,
          isActive: true,
          createdAt: true
        }
      }),
      prisma.user.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          username: true,
          isActive: true,
          createdAt: true
        }
      })
    ]);

    res.json({
      recentClients,
      recentAgents,
      recentUsers
    });

  } catch (error) {
    console.error('Recent activity error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// إحصائيات الأمان
router.get('/security-stats', authenticateToken, async (req, res) => {
  try {
    const now = new Date();
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const [
      failedAttemptsLast24h,
      successfulLoginsLast24h,
      failedAttemptsLastWeek,
      uniqueDevicesLast24h,
      suspiciousIPs
    ] = await Promise.all([
      prisma.loginAttempt.count({
        where: {
          success: false,
          timestamp: { gte: last24Hours }
        }
      }),
      prisma.loginAttempt.count({
        where: {
          success: true,
          timestamp: { gte: last24Hours }
        }
      }),
      prisma.loginAttempt.count({
        where: {
          success: false,
          timestamp: { gte: lastWeek }
        }
      }),
      prisma.loginAttempt.groupBy({
        by: ['deviceId'],
        where: {
          timestamp: { gte: last24Hours }
        },
        _count: true
      }),
      prisma.loginAttempt.groupBy({
        by: ['ipAddress'],
        where: {
          success: false,
          timestamp: { gte: last24Hours }
        },
        _count: {
          ipAddress: true
        },
        having: {
          ipAddress: {
            _count: {
              gt: 5 // أكثر من 5 محاولات فاشلة
            }
          }
        }
      })
    ]);

    res.json({
      last24Hours: {
        failedAttempts: failedAttemptsLast24h,
        successfulLogins: successfulLoginsLast24h,
        uniqueDevices: uniqueDevicesLast24h.length
      },
      lastWeek: {
        failedAttempts: failedAttemptsLastWeek
      },
      security: {
        suspiciousIPs: suspiciousIPs.map(ip => ({
          ipAddress: ip.ipAddress,
          attempts: ip._count.ipAddress
        }))
      }
    });

  } catch (error) {
    console.error('Security stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// مسارات الأمان (مؤقت)
router.get('/security/stats', authenticateToken, async (req, res) => {
  try {
    // حساب إحصائيات محاولات تسجيل الدخول
    const [
      totalAttempts,
      successfulLogins,
      failedLogins,
      uniqueIPs,
      activeDevices
    ] = await Promise.all([
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } }),
      prisma.loginAttempt.count({ where: { success: false } }),
      prisma.loginAttempt.groupBy({
        by: ['ipAddress'],
        _count: { ipAddress: true }
      }),
      prisma.user.count({ where: { device1: { not: null } } })
    ]);

    // حساب المحاولات المشبوهة (أكثر من 5 محاولات فاشلة من نفس IP)
    const suspiciousIPs = await prisma.loginAttempt.groupBy({
      by: ['ipAddress'],
      where: { success: false },
      _count: { ipAddress: true },
      having: { ipAddress: { _count: { gt: 5 } } }
    });

    // آخر فحص أمني (محاكاة)
    const lastSecurityScan = new Date().toISOString();

    res.json({
      successfulLogins,
      failedLogins,
      totalAttempts,
      blockedIPs: suspiciousIPs.length,
      activeDevices,
      suspiciousActivity: suspiciousIPs.length,
      uniqueIPs: uniqueIPs.length,
      lastSecurityScan,
      last24Hours: {
        successfulLogins: successfulLogins,
        failedAttempts: failedLogins
      }
    });

  } catch (error) {
    console.error('Security stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

router.get('/security/login-attempts', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, success, userType } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (success !== undefined) {
      where.success = success === 'true';
    }

    if (userType) {
      where.userType = userType;
    }

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: { id: true, username: true, loginName: true }
          },
          agent: {
            select: { id: true, agentName: true, agencyName: true }
          }
        }
      }),
      prisma.loginAttempt.count({ where })
    ]);

    // تحويل البيانات للتنسيق المطلوب
    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id,
      type: attempt.success ? 'success' : 'failure',
      username: attempt.user?.loginName || attempt.agent?.agentName || 'Unknown',
      ip: attempt.ipAddress,
      userAgent: 'Browser',
      timestamp: attempt.timestamp,
      deviceId: attempt.deviceId,
      reason: attempt.success ? null : 'Authentication failed'
    }));

    res.json({
      attempts: formattedAttempts,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    console.error('Login attempts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
