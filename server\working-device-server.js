const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

// تحميل متغيرات البيئة
require('dotenv').config();

// إعداد Prisma
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:yemen123@localhost:5432/yemclient_db"
    }
  }
});

const app = express();
const PORT = 8080;

// إنشاء مجلد logs إذا لم يكن موجوداً
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// دالة كتابة السجلات
const writeLog = (message) => {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  try {
    fs.appendFileSync(path.join(logsDir, 'server.log'), logMessage);
  } catch (error) {
    console.error('خطأ في كتابة السجل:', error);
  }

  console.log(`[${timestamp}] ${message}`);
};

// CORS configuration
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Device-ID']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
  writeLog(`${req.method} ${req.path} from ${clientIP}`);
  next();
});

// Health check
app.get('/health', async (req, res) => {
  try {
    await prisma.$connect();
    const userCount = await prisma.user.count();

    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: 'connected',
      userCount: userCount
    });
  } catch (error) {
    writeLog(`Health check failed: ${error.message}`);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// Auth API مع دعم device validation
app.post('/api/auth/login', async (req, res) => {
  try {
    const { loginName, password, deviceId } = req.body;
    const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';

    writeLog(`Login attempt: ${loginName} from device ${deviceId?.substring(0, 10)}... IP: ${clientIP}`);

    // البحث عن المستخدم
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: loginName },
          { loginName: loginName }
        ],
        isActive: true
      }
    });

    if (!user) {
      writeLog(`❌ User not found: ${loginName}`);

      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          ipAddress: clientIP,
          deviceId: deviceId || 'unknown',
          success: false,
          userType: 'user',
          failureReason: 'User not found'
        }
      });

      return res.status(401).json({
        success: false,
        error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من كلمة المرور
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      writeLog(`❌ Invalid password for: ${loginName}`);

      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userId: user.id,
          ipAddress: clientIP,
          deviceId: deviceId || 'unknown',
          success: false,
          userType: 'user',
          failureReason: 'Invalid password'
        }
      });

      return res.status(401).json({
        success: false,
        error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      });
    }

    // التحقق من الجهاز - دعم device1 و deviceId
    let isDeviceAuthorized = false;
    let authorizedDevices = [];

    // فحص device1 أولاً (العمود الجديد)
    if (user.device1) {
      authorizedDevices.push(user.device1);
      if (user.device1 === deviceId) {
        isDeviceAuthorized = true;
        writeLog(`✅ Device authorized via device1: ${deviceId?.substring(0, 10)}...`);
      }
    }

    // فحص deviceId (العمود القديم) إذا لم يتم العثور على الجهاز في device1
    if (!isDeviceAuthorized && user.deviceId) {
      const oldDevices = user.deviceId.includes(',')
        ? user.deviceId.split(',').map(id => id.trim())
        : [user.deviceId];

      authorizedDevices = [...authorizedDevices, ...oldDevices];

      if (oldDevices.includes(deviceId)) {
        isDeviceAuthorized = true;
        writeLog(`✅ Device authorized via deviceId: ${deviceId?.substring(0, 10)}...`);
      }
    }

    // إذا لم يكن هناك أجهزة مسجلة، السماح بالدخول وتسجيل الجهاز
    if (!user.device1 && !user.deviceId && deviceId) {
      writeLog(`📱 No devices registered for ${user.username}, registering current device`);

      // تحديث device1 بالجهاز الحالي
      await prisma.user.update({
        where: { id: user.id },
        data: { device1: deviceId }
      });

      isDeviceAuthorized = true;
      authorizedDevices = [deviceId];
    }

    if (!isDeviceAuthorized) {
      writeLog(`❌ Device not authorized: ${deviceId?.substring(0, 10)}... for user ${user.username}`);
      writeLog(`   Authorized devices: ${authorizedDevices.map(d => d?.substring(0, 10) + '...').join(', ')}`);

      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userId: user.id,
          ipAddress: clientIP,
          deviceId: deviceId || 'unknown',
          success: false,
          userType: 'user',
          failureReason: 'Device not authorized'
        }
      });

      return res.status(403).json({
        success: false,
        error: 'الجهاز غير مصرح له بالدخول',
        message: 'هذا الحساب مرتبط بأجهزة أخرى. يرجى تسجيل الدخول من أحد الأجهزة المصرح بها.',
        authorizedDevices: authorizedDevices.map(d => d?.substring(0, 10) + '...')
      });
    }

    // إنشاء JWT token
    const token = jwt.sign(
      {
        userId: user.id,
        loginName: user.loginName,
        deviceId: deviceId
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // تسجيل محاولة دخول ناجحة
    await prisma.loginAttempt.create({
      data: {
        userId: user.id,
        ipAddress: clientIP,
        deviceId: deviceId || 'unknown',
        success: true,
        userType: 'user'
      }
    });

    writeLog(`✅ Login successful for: ${user.username}`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions,
        isActive: user.isActive
      },
      token: token
    });

  } catch (error) {
    writeLog(`❌ Login error: ${error.message}`);
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: 'خطأ في الخادم'
    });
  }
});

// Logout API
app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'تم تسجيل الخروج بنجاح'
  });
});

// Dashboard Stats API
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const [totalUsers, totalClients, totalAgents, totalDataRecords] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count()
    ]);

    const activeUsers = await prisma.user.count({
      where: { isActive: true }
    });

    res.json({
      totalUsers,
      totalClients,
      totalAgents,
      totalDataRecords,
      activeUsers,
      systemHealth: 'excellent',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    writeLog(`❌ Dashboard stats error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب الإحصائيات' });
  }
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString(),
    server: 'Working Device Server',
    database: 'connected'
  });
});

// Clients API
app.get('/api/clients', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          clientName: true,
          clientCode: true,
          appName: true,
          ipAddress: true,
          status: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.client.count()
    ]);

    writeLog(`Clients API: Retrieved ${clients.length} clients (page ${page})`);

    res.json({
      success: true,
      data: clients,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    writeLog(`❌ Clients API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب بيانات العملاء' });
  }
});

// Agents API
app.get('/api/agents', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          agentName: true,
          agencyName: true,
          loginName: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.agent.count()
    ]);

    writeLog(`Agents API: Retrieved ${agents.length} agents (page ${page})`);

    res.json({
      success: true,
      data: agents,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    writeLog(`❌ Agents API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب بيانات الوكلاء' });
  }
});

// Users API
app.get('/api/users', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          username: true,
          loginName: true,
          isActive: true,
          permissions: true,
          device1: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.user.count()
    ]);

    writeLog(`Users API: Retrieved ${users.length} users (page ${page})`);

    res.json({
      success: true,
      data: users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    writeLog(`❌ Users API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب بيانات المستخدمين' });
  }
});

// Data Records API
app.get('/api/data-records', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        skip,
        take: limit,
        orderBy: { operationDate: 'desc' },
        include: {
          agent: {
            select: {
              agentName: true,
              agencyName: true
            }
          },
          client: {
            select: {
              clientName: true,
              clientCode: true
            }
          }
        }
      }),
      prisma.dataRecord.count()
    ]);

    writeLog(`Data Records API: Retrieved ${dataRecords.length} records (page ${page})`);

    res.json({
      success: true,
      dataRecords: dataRecords,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    writeLog(`❌ Data Records API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب سجلات البيانات' });
  }
});

// Security APIs
app.get('/api/security/stats', async (req, res) => {
  try {
    const [totalAttempts, successfulAttempts, failedAttempts] = await Promise.all([
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } }),
      prisma.loginAttempt.count({ where: { success: false } })
    ]);

    const successRate = totalAttempts > 0 ? ((successfulAttempts / totalAttempts) * 100).toFixed(1) : 0;

    // محاولات اليوم
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayAttempts = await prisma.loginAttempt.count({
      where: {
        timestamp: {
          gte: today
        }
      }
    });

    writeLog(`Security Stats API: ${totalAttempts} total attempts, ${successRate}% success rate`);

    res.json({
      totalAttempts,
      successfulAttempts,
      failedAttempts,
      successRate: parseFloat(successRate),
      todayAttempts
    });
  } catch (error) {
    writeLog(`❌ Security Stats API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب إحصائيات الأمان' });
  }
});

app.get('/api/security/login-attempts', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        skip,
        take: limit,
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: {
              username: true,
              loginName: true
            }
          },
          agent: {
            select: {
              agentName: true,
              loginName: true
            }
          }
        }
      }),
      prisma.loginAttempt.count()
    ]);

    // تنسيق البيانات
    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id,
      username: attempt.user?.username || attempt.agent?.agentName || 'غير محدد',
      type: attempt.userType || 'user',
      ip: attempt.ipAddress,
      deviceId: attempt.deviceId,
      success: attempt.success,
      timestamp: attempt.timestamp,
      failureReason: attempt.failureReason
    }));

    writeLog(`Login Attempts API: Retrieved ${attempts.length} attempts (page ${page})`);

    res.json({
      success: true,
      attempts: formattedAttempts,
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    });
  } catch (error) {
    writeLog(`❌ Login Attempts API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب محاولات الدخول' });
  }
});

// Static files AFTER API routes
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found', path: req.path });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Error handler
app.use((err, req, res, next) => {
  writeLog(`Server Error: ${err.message}`);
  console.error('Server Error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
const server = app.listen(PORT, '0.0.0.0', async () => {
  writeLog(`Working Device Server starting on port ${PORT}...`);

  try {
    await prisma.$connect();
    writeLog('✅ Database connected successfully');

    const userCount = await prisma.user.count();
    writeLog(`📊 Users in database: ${userCount}`);

    console.log(`✅ Working Device Server running on http://0.0.0.0:${PORT}`);
    console.log(`🌐 External: http://***********:${PORT}`);
    console.log(`🏠 Local: http://localhost:${PORT}`);
    console.log(`🗄️ Database: Connected (${userCount} users)`);
    console.log(`📱 Device validation: ENABLED`);

  } catch (error) {
    writeLog(`❌ Database connection failed: ${error.message}`);
    console.error('❌ Database connection failed:', error.message);
  }
});

server.on('error', (err) => {
  writeLog(`Server startup error: ${err.message}`);
  console.error('Server startup error:', err);
});

// Graceful shutdown
process.on('SIGINT', async () => {
  writeLog('Server shutting down...');
  await prisma.$disconnect();
  server.close(() => {
    writeLog('Server stopped');
    process.exit(0);
  });
});

writeLog('🚀 Working Device Server starting...');
