{"name": "yem-client-management", "version": "1.0.0", "description": "نظام إدارة العملاء والوكلاء", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "db:generate": "cd server && npx prisma generate", "db:migrate": "cd server && npx prisma migrate dev", "db:studio": "cd server && npx prisma studio"}, "keywords": ["client-management", "agents", "dashboard"], "author": "YemClient Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"axios": "^1.10.0", "node-fetch": "^3.3.2"}}