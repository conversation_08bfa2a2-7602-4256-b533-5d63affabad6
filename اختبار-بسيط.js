// اختبار بسيط لتسجيل الدخول
const https = require('https')
const http = require('http')

const testLogin = () => {
  const data = JSON.stringify({
    loginName: 'hash8080',
    password: 'hash8080',
    deviceId: 'koadbqwog_1751136029819'
  })

  const options = {
    hostname: 'localhost',
    port: 8080,
    path: '/api/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': data.length
    }
  }

  console.log('🔐 اختبار تسجيل الدخول...')
  
  const req = http.request(options, (res) => {
    let responseData = ''

    res.on('data', (chunk) => {
      responseData += chunk
    })

    res.on('end', () => {
      try {
        const result = JSON.parse(responseData)
        
        console.log('📊 نتيجة الاختبار:')
        console.log(`   الحالة: ${res.statusCode === 200 ? 'نجح ✅' : 'فشل ❌'}`)
        console.log(`   الكود: ${res.statusCode}`)
        
        if (res.statusCode === 200 && result.user) {
          console.log('')
          console.log('✅ بيانات المستخدم:')
          console.log(`   الرقم: ${result.user.id}`)
          console.log(`   الاسم: ${result.user.username}`)
          console.log(`   اسم الدخول: ${result.user.loginName}`)
          console.log(`   isActive موجود: ${result.user.isActive !== undefined ? 'نعم ✅' : 'لا ❌'}`)
          console.log(`   قيمة isActive: ${result.user.isActive}`)
          console.log(`   الحالة: ${result.user.isActive ? 'نشط ✅' : 'غير نشط ❌'}`)
          console.log(`   مدير النظام: ${result.user.permissions?.isAdmin ? 'نعم ✅' : 'لا ❌'}`)
          
          console.log('')
          console.log('🔍 تحليل:')
          if (result.user.isActive === undefined) {
            console.log('❌ المشكلة: isActive غير موجود - الخادم لم يتم تحديثه')
          } else if (result.user.isActive === true) {
            console.log('✅ تم حل المشكلة: isActive موجود ويظهر نشط')
            console.log('💡 الآن يجب مسح localStorage وتسجيل دخول جديد')
          } else {
            console.log('⚠️ المستخدم غير نشط في قاعدة البيانات')
          }
          
        } else {
          console.log('')
          console.log('❌ فشل في تسجيل الدخول:')
          console.log(`   الخطأ: ${result.error || 'غير محدد'}`)
          console.log(`   الرد الكامل: ${responseData}`)
        }
        
      } catch (error) {
        console.log('❌ خطأ في تحليل الرد:', error.message)
        console.log('📄 الرد الخام:', responseData)
      }
    })
  })

  req.on('error', (error) => {
    console.log('❌ خطأ في الطلب:', error.message)
  })

  req.write(data)
  req.end()
}

testLogin()
