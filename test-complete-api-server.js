/**
 * اختبار خادم API الكامل مع جميع APIs
 */

async function testCompleteAPIServer() {
  console.log('🔧 اختبار خادم API الكامل...\n');

  let totalTests = 0;
  let passedTests = 0;

  try {
    // اختبار 1: Health Check
    console.log('1️⃣ اختبار Health Check:');
    totalTests++;
    const healthResponse = await fetch('http://localhost:8080/health');
    console.log(`   📡 Status: ${healthResponse.status}`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('   ✅ Health Check يعمل!');
      console.log(`   📊 Status: ${healthData.status}`);
      console.log(`   💾 Database: ${healthData.database}`);
      passedTests++;
    } else {
      console.log('   ❌ Health Check فشل!');
    }
    console.log('');

    // اختبار 2: Login API
    console.log('2️⃣ اختبار Login API:');
    totalTests++;
    const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        loginName: 'hash8080',
        password: 'hash8080',
        deviceId: 'test_device_12345'
      })
    });

    console.log(`   📡 Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('   ✅ Login API يعمل!');
      console.log(`   📊 Success: ${loginData.success}`);
      console.log(`   👤 User: ${loginData.user?.username}`);
      passedTests++;
    } else {
      const errorData = await loginResponse.json();
      console.log('   ❌ Login API فشل!');
      console.log(`   📝 Error: ${errorData.message}`);
    }
    console.log('');

    // اختبار 3: External APIs
    console.log('3️⃣ اختبار External APIs:');
    totalTests++;
    
    const extHealthResponse = await fetch('http://localhost:8080/api/external/health');
    const verifyResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      })
    });

    console.log(`   📡 External Health: ${extHealthResponse.status}`);
    console.log(`   📡 Verify Direct: ${verifyResponse.status}`);
    
    if (extHealthResponse.ok && verifyResponse.ok) {
      console.log('   ✅ External APIs تعمل!');
      passedTests++;
    } else {
      console.log('   ❌ External APIs فشل!');
    }
    console.log('');

    // اختبار 4: Dashboard APIs
    console.log('4️⃣ اختبار Dashboard APIs:');
    totalTests++;
    
    const statsResponse = await fetch('http://localhost:8080/api/dashboard/stats');
    const activityResponse = await fetch('http://localhost:8080/api/dashboard/recent-activity');

    console.log(`   📡 Stats: ${statsResponse.status}`);
    console.log(`   📡 Activity: ${activityResponse.status}`);
    
    if (statsResponse.ok && activityResponse.ok) {
      const statsData = await statsResponse.json();
      const activityData = await activityResponse.json();
      console.log('   ✅ Dashboard APIs تعمل!');
      console.log(`   👤 المستخدمين: ${statsData.totalUsers}`);
      console.log(`   👥 العملاء: ${statsData.totalClients}`);
      console.log(`   📊 الأنشطة: ${activityData.data?.length}`);
      passedTests++;
    } else {
      console.log('   ❌ Dashboard APIs فشل!');
    }
    console.log('');

    // اختبار 5: Data APIs
    console.log('5️⃣ اختبار Data APIs:');
    totalTests++;
    
    const clientsResponse = await fetch('http://localhost:8080/api/clients?page=1&limit=5');
    const agentsResponse = await fetch('http://localhost:8080/api/agents?page=1&limit=5');
    const usersResponse = await fetch('http://localhost:8080/api/users?page=1&limit=5');
    const dataResponse = await fetch('http://localhost:8080/api/data-records?page=1&limit=5');

    console.log(`   📡 Clients: ${clientsResponse.status}`);
    console.log(`   📡 Agents: ${agentsResponse.status}`);
    console.log(`   📡 Users: ${usersResponse.status}`);
    console.log(`   📡 Data Records: ${dataResponse.status}`);
    
    if (clientsResponse.ok && agentsResponse.ok && usersResponse.ok && dataResponse.ok) {
      const clientsData = await clientsResponse.json();
      const agentsData = await agentsResponse.json();
      const usersData = await usersResponse.json();
      const dataData = await dataResponse.json();
      
      console.log('   ✅ Data APIs تعمل!');
      console.log(`   👥 العملاء: ${clientsData.total}`);
      console.log(`   🤝 الوكلاء: ${agentsData.total}`);
      console.log(`   👤 المستخدمين: ${usersData.total}`);
      console.log(`   📊 سجلات البيانات: ${dataData.total}`);
      passedTests++;
    } else {
      console.log('   ❌ Data APIs فشل!');
    }
    console.log('');

    // اختبار 6: Security APIs
    console.log('6️⃣ اختبار Security APIs:');
    totalTests++;
    
    const secStatsResponse = await fetch('http://localhost:8080/api/security/stats');
    const loginAttemptsResponse = await fetch('http://localhost:8080/api/security/login-attempts?page=1&limit=5');

    console.log(`   📡 Security Stats: ${secStatsResponse.status}`);
    console.log(`   📡 Login Attempts: ${loginAttemptsResponse.status}`);
    
    if (secStatsResponse.ok && loginAttemptsResponse.ok) {
      const secStatsData = await secStatsResponse.json();
      const loginAttemptsData = await loginAttemptsResponse.json();
      
      console.log('   ✅ Security APIs تعمل!');
      console.log(`   🔒 إجمالي المحاولات: ${secStatsData.totalAttempts}`);
      console.log(`   📊 معدل النجاح: ${secStatsData.successRate}%`);
      console.log(`   📋 سجلات الدخول: ${loginAttemptsData.total}`);
      passedTests++;
    } else {
      console.log('   ❌ Security APIs فشل!');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار خادم API الكامل:');
    console.log(`📊 إجمالي الاختبارات: ${totalTests}`);
    console.log(`✅ الاختبارات الناجحة: ${passedTests}`);
    console.log(`❌ الاختبارات الفاشلة: ${totalTests - passedTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('');

    if (passedTests === totalTests) {
      console.log('🎉 جميع APIs تعمل بشكل مثالي!');
      console.log('✅ Health Check: متاح');
      console.log('✅ Login API: متاح');
      console.log('✅ External APIs: متاحة');
      console.log('✅ Dashboard APIs: متاحة');
      console.log('✅ Data APIs: متاحة');
      console.log('✅ Security APIs: متاحة');
      console.log('');
      console.log('🚀 النظام جاهز 100% للاستخدام!');
      console.log('🔧 المطورون يمكنهم استخدام النظام!');
      console.log('🏠 النظام الأساسي يعمل بدون أخطاء!');
      console.log('🌐 جميع الروابط متاحة (محلي، داخلي، خارجي)!');
    } else {
      console.log('⚠️ بعض APIs لا تعمل بشكل صحيح');
      console.log('🔧 يرجى مراجعة الاختبارات الفاشلة أعلاه');
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار خادم API الكامل:', error.message);
  }
}

testCompleteAPIServer().catch(console.error);
