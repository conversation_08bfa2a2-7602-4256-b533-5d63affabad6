@echo off
echo ========================================
echo     نسخة احتياطية لنظام إدارة العملاء
echo ========================================
echo.

set BACKUP_DIR=C:\yemclinet-backup-%date:~-4,4%-%date:~-10,2%-%date:~-7,2%-%time:~0,2%-%time:~3,2%-%time:~6,2%
set BACKUP_DIR=%BACKUP_DIR: =0%

echo إنشاء مجلد النسخة الاحتياطية...
mkdir "%BACKUP_DIR%"

echo نسخ ملفات الخادم...
xcopy "C:\yemclinet\server" "%BACKUP_DIR%\server" /E /I /H /Y

echo نسخ ملفات العميل...
xcopy "C:\yemclinet\client" "%BACKUP_DIR%\client" /E /I /H /Y

echo نسخ ملفات التشغيل...
copy "C:\yemclinet\*.bat" "%BACKUP_DIR%\" /Y

echo نسخ ملف README...
if exist "C:\yemclinet\README.md" copy "C:\yemclinet\README.md" "%BACKUP_DIR%\" /Y

echo.
echo ========================================
echo تم إنشاء النسخة الاحتياطية بنجاح!
echo المسار: %BACKUP_DIR%
echo ========================================
echo.

echo إنشاء ملف معلومات النسخة الاحتياطية...
echo النسخة الاحتياطية لنظام إدارة العملاء اليمني > "%BACKUP_DIR%\backup-info.txt"
echo التاريخ: %date% %time% >> "%BACKUP_DIR%\backup-info.txt"
echo الإصدار: v1.0 - مكتمل مع API الأمان >> "%BACKUP_DIR%\backup-info.txt"
echo الميزات: >> "%BACKUP_DIR%\backup-info.txt"
echo - نظام المستخدمين والعملاء والوكلاء >> "%BACKUP_DIR%\backup-info.txt"
echo - نظام الأمان مع login_attempts >> "%BACKUP_DIR%\backup-info.txt"
echo - داشبورد مع إحصائيات حقيقية >> "%BACKUP_DIR%\backup-info.txt"
echo - تصفية العملاء حسب المستخدم >> "%BACKUP_DIR%\backup-info.txt"
echo - نافذة الملف الشخصي >> "%BACKUP_DIR%\backup-info.txt"
echo - قائمة جانبية قابلة للإخفاء >> "%BACKUP_DIR%\backup-info.txt"

pause
