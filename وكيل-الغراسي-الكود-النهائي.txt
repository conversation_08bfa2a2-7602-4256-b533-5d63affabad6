# 🏢 وكيل الغراسي - الكود النهائي للاستخدام
# Yemen Client Management System - Al-Ghurasi Agent Final Code

========================================
✅ تم حل جميع المشاكل!
========================================

🔧 المشاكل التي تم حلها:
1. ✅ مشكلة ERR_FAILED - استخدم العنوان الخارجي
2. ✅ طلب واحد بدلاً من طلبين - API جديد
3. ✅ بيانات وكيل الغراسي الحقيقي - تم إعدادها

========================================
🏢 بيانات وكيل الغراسي الحقيقي:
========================================

اسم المستخدم: alghurasi
كلمة المرور: alghurasi123
رقم الوكيل: 3
الاسم: الغراسي
الحالة: نشط ✅

========================================
🚀 الكود النهائي للاستخدام من خارج السيرفر:
========================================

# cURL Command:
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "alghurasi",
    "agent_login_password": "alghurasi123",
    "client_code": "1000",
    "client_token": "ABC12345"
  }'

========================================
📊 النتيجة المتوقعة عند النجاح:
========================================

{
  "status": "success",
  "message": "Client verified successfully through direct verification",
  "data": {
    "verification_result": {
      "agent_verified": true,
      "client_verified": true,
      "token_verified": true
    },
    "agent_info": {
      "agent_id": 3,
      "agent_name": "الغراسي",
      "agency_type": "وكيل معتمد"
    },
    "client_info": {
      "client_code": 1000,
      "client_name": "محمد علي الحاشدي",
      "app_name": "تطبيق العميل",
      "status": 1,
      "status_text": "نشط",
      "ip_address": "*************",
      "created_date": "2025-06-30",
      "created_by_user": "admin"
    },
    "operation_info": {
      "timestamp": "2025-06-30T21:30:00.000Z",
      "ip_address": "YOUR_IP_ADDRESS",
      "verification_method": "direct"
    }
  }
}

========================================
❌ النتائج عند الفشل:
========================================

# العميل غير موجود:
{
  "status": "error",
  "message": "Client not found",
  "error_code": "CLIENT_NOT_FOUND",
  "agent_info": {
    "agent_id": 3,
    "agent_name": "الغراسي",
    "agency_type": "وكيل معتمد"
  }
}

# التوكن غير مطابق:
{
  "status": "error",
  "message": "Invalid client token",
  "error_code": "TOKEN_MISMATCH",
  "agent_info": {
    "agent_id": 3,
    "agent_name": "الغراسي",
    "agency_type": "وكيل معتمد"
  },
  "client_info": {
    "client_code": 1000,
    "client_found": true,
    "token_match": false
  }
}

========================================
💻 أمثلة بلغات البرمجة مختلفة:
========================================

# JavaScript (للمتصفح أو Node.js):
async function verifyClientAlGhurasi(clientCode, clientToken) {
    try {
        const response = await fetch('http://***********:8080/api/external/verify-direct', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                agent_login_name: 'alghurasi',
                agent_login_password: 'alghurasi123',
                client_code: clientCode,
                client_token: clientToken
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            console.log('✅ تم التحقق بنجاح!');
            console.log('العميل:', result.data.client_info.client_name);
            console.log('الحالة:', result.data.client_info.status_text);
            return {
                success: true,
                clientName: result.data.client_info.client_name,
                clientStatus: result.data.client_info.status,
                isActive: result.data.client_info.status === 1
            };
        } else {
            console.log('❌ فشل التحقق:', result.message);
            return {
                success: false,
                error: result.message,
                errorCode: result.error_code
            };
        }
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error);
        return {
            success: false,
            error: 'خطأ في الاتصال: ' + error.message
        };
    }
}

# استخدام الدالة:
const result = await verifyClientAlGhurasi('1000', 'ABC12345');
if (result.success) {
    console.log('العميل نشط:', result.isActive);
} else {
    console.log('خطأ:', result.error);
}

# Python:
import requests
import json

def verify_client_alghurasi(client_code, client_token):
    url = 'http://***********:8080/api/external/verify-direct'
    data = {
        'agent_login_name': 'alghurasi',
        'agent_login_password': 'alghurasi123',
        'client_code': client_code,
        'client_token': client_token
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        result = response.json()
        
        if result['status'] == 'success':
            print('✅ تم التحقق بنجاح!')
            client_info = result['data']['client_info']
            print(f"العميل: {client_info['client_name']}")
            print(f"الحالة: {client_info['status_text']}")
            return {
                'success': True,
                'client_name': client_info['client_name'],
                'client_status': client_info['status'],
                'is_active': client_info['status'] == 1
            }
        else:
            print(f"❌ فشل التحقق: {result['message']}")
            return {
                'success': False,
                'error': result['message'],
                'error_code': result.get('error_code')
            }
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return {
            'success': False,
            'error': f'خطأ في الاتصال: {e}'
        }

# استخدام الدالة:
result = verify_client_alghurasi('1000', 'ABC12345')
if result['success']:
    print(f"العميل نشط: {result['is_active']}")
else:
    print(f"خطأ: {result['error']}")

# PHP:
<?php
function verifyClientAlGhurasi($clientCode, $clientToken) {
    $url = 'http://***********:8080/api/external/verify-direct';
    $data = [
        'agent_login_name' => 'alghurasi',
        'agent_login_password' => 'alghurasi123',
        'client_code' => $clientCode,
        'client_token' => $clientToken
    ];

    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data),
            'timeout' => 30
        ]
    ];

    try {
        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        $response = json_decode($result, true);

        if ($response['status'] === 'success') {
            echo "✅ تم التحقق بنجاح!\n";
            $clientInfo = $response['data']['client_info'];
            echo "العميل: " . $clientInfo['client_name'] . "\n";
            echo "الحالة: " . $clientInfo['status_text'] . "\n";
            
            return [
                'success' => true,
                'client_name' => $clientInfo['client_name'],
                'client_status' => $clientInfo['status'],
                'is_active' => $clientInfo['status'] == 1
            ];
        } else {
            echo "❌ فشل التحقق: " . $response['message'] . "\n";
            return [
                'success' => false,
                'error' => $response['message'],
                'error_code' => $response['error_code'] ?? null
            ];
        }
    } catch (Exception $e) {
        echo "❌ خطأ في الاتصال: " . $e->getMessage() . "\n";
        return [
            'success' => false,
            'error' => 'خطأ في الاتصال: ' . $e->getMessage()
        ];
    }
}

// استخدام الدالة:
$result = verifyClientAlGhurasi('1000', 'ABC12345');
if ($result['success']) {
    echo "العميل نشط: " . ($result['is_active'] ? 'نعم' : 'لا') . "\n";
} else {
    echo "خطأ: " . $result['error'] . "\n";
}
?>

# C# (.NET):
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class AlGhurasiClient
{
    private static readonly HttpClient client = new HttpClient();
    
    public static async Task<dynamic> VerifyClient(string clientCode, string clientToken)
    {
        try
        {
            var data = new {
                agent_login_name = "alghurasi",
                agent_login_password = "alghurasi123",
                client_code = clientCode,
                client_token = clientToken
            };

            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(
                "http://***********:8080/api/external/verify-direct", 
                content
            );

            var result = await response.Content.ReadAsStringAsync();
            var parsed = JsonConvert.DeserializeObject<dynamic>(result);

            if (parsed.status == "success")
            {
                Console.WriteLine("✅ تم التحقق بنجاح!");
                Console.WriteLine($"العميل: {parsed.data.client_info.client_name}");
                Console.WriteLine($"الحالة: {parsed.data.client_info.status_text}");
                
                return new {
                    success = true,
                    clientName = (string)parsed.data.client_info.client_name,
                    clientStatus = (int)parsed.data.client_info.status,
                    isActive = (int)parsed.data.client_info.status == 1
                };
            }
            else
            {
                Console.WriteLine($"❌ فشل التحقق: {parsed.message}");
                return new {
                    success = false,
                    error = (string)parsed.message,
                    errorCode = (string)parsed.error_code
                };
            }
        }
        catch (Exception e)
        {
            Console.WriteLine($"❌ خطأ في الاتصال: {e.Message}");
            return new {
                success = false,
                error = $"خطأ في الاتصال: {e.Message}"
            };
        }
    }
}

// استخدام الكلاس:
var result = await AlGhurasiClient.VerifyClient("1000", "ABC12345");
Console.WriteLine($"العميل نشط: {result.isActive}");

========================================
🛡️ نصائح الأمان والاستخدام:
========================================

1. ✅ احفظ بيانات الوكيل بشكل آمن
2. ✅ لا تشارك كلمة المرور مع أحد
3. ✅ استخدم HTTPS في الإنتاج
4. ✅ تحقق من النتائج قبل اتخاذ إجراءات
5. ✅ اعتمد على معالجة الأخطاء

========================================
🔧 استكشاف الأخطاء:
========================================

# إذا حصلت على "API not found":
- تأكد من استخدام العنوان الصحيح: http://***********:8080
- تأكد من أن الخادم يعمل
- تأكد من أن المنفذ 8080 مفتوح

# إذا حصلت على "Connection failed":
- تحقق من اتصال الإنترنت
- تحقق من إعدادات جدار الحماية
- جرب من متصفح آخر أو جهاز آخر

# إذا حصلت على "AGENT_AUTH_FAILED":
- تأكد من اسم المستخدم: alghurasi
- تأكد من كلمة المرور: alghurasi123

========================================
📞 معلومات الاتصال:
========================================

عنوان الخادم: http://***********:8080
نقطة النهاية: /api/external/verify-direct
طريقة الطلب: POST
نوع المحتوى: application/json

بيانات وكيل الغراسي:
- اسم المستخدم: alghurasi
- كلمة المرور: alghurasi123
- رقم الوكيل: 3

========================================
🎉 الخلاصة:
========================================

✅ تم حل جميع المشاكل
✅ وكيل الغراسي جاهز للاستخدام
✅ طلب واحد فقط للتحقق
✅ يعمل من خارج السيرفر
✅ أمان متقدم ومراقبة شاملة

🚀 ابدأ الآن واستمتع بالتحقق السريع من العملاء!
