<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار عمود رقم الوكيل</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 28px;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        
        .result {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s;
            width: 100%;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .data-table th {
            background: #343a40;
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: bold;
        }
        
        .data-table td {
            padding: 10px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .agent-id {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .client-code {
            background: #e8f5e8;
            color: #2e7d32;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار عمود رقم الوكيل</h1>
            <p>فحص عرض رقم الوكيل في صفحة البيانات</p>
        </div>

        <!-- اختبار API -->
        <div class="test-section">
            <h3>📡 اختبار API البيانات</h3>
            <button onclick="testDataRecordsAPI()">🔍 اختبار API</button>
            <div id="apiStatus" class="status warning">
                ⏳ لم يتم الاختبار بعد
            </div>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <!-- عرض البيانات -->
        <div class="test-section">
            <h3>📊 عرض البيانات</h3>
            <button onclick="displayDataTable()">📋 عرض الجدول</button>
            <div id="tableContainer" style="display: none;">
                <table class="data-table" id="dataTable">
                    <thead>
                        <tr>
                            <th>المعرف</th>
                            <th>رقم الوكيل</th>
                            <th>رمز العميل</th>
                            <th>تاريخ العملية</th>
                            <th>حالة العملية</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- تحليل النتائج -->
        <div class="test-section">
            <h3>🔍 تحليل النتائج</h3>
            <div id="analysisResult" class="status warning">
                ⏳ قم بتشغيل الاختبار أولاً
            </div>
        </div>
    </div>

    <script>
        let testData = null;

        // اختبار API البيانات
        async function testDataRecordsAPI() {
            const statusDiv = document.getElementById('apiStatus');
            const resultDiv = document.getElementById('apiResult');
            
            statusDiv.className = 'status warning';
            statusDiv.textContent = '⏳ جاري اختبار API...';
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'جاري الاتصال بالخادم...';
            
            try {
                const response = await fetch('http://localhost:8080/api/data-records?page=1&limit=10', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                testData = data;
                
                let output = '🧪 نتائج اختبار API:\n';
                output += '=' .repeat(50) + '\n\n';
                output += `📊 حالة الاستجابة: ${response.ok ? 'نجح ✅' : 'فشل ❌'}\n`;
                output += `🔢 كود الاستجابة: ${response.status}\n\n`;
                
                if (data.dataRecords) {
                    output += `📋 عدد السجلات: ${data.dataRecords.length}\n`;
                    output += `📊 إجمالي السجلات: ${data.pagination?.total || 'غير محدد'}\n\n`;
                    
                    if (data.dataRecords.length > 0) {
                        output += '🔍 فحص أول سجل:\n';
                        const firstRecord = data.dataRecords[0];
                        output += `   المعرف: ${firstRecord.id}\n`;
                        output += `   رقم الوكيل: ${firstRecord.agentId !== undefined ? firstRecord.agentId : 'غير موجود ❌'}\n`;
                        output += `   رمز العميل: ${firstRecord.clientCode}\n`;
                        output += `   حالة العملية: ${firstRecord.operationStatus === 1 ? 'ناجحة ✅' : 'فاشلة ❌'}\n`;
                        output += `   تاريخ العملية: ${new Date(firstRecord.operationDate).toLocaleString('ar-SA')}\n\n`;
                        
                        // فحص جميع السجلات للتأكد من وجود agentId
                        const recordsWithAgentId = data.dataRecords.filter(r => r.agentId !== undefined);
                        output += `✅ السجلات التي تحتوي على رقم الوكيل: ${recordsWithAgentId.length}/${data.dataRecords.length}\n`;
                        
                        if (recordsWithAgentId.length === data.dataRecords.length) {
                            output += '🎉 جميع السجلات تحتوي على رقم الوكيل!\n';
                        } else {
                            output += '⚠️ بعض السجلات لا تحتوي على رقم الوكيل\n';
                        }
                    }
                } else {
                    output += '❌ لا توجد بيانات في الاستجابة\n';
                }
                
                resultDiv.textContent = output;
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ تم اختبار API بنجاح!';
                
                // تحليل النتائج
                analyzeResults(data);
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ فشل في الاتصال: ${error.message}`;
            }
        }
        
        // عرض جدول البيانات
        function displayDataTable() {
            if (!testData || !testData.dataRecords) {
                alert('قم بتشغيل اختبار API أولاً');
                return;
            }
            
            const tableContainer = document.getElementById('tableContainer');
            const tableBody = document.getElementById('tableBody');
            
            tableBody.innerHTML = '';
            
            testData.dataRecords.forEach(record => {
                const row = document.createElement('tr');
                
                row.innerHTML = `
                    <td>${record.id}</td>
                    <td><span class="agent-id">${record.agentId || 'غير محدد'}</span></td>
                    <td><span class="client-code">${record.clientCode}</span></td>
                    <td>${new Date(record.operationDate).toLocaleString('ar-SA')}</td>
                    <td><span class="${record.operationStatus === 1 ? 'status-success' : 'status-failed'}">
                        ${record.operationStatus === 1 ? 'ناجحة' : 'فاشلة'}
                    </span></td>
                `;
                
                tableBody.appendChild(row);
            });
            
            tableContainer.style.display = 'block';
        }
        
        // تحليل النتائج
        function analyzeResults(data) {
            const analysisDiv = document.getElementById('analysisResult');
            
            if (!data || !data.dataRecords) {
                analysisDiv.className = 'status error';
                analysisDiv.innerHTML = '❌ لا توجد بيانات للتحليل';
                return;
            }
            
            const records = data.dataRecords;
            const recordsWithAgentId = records.filter(r => r.agentId !== undefined && r.agentId !== null);
            const percentage = records.length > 0 ? (recordsWithAgentId.length / records.length * 100).toFixed(1) : 0;
            
            let analysis = '';
            
            if (recordsWithAgentId.length === records.length && records.length > 0) {
                analysisDiv.className = 'status success';
                analysis = `✅ ممتاز! جميع السجلات (${records.length}) تحتوي على رقم الوكيل<br>`;
                analysis += `🎯 النسبة: ${percentage}%<br>`;
                analysis += `🎉 عمود رقم الوكيل يعمل بشكل صحيح!`;
            } else if (recordsWithAgentId.length > 0) {
                analysisDiv.className = 'status warning';
                analysis = `⚠️ بعض السجلات تحتوي على رقم الوكيل<br>`;
                analysis += `📊 السجلات مع رقم الوكيل: ${recordsWithAgentId.length}/${records.length}<br>`;
                analysis += `🎯 النسبة: ${percentage}%<br>`;
                analysis += `🔧 يحتاج إلى مراجعة البيانات`;
            } else {
                analysisDiv.className = 'status error';
                analysis = `❌ لا توجد سجلات تحتوي على رقم الوكيل<br>`;
                analysis += `📊 إجمالي السجلات: ${records.length}<br>`;
                analysis += `🔧 يحتاج إلى إصلاح API أو قاعدة البيانات`;
            }
            
            analysisDiv.innerHTML = analysis;
        }
        
        // تشغيل الاختبار عند تحميل الصفحة
        window.onload = function() {
            // يمكن تشغيل الاختبار تلقائياً
            // testDataRecordsAPI();
        };
    </script>
</body>
</html>
