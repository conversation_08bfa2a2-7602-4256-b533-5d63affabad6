import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Alert,
  Grid,
  Paper
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  Person,
  Business
} from '@mui/icons-material';
import EnhancedButton from '../components/common/EnhancedButton';

const TestButtons = () => {
  const [clickedButton, setClickedButton] = useState('');
  const [clickCount, setClickCount] = useState(0);

  const handleButtonClick = (buttonName) => {
    setClickedButton(buttonName);
    setClickCount(prev => prev + 1);
    console.log(`تم الضغط على: ${buttonName}`);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" sx={{ mb: 3, textAlign: 'center' }}>
        🧪 اختبار وظائف الأزرار
      </Typography>

      {clickedButton && (
        <Alert severity="success" sx={{ mb: 3 }}>
          ✅ تم الضغط على: <strong>{clickedButton}</strong> (المرة رقم {clickCount})
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* اختبار الأزرار العادية */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                🔘 الأزرار العادية
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <EnhancedButton
                  icon={Add}
                  iconName="Add"
                  onClick={() => handleButtonClick('إضافة عميل جديد')}
                  color="primary"
                  variant="contained"
                  fallbackText="إضافة عميل جديد"
                >
                  إضافة عميل جديد
                </EnhancedButton>

                <EnhancedButton
                  icon={Person}
                  iconName="Person"
                  onClick={() => handleButtonClick('إضافة مستخدم جديد')}
                  color="secondary"
                  variant="contained"
                  fallbackText="إضافة مستخدم جديد"
                >
                  إضافة مستخدم جديد
                </EnhancedButton>

                <EnhancedButton
                  icon={Business}
                  iconName="Business"
                  onClick={() => handleButtonClick('إضافة وكيل جديد')}
                  color="success"
                  variant="contained"
                  fallbackText="إضافة وكيل جديد"
                >
                  إضافة وكيل جديد
                </EnhancedButton>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* اختبار أزرار الأيقونات */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                🎯 أزرار الأيقونات
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <EnhancedButton
                  icon={Visibility}
                  iconName="Visibility"
                  onClick={() => handleButtonClick('عرض')}
                  tooltip="عرض"
                  isIconButton={true}
                  size="small"
                  color="primary"
                  fallbackText="👁️"
                />

                <EnhancedButton
                  icon={Edit}
                  iconName="Edit"
                  onClick={() => handleButtonClick('تعديل')}
                  tooltip="تعديل"
                  isIconButton={true}
                  size="small"
                  color="warning"
                  fallbackText="✏️"
                />

                <EnhancedButton
                  icon={Delete}
                  iconName="Delete"
                  onClick={() => handleButtonClick('حذف')}
                  tooltip="حذف"
                  isIconButton={true}
                  size="small"
                  color="error"
                  fallbackText="🗑️"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* معلومات التشخيص */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                📊 معلومات التشخيص
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h4" color="primary">
                      {clickCount}
                    </Typography>
                    <Typography variant="body2">
                      إجمالي النقرات
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="secondary">
                      {clickedButton || 'لا يوجد'}
                    </Typography>
                    <Typography variant="body2">
                      آخر زر تم الضغط عليه
                    </Typography>
                  </Paper>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="h6" color="success.main">
                      {new Date().toLocaleTimeString('ar-SA')}
                    </Typography>
                    <Typography variant="body2">
                      الوقت الحالي
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>

              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>تعليمات الاختبار:</strong><br/>
                  1. اضغط على أي زر أعلاه<br/>
                  2. يجب أن تظهر رسالة نجاح<br/>
                  3. يجب أن يزيد عداد النقرات<br/>
                  4. تحقق من console.log في أدوات المطور
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TestButtons;
