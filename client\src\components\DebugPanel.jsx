import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  Collapse,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Alert
} from '@mui/material';
import {
  BugReport,
  ExpandMore,
  ExpandLess,
  Download,
  Clear,
  Refresh,
  Info,
  Error,
  Warning,
  CheckCircle
} from '@mui/icons-material';
import debugLogger from '../utils/debugLogger';

const DebugPanel = () => {
  const [open, setOpen] = useState(false);
  const [logs, setLogs] = useState([]);
  const [expandedLog, setExpandedLog] = useState(null);
  const [filter, setFilter] = useState('ALL');
  const [autoRefresh, setAutoRefresh] = useState(true);

  // تحديث السجلات
  const refreshLogs = () => {
    const allLogs = debugLogger.getLogs(filter === 'ALL' ? null : filter, 100);
    setLogs(allLogs.reverse()); // أحدث السجلات أولاً
  };

  useEffect(() => {
    refreshLogs();
    
    if (autoRefresh) {
      const interval = setInterval(refreshLogs, 2000); // تحديث كل ثانيتين
      return () => clearInterval(interval);
    }
  }, [filter, autoRefresh]);

  // أيقونات حسب نوع السجل
  const getLogIcon = (category) => {
    switch (category) {
      case 'ERROR': return <Error color="error" />;
      case 'WARNING': return <Warning color="warning" />;
      case 'SUCCESS': return <CheckCircle color="success" />;
      case 'INFO': return <Info color="info" />;
      default: return <Info />;
    }
  };

  // ألوان حسب نوع السجل
  const getLogColor = (category) => {
    switch (category) {
      case 'ERROR': return 'error';
      case 'WARNING': return 'warning';
      case 'SUCCESS': return 'success';
      case 'AUTH': return 'primary';
      case 'NETWORK': return 'info';
      default: return 'default';
    }
  };

  // تصدير السجلات
  const handleExport = () => {
    debugLogger.exportLogs();
  };

  // مسح السجلات
  const handleClear = () => {
    debugLogger.clear();
    refreshLogs();
  };

  // معلومات النظام
  const systemInfo = debugLogger.getSystemInfo();

  if (!open) {
    return (
      <Box
        sx={{
          position: 'fixed',
          bottom: 20,
          right: 20,
          zIndex: 9999
        }}
      >
        <Button
          variant="contained"
          color="secondary"
          startIcon={<BugReport />}
          onClick={() => setOpen(true)}
          sx={{
            borderRadius: '50px',
            boxShadow: 3
          }}
        >
          Debug
        </Button>
      </Box>
    );
  }

  return (
    <Dialog
      open={open}
      onClose={() => setOpen(false)}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center" gap={1}>
            <BugReport />
            <Typography variant="h6">Debug Panel</Typography>
            <Chip 
              label={`${logs.length} logs`} 
              size="small" 
              color="primary" 
            />
          </Box>
          <Box display="flex" gap={1}>
            <Button
              size="small"
              startIcon={<Refresh />}
              onClick={refreshLogs}
            >
              تحديث
            </Button>
            <Button
              size="small"
              startIcon={<Download />}
              onClick={handleExport}
            >
              تصدير
            </Button>
            <Button
              size="small"
              startIcon={<Clear />}
              onClick={handleClear}
              color="error"
            >
              مسح
            </Button>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {/* معلومات النظام */}
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>معلومات النظام</Typography>
            <Box display="flex" flexWrap="wrap" gap={1}>
              <Chip label={`Session: ${systemInfo.sessionId.substring(0, 12)}...`} size="small" />
              <Chip label={`Logs: ${systemInfo.totalLogs}`} size="small" />
              <Chip 
                label={`Token: ${systemInfo.localStorage.token ? 'موجود' : 'غير موجود'}`} 
                size="small" 
                color={systemInfo.localStorage.token ? 'success' : 'error'}
              />
              <Chip 
                label={`User: ${systemInfo.localStorage.user ? 'موجود' : 'غير موجود'}`} 
                size="small" 
                color={systemInfo.localStorage.user ? 'success' : 'error'}
              />
              <Chip 
                label={`Client: ${systemInfo.localStorage.clientData ? 'موجود' : 'غير موجود'}`} 
                size="small" 
                color={systemInfo.localStorage.clientData ? 'success' : 'error'}
              />
            </Box>
          </CardContent>
        </Card>

        {/* فلتر السجلات */}
        <Box display="flex" gap={2} mb={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>فلتر</InputLabel>
            <Select
              value={filter}
              label="فلتر"
              onChange={(e) => setFilter(e.target.value)}
            >
              <MenuItem value="ALL">الكل</MenuItem>
              <MenuItem value="AUTH">المصادقة</MenuItem>
              <MenuItem value="NETWORK">الشبكة</MenuItem>
              <MenuItem value="ERROR">الأخطاء</MenuItem>
              <MenuItem value="SUCCESS">النجاح</MenuItem>
              <MenuItem value="WARNING">التحذيرات</MenuItem>
              <MenuItem value="INFO">المعلومات</MenuItem>
            </Select>
          </FormControl>
          
          <Button
            variant={autoRefresh ? 'contained' : 'outlined'}
            size="small"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            تحديث تلقائي
          </Button>
        </Box>

        {/* قائمة السجلات */}
        <List sx={{ maxHeight: '400px', overflow: 'auto' }}>
          {logs.length === 0 ? (
            <Alert severity="info">لا توجد سجلات</Alert>
          ) : (
            logs.map((log) => (
              <React.Fragment key={log.id}>
                <ListItem
                  button
                  onClick={() => setExpandedLog(expandedLog === log.id ? null : log.id)}
                  sx={{
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <Box display="flex" alignItems="center" gap={1} width="100%">
                    {getLogIcon(log.category)}
                    <Box flexGrow={1}>
                      <Box display="flex" alignItems="center" gap={1} mb={0.5}>
                        <Chip 
                          label={log.category} 
                          size="small" 
                          color={getLogColor(log.category)}
                        />
                        <Typography variant="caption" color="text.secondary">
                          {new Date(log.timestamp).toLocaleTimeString('ar-SA')}
                        </Typography>
                      </Box>
                      <Typography variant="body2">
                        {log.message}
                      </Typography>
                    </Box>
                    <IconButton size="small">
                      {expandedLog === log.id ? <ExpandLess /> : <ExpandMore />}
                    </IconButton>
                  </Box>
                </ListItem>

                <Collapse in={expandedLog === log.id}>
                  <Box sx={{ pl: 4, pr: 2, pb: 2 }}>
                    <Typography variant="caption" color="text.secondary" display="block">
                      URL: {log.url}
                    </Typography>
                    {Object.keys(log.data).length > 0 && (
                      <Box mt={1}>
                        <Typography variant="caption" fontWeight="bold">البيانات:</Typography>
                        <Box
                          component="pre"
                          sx={{
                            fontSize: '0.75rem',
                            backgroundColor: 'grey.100',
                            p: 1,
                            borderRadius: 1,
                            overflow: 'auto',
                            maxHeight: '200px'
                          }}
                        >
                          {JSON.stringify(log.data, null, 2)}
                        </Box>
                      </Box>
                    )}
                  </Box>
                </Collapse>
              </React.Fragment>
            ))
          )}
        </List>
      </DialogContent>

      <DialogActions>
        <Button onClick={() => setOpen(false)}>إغلاق</Button>
      </DialogActions>
    </Dialog>
  );
};

export default DebugPanel;
