# 🧪 دليل صفحة اختبار المطورين - API المنفذ 8081

## 📋 نظرة عامة

صفحة اختبار المطورين الجديدة مصممة لاختبار API الخارجي على المنفذ 8081 مع دعم تشفير التوكن وأكواد البرمجة لجميع اللغات الشائعة.

## 🌐 الروابط

### 🔗 الروابط المحلية:
- **صفحة الاختبار:** http://localhost:8080/developer-api-test-8081.html
- **API المحلي:** http://localhost:8081/api/external/verify-direct

### 🔗 الروابط الخارجية:
- **API الخارجي:** http://***********:8081/api/external/verify-direct

## 🔐 البيانات التجريبية

### 🏢 الوكيل التجريبي:
```
اسم المستخدم: testuser
كلمة المرور: test123
الاسم: وكيل تجريبي للمطورين
الحالة: نشط ✅
```

### 👥 العملاء التجريبيون:

| رمز العميل | التوكن الأصلي | التوكن المشفر | الحالة | الوصف |
|------------|---------------|----------------|---------|--------|
| 1004 | TEST1004 | ENCRYPT_VEVTVDEwMDQ= | نشط ✅ | عميل تجريبي نشط |
| 1005 | TEST1005 | ENCRYPT_VEVTVDEwMDU= | غير نشط ❌ | عميل تجريبي غير نشط |
| 9999 | DEMO999 | ENCRYPT_REVNTU85OTk= | نشط ✅ | عميل تجريبي للعرض |
| 1000 | ABC12345 | ENCRYPT_QUJDMTIzNDU= | نشط ✅ | عميل تجريبي إضافي |
| 1001 | newToken456 | ENCRYPT_bmV3VG9rZW40NTY= | نشط ✅ | عميل جديد |

## 🔐 طريقة تشفير التوكن

### 📋 خطوات التشفير:

1. **التوكن الأصلي:** مثال "newToken456"
2. **تشفير Base64:** "bmV3VG9rZW40NTY="
3. **إضافة البادئة:** "ENCRYPT_" + Base64
4. **النتيجة النهائية:** "ENCRYPT_bmV3VG9rZW40NTY="

### 💻 أمثلة التشفير بلغات مختلفة:

#### JavaScript:
```javascript
function encryptToken(token) {
    const base64Token = btoa(token);
    return 'ENCRYPT_' + base64Token;
}
```

#### PHP:
```php
function encryptToken($token) {
    $base64Token = base64_encode($token);
    return 'ENCRYPT_' . $base64Token;
}
```

#### Python:
```python
import base64

def encrypt_token(token):
    base64_token = base64.b64encode(token.encode()).decode()
    return f'ENCRYPT_{base64_token}'
```

#### C#:
```csharp
public static string EncryptToken(string token)
{
    byte[] tokenBytes = Encoding.UTF8.GetBytes(token);
    string base64Token = Convert.ToBase64String(tokenBytes);
    return $"ENCRYPT_{base64Token}";
}
```

## 📡 استخدام API

### 📝 بنية الطلب:

```json
{
    "agent_login_name": "testuser",
    "agent_login_password": "test123",
    "client_code": "1004",
    "client_token": "ENCRYPT_VEVTVDEwMDQ="
}
```

### 📤 أنواع الردود:

#### ✅ نجاح - عميل نشط:
```json
{
    "status": "success",
    "client_status": 1
}
```

#### ⚠️ نجاح - عميل غير نشط:
```json
{
    "status": "success",
    "client_status": 0
}
```

#### 🔐 خطأ في بيانات الوكيل:
```json
{
    "status": "agent_error"
}
```

#### 👤 خطأ في بيانات العميل:
```json
{
    "status": "client_error"
}
```

#### ❌ خطأ في الخادم:
```json
{
    "status": "error"
}
```

## 🧪 الاختبارات المتاحة

### ⚡ اختبارات سريعة:

1. **✅ عميل نشط (1004):** اختبار عميل نشط مع توكن صحيح
2. **⚠️ عميل غير نشط (1005):** اختبار عميل غير نشط
3. **🆕 عميل جديد (1001):** اختبار عميل جديد مع توكن جديد
4. **✅ عميل آخر (1000):** اختبار عميل إضافي
5. **🔐 خطأ كلمة المرور:** اختبار خطأ في بيانات الوكيل
6. **👤 عميل غير موجود:** اختبار عميل غير موجود
7. **🔑 توكن خاطئ:** اختبار توكن خاطئ

### 🔧 أداة تشفير التوكن:

- أدخل التوكن الأصلي
- اضغط "🔐 تشفير التوكن"
- احصل على التوكن المشفر تلقائياً
- يتم تحديث أمثلة الكود تلقائياً

## 💻 أمثلة الكود الجاهزة

الصفحة تحتوي على أمثلة كود جاهزة للنسخ بلغات:

- **cURL:** للاختبار من سطر الأوامر
- **JavaScript:** للتطبيقات الويب
- **PHP:** للخوادم PHP
- **Python:** للتطبيقات Python
- **C#:** للتطبيقات .NET

## 🎯 كيفية الاستخدام

### 📍 للاختبار السريع:

1. افتح الصفحة: http://localhost:8080/developer-api-test-8081.html
2. اختر أحد الاختبارات السريعة
3. راقب النتيجة في قسم "نتيجة الاختبار"

### 📍 للاختبار المخصص:

1. أدخل بيانات الوكيل (testuser / test123)
2. أدخل رمز العميل
3. أدخل التوكن المشفر أو استخدم أداة التشفير
4. اضغط "🔍 تنفيذ التحقق بالبيانات الحالية"

### 📍 لنسخ الكود:

1. أدخل البيانات المطلوبة
2. اختر اللغة المطلوبة
3. اضغط زر "📋 نسخ" بجانب الكود
4. الصق الكود في مشروعك

## ⚠️ ملاحظات مهمة

### 🔐 الأمان:
- **يجب تشفير التوكن دائماً** قبل الإرسال
- **لا ترسل التوكن الأصلي أبداً** في الطلبات
- **استخدم البادئة "ENCRYPT_"** دائماً
- **تحقق من صحة التشفير** قبل الإرسال

### 🌐 الشبكة:
- **المنفذ 8081** مخصص للـ API الخارجي
- **المنفذ 8080** للواجهة الرئيسية
- **تأكد من فتح المنافذ** في جدار الحماية
- **اختبر محلياً أولاً** قبل الاستخدام الخارجي

### 📊 الأداء:
- **وقت الاستجابة المتوقع:** أقل من 1000ms
- **المهلة الزمنية:** 30 ثانية
- **معدل الطلبات:** غير محدود للاختبار

## 🔧 استكشاف الأخطاء

### ❌ مشاكل شائعة:

#### 1. خطأ في الاتصال:
- تحقق من تشغيل الخادم على المنفذ 8081
- تحقق من إعدادات جدار الحماية
- تحقق من الاتصال بالإنترنت

#### 2. خطأ في التشفير:
- تأكد من استخدام Base64 الصحيح
- تأكد من إضافة البادئة "ENCRYPT_"
- تحقق من صحة التوكن الأصلي

#### 3. خطأ في البيانات:
- تحقق من بيانات الوكيل (testuser / test123)
- تحقق من رمز العميل الصحيح
- تحقق من التوكن المشفر

## 📞 الدعم

للحصول على المساعدة:

1. **راجع هذا الدليل** أولاً
2. **اختبر الأمثلة الجاهزة** في الصفحة
3. **تحقق من رسائل الخطأ** في وحدة التحكم
4. **استخدم أداة التشخيص** المدمجة

## 🎉 الخلاصة

صفحة اختبار المطورين الجديدة توفر:

- ✅ **واجهة سهلة الاستخدام** للاختبار
- ✅ **تشفير تلقائي للتوكن** مع شرح مفصل
- ✅ **أمثلة كود جاهزة** لجميع اللغات الشائعة
- ✅ **اختبارات سريعة** لجميع الحالات
- ✅ **تشخيص مفصل** للأخطاء
- ✅ **دعم المنفذ الجديد 8081** مع تحسينات الأداء

استخدم هذه الصفحة لاختبار وتطوير تطبيقاتك بسهولة وثقة!
