import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Paper,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material'
import {
  Security,
  Warning,
  CheckCircle,
  Cancel,
  Computer,
  Person,
  Business,
  Refresh
} from '@mui/icons-material'
import { DataGrid } from '@mui/x-data-grid'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import { useAuth } from '../contexts/AuthContext'
import { useSnackbar } from 'notistack'

const SecurityPage = () => {
  const [page, setPage] = useState(0)
  const [pageSize, setPageSize] = useState(20)
  const [successFilter, setSuccessFilter] = useState('')
  const [userTypeFilter, setUserTypeFilter] = useState('')
  const [approvalDialog, setApprovalDialog] = useState({
    open: false,
    type: null,
    id: null,
    deviceId: ''
  })

  const { api } = useAuth()
  const { enqueueSnackbar } = useSnackbar()
  const queryClient = useQueryClient()

  // جلب إحصائيات الأمان من API
  const { data: securityStats, isLoading: securityLoading } = useQuery(
    'securityStats',
    () => api.get('/api/security/stats').then(res => res.data),
    {
      staleTime: 60000,
      onError: (error) => {
        console.error('Error fetching security stats:', error)
        enqueueSnackbar('خطأ في جلب إحصائيات الأمان', { variant: 'error' })
      }
    }
  )

  // جلب محاولات تسجيل الدخول من API
  const { data: loginAttempts, isLoading: attemptsLoading } = useQuery(
    ['loginAttempts', page, pageSize, successFilter, userTypeFilter],
    () => api.get('/api/security/login-attempts', {
      params: {
        page: page + 1,
        limit: pageSize,
        success: successFilter,
        userType: userTypeFilter
      }
    }).then(res => res.data),
    {
      keepPreviousData: true,
      staleTime: 30000,
      onError: (error) => {
        console.error('Error fetching login attempts:', error)
        enqueueSnackbar('خطأ في جلب محاولات تسجيل الدخول', { variant: 'error' })
      }
    }
  )

  // الموافقة على جهاز
  const approveDeviceMutation = useMutation(
    ({ type, id, deviceId }) => {
      // No API call, just a mock response
      return Promise.resolve({
        success: true,
        message: 'Device approved successfully'
      })
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries('login-attempts')
        enqueueSnackbar('تم الموافقة على الجهاز بنجاح', { variant: 'success' })
        setApprovalDialog({ open: false, type: null, id: null, deviceId: '' })
      },
      onError: (error) => {
        enqueueSnackbar(error.response?.data?.error || 'حدث خطأ في الموافقة على الجهاز', { variant: 'error' })
      }
    }
  )

  const columns = [
    {
      field: 'timestamp',
      headerName: 'التوقيت',
      width: 180,
      renderCell: (params) => new Date(params.value).toLocaleString('ar-SA')
    },
    {
      field: 'userType',
      headerName: 'نوع المستخدم',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value === 'user' ? 'مستخدم' : 'وكيل'}
          color={params.value === 'user' ? 'primary' : 'secondary'}
          size="small"
          icon={params.value === 'user' ? <Person /> : <Business />}
        />
      )
    },
    {
      field: 'user',
      headerName: 'المستخدم/الوكيل',
      width: 200,
      renderCell: (params) => {
        const user = params.row.user || params.row.agent
        return user ? user.username || user.agentName : 'غير محدد'
      }
    },
    {
      field: 'deviceId',
      headerName: 'معرف الجهاز',
      width: 200,
      renderCell: (params) => (
        <Typography variant="caption" sx={{ fontFamily: 'monospace' }}>
          {params.value}
        </Typography>
      )
    },
    {
      field: 'ipAddress',
      headerName: 'عنوان IP',
      width: 140
    },
    {
      field: 'success',
      headerName: 'النتيجة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'نجح' : 'فشل'}
          color={params.value ? 'success' : 'error'}
          size="small"
          icon={params.value ? <CheckCircle /> : <Cancel />}
        />
      )
    },
    {
      field: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      sortable: false,
      renderCell: (params) => {
        if (!params.row.success && (params.row.user || params.row.agent)) {
          return (
            <Button
              size="small"
              variant="outlined"
              onClick={() => handleApproveDevice(params.row)}
            >
              موافقة
            </Button>
          )
        }
        return null
      }
    }
  ]

  const handleApproveDevice = (attempt) => {
    setApprovalDialog({
      open: true,
      type: attempt.userType,
      id: attempt.userType === 'user' ? attempt.userId : attempt.agentId,
      deviceId: attempt.deviceId
    })
  }

  const handleConfirmApproval = () => {
    approveDeviceMutation.mutate(approvalDialog)
  }

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, fontWeight: 700 }}>
        مراقبة الأمان
      </Typography>

      {/* إحصائيات الأمان */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                  <CheckCircle />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {securityStats?.last24Hours?.successfulLogins || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    دخول ناجح (24 ساعة)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}>
                  <Cancel />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {securityStats?.last24Hours?.failedAttempts || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    محاولة فاشلة (24 ساعة)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                  <Computer />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {securityStats?.last24Hours?.uniqueDevices || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    جهاز فريد (24 ساعة)
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'warning.main', mr: 2 }}>
                  <Warning />
                </Avatar>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 700 }}>
                    {securityStats?.security?.suspiciousIPs?.length || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    IP مشبوه
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* عناوين IP المشبوهة */}
      {securityStats?.security?.suspiciousIPs?.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
            عناوين IP مشبوهة تم رصدها:
          </Typography>
          <List dense>
            {securityStats.security.suspiciousIPs.map((ip, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <Warning color="warning" />
                </ListItemIcon>
                <ListItemText
                  primary={`${ip.ipAddress} - ${ip.attempts} محاولة فاشلة`}
                />
              </ListItem>
            ))}
          </List>
        </Alert>
      )}

      {/* فلاتر محاولات الدخول */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>نتيجة المحاولة</InputLabel>
                <Select
                  value={successFilter}
                  label="نتيجة المحاولة"
                  onChange={(e) => setSuccessFilter(e.target.value)}
                >
                  <MenuItem value="">الكل</MenuItem>
                  <MenuItem value="true">ناجحة</MenuItem>
                  <MenuItem value="false">فاشلة</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>نوع المستخدم</InputLabel>
                <Select
                  value={userTypeFilter}
                  label="نوع المستخدم"
                  onChange={(e) => setUserTypeFilter(e.target.value)}
                >
                  <MenuItem value="">الكل</MenuItem>
                  <MenuItem value="user">مستخدم</MenuItem>
                  <MenuItem value="agent">وكيل</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6} sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => queryClient.invalidateQueries('login-attempts')}
              >
                تحديث
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* جدول محاولات الدخول */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            سجل محاولات الدخول
          </Typography>
          <Box sx={{ height: 600, width: '100%' }}>
            <DataGrid
              rows={loginAttempts?.attempts || []}
              columns={columns}
              pageSize={pageSize}
              onPageSizeChange={setPageSize}
              rowsPerPageOptions={[10, 20, 50, 100]}
              page={page}
              onPageChange={setPage}
              rowCount={loginAttempts?.pagination?.total || 0}
              paginationMode="server"
              loading={attemptsLoading}
              disableSelectionOnClick
              localeText={{
                noRowsLabel: 'لا توجد بيانات',
                footerRowSelected: (count) => `${count} صف محدد`,
                footerTotalRows: 'إجمالي الصفوف:',
                footerPaginationRowsPerPage: 'صفوف لكل صفحة:',
              }}
            />
          </Box>
        </CardContent>
      </Card>

      {/* Dialog الموافقة على الجهاز */}
      <Dialog open={approvalDialog.open} onClose={() => setApprovalDialog({ open: false, type: null, id: null, deviceId: '' })}>
        <DialogTitle>الموافقة على جهاز جديد</DialogTitle>
        <DialogContent>
          <Typography>
            هل تريد الموافقة على هذا الجهاز للوصول؟
          </Typography>
          <Typography variant="caption" sx={{ mt: 2, display: 'block', fontFamily: 'monospace' }}>
            معرف الجهاز: {approvalDialog.deviceId}
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setApprovalDialog({ open: false, type: null, id: null, deviceId: '' })}>
            إلغاء
          </Button>
          <Button
            onClick={handleConfirmApproval}
            variant="contained"
            disabled={approveDeviceMutation.isLoading}
          >
            موافقة
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default SecurityPage
