/**
 * اختبار بسيط للخادم
 */

async function testServer() {
  console.log('🧪 اختبار الخادم...\n');

  try {
    // اختبار Health Check
    console.log('1️⃣ اختبار Health Check:');
    const healthResponse = await fetch('http://localhost:8080/health');
    console.log(`   📡 Status: ${healthResponse.status}`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('   ✅ Health Check يعمل!');
      console.log(`   📊 Database: ${healthData.database}`);
    } else {
      console.log('   ❌ Health Check فشل');
    }
    console.log('');

    // اختبار API البيانات
    console.log('2️⃣ اختبار API البيانات:');
    const dataResponse = await fetch('http://localhost:8080/api/data-records?page=1&limit=5');
    console.log(`   📡 Status: ${dataResponse.status}`);
    
    if (dataResponse.ok) {
      const data = await dataResponse.json();
      console.log('   ✅ API البيانات يعمل!');
      console.log(`   📊 إجمالي السجلات: ${data.total}`);
      console.log(`   📋 السجلات في هذه الصفحة: ${data.dataRecords?.length}`);
      
      if (data.dataRecords && data.dataRecords.length > 0) {
        console.log('   📋 أحدث سجل:');
        const latest = data.dataRecords[0];
        console.log(`      ID: ${latest.id}`);
        console.log(`      الوكيل: ${latest.agentName}`);
        console.log(`      العميل: ${latest.clientName} (${latest.clientCode})`);
        console.log(`      الحالة: ${latest.operationStatus === 1 ? 'نجح' : 'فشل'}`);
      }
    } else {
      console.log('   ❌ API البيانات فشل');
    }
    console.log('');

    // اختبار Dashboard Stats
    console.log('3️⃣ اختبار Dashboard Stats:');
    const statsResponse = await fetch('http://localhost:8080/api/dashboard/stats');
    console.log(`   📡 Status: ${statsResponse.status}`);
    
    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      console.log('   ✅ Dashboard Stats يعمل!');
      console.log(`   👥 المستخدمين: ${stats.totalUsers}`);
      console.log(`   👥 العملاء: ${stats.totalClients}`);
      console.log(`   🏢 الوكلاء: ${stats.totalAgents}`);
      console.log(`   📊 سجلات البيانات: ${stats.totalDataRecords}`);
    } else {
      console.log('   ❌ Dashboard Stats فشل');
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
    console.log('\n🔧 تأكد من أن الخادم يعمل على المنفذ 8080');
  }
}

testServer().catch(console.error);
