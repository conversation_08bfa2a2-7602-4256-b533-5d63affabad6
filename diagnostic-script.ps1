# YemClient Diagnostic Script
# تشغيل: powershell -ExecutionPolicy Bypass -File diagnostic-script.ps1

param(
    [string]$ServerUrl = "http://***********:8080",
    [string]$TestUser = "admin",
    [string]$TestPassword = "admin123",
    [string]$TestClient = "1001",
    [string]$TestClientPassword = "123456"
)

Write-Host "🔍 YemClient Diagnostic Script" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host "Server URL: $ServerUrl" -ForegroundColor Yellow
Write-Host ""

# Function to make HTTP requests
function Invoke-ApiRequest {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null
    )
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            Headers = $Headers
            ContentType = "application/json"
        }
        
        if ($Body) {
            $params.Body = $Body
        }
        
        $response = Invoke-RestMethod @params
        return @{
            Success = $true
            Data = $response
            StatusCode = 200
        }
    }
    catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
            StatusCode = $_.Exception.Response.StatusCode.value__
        }
    }
}

# Test 1: Basic Connectivity
Write-Host "📡 Test 1: Basic Connectivity" -ForegroundColor Green
Write-Host "------------------------------"

$pingResult = Test-NetConnection -ComputerName "***********" -Port 8080 -WarningAction SilentlyContinue
if ($pingResult.TcpTestSucceeded) {
    Write-Host "✅ Network connection successful" -ForegroundColor Green
} else {
    Write-Host "❌ Network connection failed" -ForegroundColor Red
    Write-Host "   Check firewall and network settings" -ForegroundColor Yellow
}

# Test 2: Server Health
Write-Host "`n🏥 Test 2: Server Health" -ForegroundColor Green
Write-Host "------------------------"

$healthResult = Invoke-ApiRequest -Url "$ServerUrl/health"
if ($healthResult.Success) {
    Write-Host "✅ Server health check passed" -ForegroundColor Green
    Write-Host "   Status: $($healthResult.Data.status)" -ForegroundColor Cyan
    Write-Host "   Uptime: $([math]::Round($healthResult.Data.uptime / 60, 2)) minutes" -ForegroundColor Cyan
} else {
    Write-Host "❌ Server health check failed" -ForegroundColor Red
    Write-Host "   Error: $($healthResult.Error)" -ForegroundColor Yellow
}

# Test 3: System Info
Write-Host "`n💻 Test 3: System Information" -ForegroundColor Green
Write-Host "------------------------------"

$systemResult = Invoke-ApiRequest -Url "$ServerUrl/api/debug/system-info"
if ($systemResult.Success) {
    Write-Host "✅ System info retrieved" -ForegroundColor Green
    $info = $systemResult.Data.systemInfo
    Write-Host "   Node.js: $($info.server.nodeVersion)" -ForegroundColor Cyan
    Write-Host "   Platform: $($info.server.platform)" -ForegroundColor Cyan
    Write-Host "   Environment: $($info.server.environment)" -ForegroundColor Cyan
    Write-Host "   Memory Used: $([math]::Round($info.server.memory.used / 1024 / 1024, 2)) MB" -ForegroundColor Cyan
} else {
    Write-Host "❌ System info failed" -ForegroundColor Red
    Write-Host "   Error: $($systemResult.Error)" -ForegroundColor Yellow
}

# Test 4: User Login
Write-Host "`n👤 Test 4: User Login" -ForegroundColor Green
Write-Host "----------------------"

$deviceId = "diagnostic-device-$(Get-Date -Format 'yyyyMMddHHmmss')"
$userLoginBody = @{
    loginName = $TestUser
    password = $TestPassword
    deviceId = $deviceId
    userType = "user"
} | ConvertTo-Json

$userLoginResult = Invoke-ApiRequest -Url "$ServerUrl/api/auth/login" -Method "POST" -Body $userLoginBody
if ($userLoginResult.Success -and $userLoginResult.Data.success) {
    Write-Host "✅ User login successful" -ForegroundColor Green
    Write-Host "   User: $($userLoginResult.Data.user.username)" -ForegroundColor Cyan
    Write-Host "   Account Type: $($userLoginResult.Data.user.accountType)" -ForegroundColor Cyan
    Write-Host "   Token: $($userLoginResult.Data.token -ne $null ? 'Present' : 'Missing')" -ForegroundColor Cyan
    
    # Test session validation
    if ($userLoginResult.Data.token) {
        Write-Host "`n🔐 Test 4.1: Session Validation" -ForegroundColor Green
        $headers = @{ "Authorization" = "Bearer $($userLoginResult.Data.token)" }
        $validateResult = Invoke-ApiRequest -Url "$ServerUrl/api/auth/validate" -Headers $headers
        
        if ($validateResult.Success -and $validateResult.Data.success) {
            Write-Host "✅ Session validation successful" -ForegroundColor Green
        } else {
            Write-Host "❌ Session validation failed" -ForegroundColor Red
            Write-Host "   Error: $($validateResult.Data.message)" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "❌ User login failed" -ForegroundColor Red
    Write-Host "   Error: $($userLoginResult.Data.message)" -ForegroundColor Yellow
    Write-Host "   Status Code: $($userLoginResult.StatusCode)" -ForegroundColor Yellow
}

# Test 5: Client Login
Write-Host "`n🏢 Test 5: Client Login" -ForegroundColor Green
Write-Host "------------------------"

$clientLoginBody = @{
    loginName = $TestClient
    password = $TestClientPassword
    deviceId = $deviceId
    userType = "client"
} | ConvertTo-Json

$clientLoginResult = Invoke-ApiRequest -Url "$ServerUrl/api/auth/login" -Method "POST" -Body $clientLoginBody
if ($clientLoginResult.Success -and $clientLoginResult.Data.success) {
    Write-Host "✅ Client login successful" -ForegroundColor Green
    Write-Host "   Client: $($clientLoginResult.Data.user.username)" -ForegroundColor Cyan
    Write-Host "   Client Code: $($clientLoginResult.Data.user.clientCode)" -ForegroundColor Cyan
    Write-Host "   Account Type: $($clientLoginResult.Data.user.accountType)" -ForegroundColor Cyan
} else {
    Write-Host "❌ Client login failed" -ForegroundColor Red
    Write-Host "   Error: $($clientLoginResult.Data.message)" -ForegroundColor Yellow
    Write-Host "   Status Code: $($clientLoginResult.StatusCode)" -ForegroundColor Yellow
    
    # Try alternative passwords
    Write-Host "`n🔄 Trying alternative client passwords..." -ForegroundColor Yellow
    
    $altPasswords = @("client123", $TestClient, "password", "123")
    foreach ($altPassword in $altPasswords) {
        $altBody = @{
            loginName = $TestClient
            password = $altPassword
            deviceId = $deviceId
            userType = "client"
        } | ConvertTo-Json
        
        $altResult = Invoke-ApiRequest -Url "$ServerUrl/api/auth/login" -Method "POST" -Body $altBody
        if ($altResult.Success -and $altResult.Data.success) {
            Write-Host "✅ Client login successful with password: $altPassword" -ForegroundColor Green
            break
        }
    }
}

# Test 6: Login Attempts
Write-Host "`n📋 Test 6: Recent Login Attempts" -ForegroundColor Green
Write-Host "---------------------------------"

$attemptsResult = Invoke-ApiRequest -Url "$ServerUrl/api/debug/login-attempts?limit=5"
if ($attemptsResult.Success -and $attemptsResult.Data.success) {
    Write-Host "✅ Login attempts retrieved" -ForegroundColor Green
    $attempts = $attemptsResult.Data.attempts
    
    if ($attempts.Count -gt 0) {
        Write-Host "   Recent attempts:" -ForegroundColor Cyan
        foreach ($attempt in $attempts[0..([math]::Min(4, $attempts.Count-1))]) {
            $status = if ($attempt.success) { "✅" } else { "❌" }
            $time = [DateTime]::Parse($attempt.timestamp).ToString("HH:mm:ss")
            Write-Host "   $status $time - $($attempt.user) ($($attempt.userType)) from $($attempt.ipAddress)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "   No recent attempts found" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Failed to retrieve login attempts" -ForegroundColor Red
}

# Test 7: CORS Check
Write-Host "`n🌐 Test 7: CORS Configuration" -ForegroundColor Green
Write-Host "------------------------------"

try {
    $corsHeaders = @{
        "Origin" = "http://localhost:3000"
        "Access-Control-Request-Method" = "POST"
        "Access-Control-Request-Headers" = "Content-Type,Authorization"
    }
    
    $corsResult = Invoke-WebRequest -Uri "$ServerUrl/api/auth/login" -Method OPTIONS -Headers $corsHeaders -UseBasicParsing
    
    if ($corsResult.Headers["Access-Control-Allow-Origin"]) {
        Write-Host "✅ CORS configured correctly" -ForegroundColor Green
        Write-Host "   Allowed Origin: $($corsResult.Headers['Access-Control-Allow-Origin'])" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ CORS headers not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ CORS check failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Summary
Write-Host "`n📊 Diagnostic Summary" -ForegroundColor Magenta
Write-Host "=====================" -ForegroundColor Magenta

$issues = @()
if (-not $pingResult.TcpTestSucceeded) { $issues += "Network connectivity" }
if (-not $healthResult.Success) { $issues += "Server health" }
if (-not ($userLoginResult.Success -and $userLoginResult.Data.success)) { $issues += "User login" }
if (-not ($clientLoginResult.Success -and $clientLoginResult.Data.success)) { $issues += "Client login" }

if ($issues.Count -eq 0) {
    Write-Host "🎉 All tests passed! System is working correctly." -ForegroundColor Green
} else {
    Write-Host "⚠️ Issues detected:" -ForegroundColor Yellow
    foreach ($issue in $issues) {
        Write-Host "   - $issue" -ForegroundColor Red
    }
    
    Write-Host "`n💡 Recommendations:" -ForegroundColor Cyan
    Write-Host "   1. Check server logs: pm2 logs yemclient-server" -ForegroundColor White
    Write-Host "   2. Verify database connection" -ForegroundColor White
    Write-Host "   3. Check firewall settings for port 8080" -ForegroundColor White
    Write-Host "   4. Review TROUBLESHOOTING.md for detailed solutions" -ForegroundColor White
}

Write-Host "`n🔗 Useful URLs:" -ForegroundColor Cyan
Write-Host "   Health Check: $ServerUrl/health" -ForegroundColor White
Write-Host "   Test Page: Open test-login.html in browser" -ForegroundColor White
Write-Host "   Debug API: $ServerUrl/api/debug/system-info" -ForegroundColor White

Write-Host "`nDiagnostic completed at $(Get-Date)" -ForegroundColor Gray
