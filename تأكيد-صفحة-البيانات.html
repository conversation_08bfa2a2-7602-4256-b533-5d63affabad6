<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ تأكيد صفحة البيانات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 32px;
        }
        
        .api-test {
            background: #e3f2fd;
            border: 3px solid #2196f3;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .api-test h3 {
            color: #1565c0;
            margin: 0 0 20px 0;
            text-align: center;
        }
        
        button {
            background: linear-gradient(45deg, #2196f3, #21cbf3);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s;
            width: 100%;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(33, 150, 243, 0.4);
        }
        
        .result {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 20px 0;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .data-table {
            background: white;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        th {
            background: #4caf50;
            color: white;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        
        .status-success {
            background: #4caf50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .status-failed {
            background: #f44336;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .access-links {
            background: #f3e5f5;
            border: 3px solid #9c27b0;
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .access-links h3 {
            color: #6a1b9a;
            margin: 0 0 20px 0;
            text-align: center;
            font-size: 24px;
        }
        
        .access-link {
            display: block;
            background: linear-gradient(45deg, #9c27b0, #673ab7);
            color: white;
            text-decoration: none;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 18px;
            transition: all 0.3s;
            margin: 10px 0;
        }
        
        .access-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(156, 39, 176, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 تأكيد صفحة البيانات</h1>
            <p style="font-size: 20px; margin: 10px 0 0 0;">فحص API البيانات وعرض السجلات الحقيقية</p>
        </div>

        <!-- اختبار API البيانات -->
        <div class="api-test">
            <h3>🧪 اختبار API البيانات</h3>
            <button onclick="testDataRecordsAPI()">🔍 اختبار /api/data-records</button>
            <div id="apiResult" class="result" style="display: none;"></div>
        </div>

        <!-- عرض البيانات في جدول -->
        <div class="data-table" id="dataTable" style="display: none;">
            <h3 style="color: #4caf50; text-align: center;">📋 سجلات البيانات الحقيقية</h3>
            <table id="recordsTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>رقم الوكيل</th>
                        <th>اسم الوكيل</th>
                        <th>رمز العميل</th>
                        <th>اسم العميل</th>
                        <th>تاريخ العملية</th>
                        <th>الحالة</th>
                        <th>IP العميل</th>
                    </tr>
                </thead>
                <tbody id="recordsBody">
                </tbody>
            </table>
        </div>

        <!-- روابط الوصول -->
        <div class="access-links">
            <h3>🌐 روابط الوصول للنظام</h3>
            <a href="http://localhost:8080" target="_blank" class="access-link">
                🏠 فتح النظام المحلي
            </a>
            <a href="http://**************:8080" target="_blank" class="access-link">
                🌐 فتح النظام الداخلي
            </a>
            <a href="http://***********:8080" target="_blank" class="access-link">
                🌍 فتح النظام الخارجي
            </a>
        </div>
    </div>

    <script>
        // اختبار API البيانات
        async function testDataRecordsAPI() {
            const resultDiv = document.getElementById('apiResult');
            const dataTable = document.getElementById('dataTable');
            const recordsBody = document.getElementById('recordsBody');
            
            resultDiv.style.display = 'block';
            
            let output = '🧪 اختبار API البيانات:\n';
            output += '=' .repeat(60) + '\n\n';
            
            try {
                // اختبار API البيانات
                output += '📊 جلب سجلات البيانات...\n';
                const response = await fetch('/api/data-records?page=1&limit=20');
                
                output += `📡 Status: ${response.status}\n`;
                
                if (response.ok) {
                    const data = await response.json();
                    output += '✅ تم جلب البيانات بنجاح!\n\n';
                    
                    output += `📋 إجمالي السجلات: ${data.total}\n`;
                    output += `📄 الصفحة الحالية: ${data.pagination?.page}\n`;
                    output += `📊 السجلات في هذه الصفحة: ${data.dataRecords?.length}\n\n`;
                    
                    if (data.dataRecords && data.dataRecords.length > 0) {
                        output += '📋 أحدث 5 سجلات:\n';
                        output += '-'.repeat(40) + '\n';
                        
                        // عرض أحدث 5 سجلات
                        const recentRecords = data.dataRecords.slice(0, 5);
                        recentRecords.forEach((record, index) => {
                            const date = new Date(record.operationDate).toLocaleString('ar-EG');
                            const status = record.operationStatus === 1 ? 'نجح' : 'فشل';
                            
                            output += `${index + 1}. ID: ${record.id}\n`;
                            output += `   الوكيل: ${record.agentName}\n`;
                            output += `   العميل: ${record.clientName} (${record.clientCode})\n`;
                            output += `   التاريخ: ${date}\n`;
                            output += `   الحالة: ${status}\n`;
                            output += `   IP: ${record.clientIpAddress}\n\n`;
                        });
                        
                        // ملء الجدول
                        recordsBody.innerHTML = '';
                        data.dataRecords.forEach(record => {
                            const row = document.createElement('tr');
                            const date = new Date(record.operationDate).toLocaleDateString('ar-EG');
                            const statusClass = record.operationStatus === 1 ? 'status-success' : 'status-failed';
                            const statusText = record.operationStatus === 1 ? 'نجح' : 'فشل';
                            
                            row.innerHTML = `
                                <td>${record.id}</td>
                                <td>${record.agentId}</td>
                                <td>${record.agentName}</td>
                                <td>${record.clientCode}</td>
                                <td>${record.clientName}</td>
                                <td>${date}</td>
                                <td><span class="${statusClass}">${statusText}</span></td>
                                <td>${record.clientIpAddress}</td>
                            `;
                            recordsBody.appendChild(row);
                        });
                        
                        dataTable.style.display = 'block';
                        
                        output += '✅ تم عرض البيانات في الجدول أعلاه!\n';
                        output += '📊 جميع البيانات حقيقية من قاعدة البيانات\n';
                        
                    } else {
                        output += '⚠️ لا توجد سجلات في قاعدة البيانات\n';
                    }
                    
                } else {
                    const errorData = await response.json();
                    output += '❌ فشل في جلب البيانات!\n';
                    output += `📝 الخطأ: ${errorData.error || errorData.message}\n`;
                }
                
            } catch (error) {
                output += `❌ خطأ في الاتصال: ${error.message}\n`;
            }
            
            output += '\n' + '='.repeat(60) + '\n';
            output += '📋 الخلاصة:\n';
            output += '✅ API البيانات يعمل ويعرض البيانات الحقيقية\n';
            output += '✅ يمكن الوصول للبيانات من الواجهة الأمامية\n';
            output += '✅ البيانات تحتوي على معلومات الوكلاء والعملاء\n';
            output += '📊 إذا لم تظهر البيانات في صفحة النظام، المشكلة في:\n';
            output += '   - Authentication (تسجيل الدخول)\n';
            output += '   - Frontend rendering (عرض الواجهة)\n';
            output += '   - JavaScript errors (أخطاء JavaScript)\n';
            
            resultDiv.textContent = output;
        }
        
        // تشغيل الاختبار تلقائياً عند تحميل الصفحة
        window.onload = function() {
            setTimeout(testDataRecordsAPI, 2000);
        };
    </script>
</body>
</html>
