/**
 * فحص بيانات العميل 1005
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkClient1005() {
  try {
    console.log('🔍 فحص بيانات العميل 1005...\n');

    const client = await prisma.client.findFirst({
      where: { clientCode: 1005 }
    });

    if (client) {
      console.log('📋 بيانات العميل 1005:');
      console.log(`   ID: ${client.id}`);
      console.log(`   الاسم: ${client.clientName}`);
      console.log(`   الكود: ${client.clientCode}`);
      console.log(`   كلمة المرور: ${client.password}`);
      console.log(`   التوكن: ${client.token}`);
      console.log(`   الحالة: ${client.status === 1 ? 'نشط' : 'غير نشط'}`);
      console.log(`   تاريخ الإنشاء: ${client.createdAt}`);

      console.log('\n🧪 اختبار مع Token الصحيح:');
      
      // اختبار مع Token الصحيح
      const testResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent_login_name: 'agent001',
          agent_login_password: 'agent123',
          client_code: 1005,
          client_token: client.token // استخدام Token الصحيح
        })
      });

      console.log(`   📡 Status: ${testResponse.status}`);
      
      if (testResponse.ok) {
        const testData = await testResponse.json();
        console.log('   ✅ التحقق نجح مع Token الصحيح!');
        console.log(`   📊 Status: ${testData.status}`);
        console.log(`   👤 Client Status: ${testData.client_status}`);
        
        if (testData.client_status === 0) {
          console.log('   🎉 النظام يعرض حالة العميل غير النشط بشكل صحيح!');
        } else {
          console.log('   ⚠️ النظام لا يعرض حالة العميل غير النشط!');
        }
      } else {
        const errorData = await testResponse.json();
        console.log('   ❌ التحقق فشل حتى مع Token الصحيح!');
        console.log(`   📝 Error: ${errorData.status}`);
      }

    } else {
      console.log('❌ العميل 1005 غير موجود');
    }

  } catch (error) {
    console.error('❌ خطأ في فحص العميل 1005:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkClient1005().catch(console.error);
