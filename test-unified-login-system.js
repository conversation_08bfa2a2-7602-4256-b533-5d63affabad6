/**
 * اختبار نظام الدخول الموحد المحدث
 */

async function testUnifiedLoginSystem() {
  console.log('🎨 اختبار نظام الدخول الموحد المحدث...\n');

  let totalTests = 0;
  let passedTests = 0;

  try {
    // اختبار 1: صفحة الدخول الموحدة
    console.log('1️⃣ اختبار صفحة الدخول الموحدة:');
    totalTests++;
    
    const loginResponse = await fetch('http://localhost:8080/');
    console.log(`   📡 Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      const content = await loginResponse.text();
      
      const hasNewTitle = content.includes('نظام ادارة التطبيقات الموحد');
      const hasKhalidFont = content.includes('Khalid-Art-bold');
      const hasToggleButtons = content.includes('ToggleButtonGroup');
      const hasUserOption = content.includes('دخول مستخدم');
      const hasClientOption = content.includes('دخول عميل');
      const hasInteractiveDesign = content.includes('linear-gradient') && content.includes('transform');
      
      if (hasNewTitle && hasKhalidFont && hasToggleButtons && hasUserOption && hasClientOption && hasInteractiveDesign) {
        passedTests++;
        console.log('   ✅ صفحة الدخول الموحدة محدثة بالكامل!');
        console.log('   📋 العنوان الجديد: موجود');
        console.log('   🎨 خط Khalid-Art: مطبق');
        console.log('   🔄 أزرار التبديل: موجودة');
        console.log('   👤 خيار دخول المستخدم: موجود');
        console.log('   🏢 خيار دخول العميل: موجود');
        console.log('   ✨ التصميم التفاعلي: مطبق');
      } else {
        console.log('   ❌ صفحة الدخول الموحدة غير مكتملة');
        console.log(`   📋 العنوان الجديد: ${hasNewTitle ? '✅' : '❌'}`);
        console.log(`   🎨 خط Khalid-Art: ${hasKhalidFont ? '✅' : '❌'}`);
        console.log(`   🔄 أزرار التبديل: ${hasToggleButtons ? '✅' : '❌'}`);
        console.log(`   👤 خيار دخول المستخدم: ${hasUserOption ? '✅' : '❌'}`);
        console.log(`   🏢 خيار دخول العميل: ${hasClientOption ? '✅' : '❌'}`);
        console.log(`   ✨ التصميم التفاعلي: ${hasInteractiveDesign ? '✅' : '❌'}`);
      }
    } else {
      console.log('   ❌ فشل في تحميل صفحة الدخول');
    }
    console.log('');

    // اختبار 2: تسجيل دخول عميل
    console.log('2️⃣ اختبار تسجيل دخول عميل:');
    totalTests++;
    
    const clientLoginResponse = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 1001,
        password: 'Hash2020@'
      })
    });

    console.log(`   📡 Status: ${clientLoginResponse.status}`);
    
    if (clientLoginResponse.ok) {
      const clientData = await clientLoginResponse.json();
      if (clientData.success) {
        passedTests++;
        console.log('   ✅ تسجيل دخول العميل نجح!');
        console.log(`   🏢 العميل: ${clientData.client?.clientName}`);
        console.log(`   🔢 رمز العميل: ${clientData.client?.clientCode}`);
        console.log(`   🎫 رمز التوكن: ${clientData.client?.token || 'غير محدد'}`);
      } else {
        console.log('   ❌ تسجيل دخول العميل فشل في البيانات');
      }
    } else {
      const errorData = await clientLoginResponse.json();
      console.log('   ❌ تسجيل دخول العميل فشل');
      console.log(`   📝 Error: ${errorData.message}`);
    }
    console.log('');

    // اختبار 3: صفحة لوحة تحكم العميل
    console.log('3️⃣ اختبار صفحة لوحة تحكم العميل:');
    totalTests++;
    
    try {
      const fs = require('fs');
      const clientDashboardContent = fs.readFileSync('client/src/pages/ClientDashboard.jsx', 'utf8');
      
      const hasNewTitle = clientDashboardContent.includes('نظام ادارة التطبيقات الموحد');
      const hasKhalidFont = clientDashboardContent.includes('Khalid-Art-bold');
      const hasPasswordEdit = clientDashboardContent.includes('editingPassword');
      const hasTokenEdit = clientDashboardContent.includes('editingToken');
      const hasUpdateFunctions = clientDashboardContent.includes('handleUpdatePassword') && clientDashboardContent.includes('handleUpdateToken');
      const hasReadOnlyFields = clientDashboardContent.includes('clientCode') && clientDashboardContent.includes('clientName');
      
      if (hasNewTitle && hasKhalidFont && hasPasswordEdit && hasTokenEdit && hasUpdateFunctions && hasReadOnlyFields) {
        passedTests++;
        console.log('   ✅ صفحة لوحة تحكم العميل مكتملة!');
        console.log('   📋 العنوان الجديد: موجود');
        console.log('   🎨 خط Khalid-Art: مطبق');
        console.log('   🔑 تعديل كلمة المرور: متاح');
        console.log('   🎫 تعديل رمز التوكن: متاح');
        console.log('   ⚙️ دوال التحديث: موجودة');
        console.log('   👁️ الحقول للقراءة فقط: مطبقة');
      } else {
        console.log('   ❌ صفحة لوحة تحكم العميل غير مكتملة');
        console.log(`   📋 العنوان الجديد: ${hasNewTitle ? '✅' : '❌'}`);
        console.log(`   🎨 خط Khalid-Art: ${hasKhalidFont ? '✅' : '❌'}`);
        console.log(`   🔑 تعديل كلمة المرور: ${hasPasswordEdit ? '✅' : '❌'}`);
        console.log(`   🎫 تعديل رمز التوكن: ${hasTokenEdit ? '✅' : '❌'}`);
        console.log(`   ⚙️ دوال التحديث: ${hasUpdateFunctions ? '✅' : '❌'}`);
        console.log(`   👁️ الحقول للقراءة فقط: ${hasReadOnlyFields ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.log('   ❌ فشل في قراءة ملف صفحة لوحة تحكم العميل');
      console.log(`   📝 Error: ${error.message}`);
    }
    console.log('');

    // اختبار 4: API تحديث بيانات العميل
    console.log('4️⃣ اختبار API تحديث بيانات العميل:');
    totalTests++;
    
    const updateResponse = await fetch('http://localhost:8080/api/client/update', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientId: 1,
        password: 'newTestPassword2024',
        token: 'newTestToken2024'
      })
    });

    console.log(`   📡 Status: ${updateResponse.status}`);
    
    if (updateResponse.ok) {
      const updateData = await updateResponse.json();
      if (updateData.success) {
        passedTests++;
        console.log('   ✅ API تحديث بيانات العميل يعمل!');
        console.log('   🔑 تحديث كلمة المرور: نجح');
        console.log('   🎫 تحديث رمز التوكن: نجح');
        console.log(`   📝 رسالة النجاح: ${updateData.message}`);
      } else {
        console.log('   ❌ API تحديث بيانات العميل فشل في البيانات');
      }
    } else {
      const errorData = await updateResponse.json();
      console.log('   ❌ API تحديث بيانات العميل فشل');
      console.log(`   📝 Error: ${errorData.message}`);
    }
    console.log('');

    // اختبار 5: التحقق من التصميم التفاعلي
    console.log('5️⃣ اختبار التصميم التفاعلي:');
    totalTests++;
    
    if (loginResponse.ok) {
      const content = await loginResponse.text();
      
      const hasHoverEffects = content.includes('&:hover') && content.includes('transform');
      const hasGradients = content.includes('linear-gradient');
      const hasBoxShadows = content.includes('boxShadow');
      const hasTransitions = content.includes('transition');
      const hasInteractiveIcons = content.includes('fontSize: 24');
      
      if (hasHoverEffects && hasGradients && hasBoxShadows && hasTransitions && hasInteractiveIcons) {
        passedTests++;
        console.log('   ✅ التصميم التفاعلي مطبق بالكامل!');
        console.log('   🖱️ تأثيرات الـ hover: موجودة');
        console.log('   🎨 التدرجات اللونية: مطبقة');
        console.log('   💫 الظلال: مطبقة');
        console.log('   🔄 الانتقالات: مطبقة');
        console.log('   🎯 الأيقونات التفاعلية: مطبقة');
      } else {
        console.log('   ❌ التصميم التفاعلي غير مكتمل');
        console.log(`   🖱️ تأثيرات الـ hover: ${hasHoverEffects ? '✅' : '❌'}`);
        console.log(`   🎨 التدرجات اللونية: ${hasGradients ? '✅' : '❌'}`);
        console.log(`   💫 الظلال: ${hasBoxShadows ? '✅' : '❌'}`);
        console.log(`   🔄 الانتقالات: ${hasTransitions ? '✅' : '❌'}`);
        console.log(`   🎯 الأيقونات التفاعلية: ${hasInteractiveIcons ? '✅' : '❌'}`);
      }
    }

    console.log('\n' + '='.repeat(70));
    console.log('📋 ملخص اختبار نظام الدخول الموحد المحدث:');
    console.log(`📊 إجمالي الاختبارات: ${totalTests}`);
    console.log(`✅ الاختبارات الناجحة: ${passedTests}`);
    console.log(`❌ الاختبارات الفاشلة: ${totalTests - passedTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('');

    if (passedTests === totalTests) {
      console.log('🎉 جميع المتطلبات تم تطبيقها بنجاح!');
      console.log('✅ صفحة دخول موحدة بتصميم خرافي');
      console.log('✅ خيارين للدخول (مستخدم وعميل)');
      console.log('✅ أيقونات ذكية تفاعلية');
      console.log('✅ العنوان الجديد: "نظام ادارة التطبيقات الموحد"');
      console.log('✅ خط Khalid-Art-bold مطبق');
      console.log('✅ صفحة بيانات عميل احترافية');
      console.log('✅ تحديث كلمة المرور والتوكن فقط');
      console.log('✅ جميع البيانات الأخرى للقراءة فقط');
      console.log('');
      console.log('🚀 النظام جاهز للاستخدام الإنتاجي!');
    } else {
      console.log('⚠️ بعض المتطلبات لم تكتمل بعد');
    }

    console.log('\n🌐 الروابط النهائية:');
    console.log('   📍 الدخول الموحد: http://localhost:8080');
    console.log('   📍 لوحة تحكم العميل: http://localhost:8080/client-dashboard');
    console.log('');
    console.log('💡 بيانات الاختبار:');
    console.log('   👤 مستخدم: hash8080 / hash8080');
    console.log('   🏢 عميل: 1001 / Hash2020@');
    console.log('   🏢 عميل: 1000 / 112223333');
    console.log('');
    console.log('🔧 المميزات المطبقة:');
    console.log('   📝 العنوان: نظام ادارة التطبيقات الموحد');
    console.log('   🎨 الخط: Khalid-Art-bold');
    console.log('   🔄 خيارات الدخول: مستخدم وعميل');
    console.log('   🎯 أيقونات تفاعلية: تكبر عند التحديد');
    console.log('   💫 تصميم خرافي: تدرجات وظلال وانتقالات');
    console.log('   🏢 صفحة عميل: معلومات للقراءة + تعديل كلمة المرور والتوكن');
    console.log('   🔒 أمان: إنهاء جلسة كامل عند الخروج');

  } catch (error) {
    console.error('❌ خطأ في اختبار نظام الدخول الموحد:', error.message);
  }
}

testUnifiedLoginSystem().catch(console.error);
