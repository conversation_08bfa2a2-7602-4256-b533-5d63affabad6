/**
 * فحص بيانات العملاء في قاعدة البيانات
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkClientData() {
  try {
    console.log('🔍 فحص بيانات العملاء...\n');

    // جلب جميع العملاء
    const clients = await prisma.client.findMany({
      orderBy: { clientCode: 'asc' }
    });

    console.log(`📊 إجمالي العملاء: ${clients.length}\n`);

    clients.forEach((client, index) => {
      console.log(`${index + 1}️⃣ العميل رقم ${client.clientCode}:`);
      console.log(`   📝 الاسم: ${client.clientName || 'غير محدد'}`);
      console.log(`   📱 التطبيق: ${client.appName || 'غير محدد'}`);
      console.log(`   🌐 IP: ${client.ipAddress || 'غير محدد'}`);
      console.log(`   📊 الحالة: ${client.status === 1 ? 'نشط' : 'غير نشط'}`);
      console.log(`   🔑 كلمة المرور: ${client.password || 'غير محددة'}`);
      console.log(`   🎫 الرمز: ${client.token || 'غير محدد'}`);
      console.log(`   📅 تاريخ الإنشاء: ${client.createdAt}`);
      console.log('');
    });

    // البحث عن العميل 1004 تحديداً
    const client1004 = clients.find(c => c.clientCode === 1004);
    if (client1004) {
      console.log('🎯 تفاصيل العميل 1004 (للاختبار):');
      console.log(`   رمز العميل: ${client1004.clientCode}`);
      console.log(`   كلمة المرور: ${client1004.password}`);
      console.log(`   الرمز: ${client1004.token}`);
      console.log(`   الحالة: ${client1004.status}`);
      console.log('');
      
      // اقتراح كلمات مرور للاختبار
      console.log('💡 جرب هذه القيم لتسجيل الدخول:');
      console.log(`   رمز العميل: ${client1004.clientCode}`);
      if (client1004.password) {
        console.log(`   كلمة المرور: ${client1004.password}`);
      }
      if (client1004.token) {
        console.log(`   أو استخدم الرمز: ${client1004.token}`);
      }
    }

  } catch (error) {
    console.error('❌ خطأ في فحص بيانات العملاء:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkClientData();
