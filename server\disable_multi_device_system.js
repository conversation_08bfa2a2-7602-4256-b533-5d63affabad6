const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function disableMultiDeviceSystem() {
  try {
    console.log('🚫 إلغاء نظام الأجهزة المتعددة')
    console.log('==============================')
    
    // البحث عن المستخدم محمد الحاشدي
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        isActive: true
      }
    })

    if (!user) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }
    
    console.log(`👤 المستخدم: ${user.username}`)
    console.log(`📱 الأجهزة الحالية: ${user.deviceId || 'غير محدد'}`)
    
    // استخدام الجهاز الأول فقط
    const primaryDevice = 'honbi5nms_1751046183491'  // الجهاز الأساسي
    
    console.log(`📱 الجهاز الأساسي المختار: ${primaryDevice}`)
    
    // تحديث المستخدم ليستخدم جهاز واحد فقط
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { 
        deviceId: primaryDevice,  // جهاز واحد فقط بدون فاصلة
        isActive: true
      }
    })
    
    console.log('✅ تم تحديث المستخدم لاستخدام جهاز واحد فقط!')
    
    // اختبار تسجيل الدخول
    console.log('')
    console.log('🧪 اختبار تسجيل الدخول:')
    
    try {
      const response = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          loginName: 'hash8080',
          password: 'hash8080',
          deviceId: primaryDevice
        })
      })
      
      const result = await response.json()
      console.log(`📱 الجهاز الأساسي: ${response.ok ? '✅ نجح' : '❌ فشل'} - ${result.error || 'تم بنجاح'}`)
      
      if (response.ok) {
        console.log('🎉 تسجيل الدخول يعمل بنجاح!')
      }
      
    } catch (error) {
      console.log(`📱 الجهاز الأساسي: ❌ خطأ في الاتصال - ${error.message}`)
    }
    
    console.log('')
    console.log('🎉 النتيجة النهائية:')
    console.log(`👤 المستخدم: ${updatedUser.username}`)
    console.log(`📱 الجهاز: ${updatedUser.deviceId}`)
    console.log(`✅ نظام الجهاز الواحد مفعل`)
    
    console.log('')
    console.log('📝 ملاحظة: تم إلغاء نظام الأجهزة المتعددة')
    console.log('📝 المستخدم يمكنه الآن الدخول من جهاز واحد فقط')
    
  } catch (error) {
    console.error('❌ خطأ في إلغاء نظام الأجهزة المتعددة:', error)
  } finally {
    await prisma.$disconnect()
  }
}

disableMultiDeviceSystem()
