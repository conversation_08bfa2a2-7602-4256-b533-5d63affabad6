const http = require('http');

function testLogin(loginName, password, deviceId = 'test-device') {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      loginName,
      password,
      deviceId
    });

    const options = {
      hostname: 'localhost',
      port: 8080,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': data.length
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsed
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(data);
    req.end();
  });
}

async function testAllLogins() {
  console.log('🧪 Testing Login System...\n');

  const testCases = [
    { name: 'Admin User', loginName: 'admin', password: 'admin123456' },
    { name: 'Hash8080 User', loginName: 'hash8080', password: 'admin123456' },
    { name: 'Invalid User', loginName: 'invalid', password: 'wrong' },
    { name: 'Wrong Password', loginName: 'admin', password: 'wrongpassword' }
  ];

  for (const testCase of testCases) {
    try {
      console.log(`Testing: ${testCase.name}`);
      const result = await testLogin(testCase.loginName, testCase.password);
      
      console.log(`Status: ${result.status}`);
      
      if (result.data.success) {
        console.log(`✅ Login successful`);
        console.log(`   User: ${result.data.user.username}`);
        console.log(`   Login Name: ${result.data.user.loginName}`);
        console.log(`   Active: ${result.data.user.isActive}`);
        console.log(`   Permissions: ${Object.keys(result.data.user.permissions || {}).length} keys`);
      } else {
        console.log(`❌ Login failed: ${result.data.message}`);
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
    }
    
    console.log('');
  }

  console.log('🏁 Login tests completed!');
}

testAllLogins().catch(console.error);
