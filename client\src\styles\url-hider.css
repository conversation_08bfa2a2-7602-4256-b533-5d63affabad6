/* إخفاء URL وجعله ثابت */

/* منع عرض hash في شريط العنوان */
html {
  scroll-behavior: smooth;
}

/* إخفاء أي مؤشرات للتنقل في URL */
body {
  overflow-anchor: none;
}

/* منع تمييز النص في شريط العنوان */
* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* السماح بتحديد النص في المحتوى فقط */
input, textarea, [contenteditable] {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* إخفاء أي عناصر قد تكشف URL */
.url-indicator,
.breadcrumb-url,
.location-display {
  display: none !important;
}

/* منع عرض tooltip للروابط */
a[href] {
  text-decoration: none;
}

a[href]:hover::after {
  display: none !important;
}

/* إخفاء status bar في المتصفحات */
@media print {
  .no-print {
    display: none !important;
  }
}

/* منع عرض URL في developer tools */
.devtools-hide {
  display: none !important;
}

/* إخفاء أي مؤشرات للمسار الحالي */
.current-path,
.route-indicator,
.navigation-path {
  visibility: hidden !important;
}

/* تأكيد عدم عرض hash */
:target {
  scroll-margin-top: 0;
}

/* منع عرض URL في print preview */
@page {
  margin: 0;
}

@media print {
  body::before {
    content: "";
    display: none;
  }
  
  a[href^="http"]::after,
  a[href^="https://"]::after,
  a[href^="#"]::after {
    content: "" !important;
    display: none !important;
  }
}
