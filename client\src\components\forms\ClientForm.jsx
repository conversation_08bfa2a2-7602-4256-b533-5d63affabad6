import React, { useEffect } from 'react'
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Divider
} from '@mui/material'
import { useForm, Controller } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import * as yup from 'yup'
import { useMutation, useQuery } from 'react-query'
import { useAuth } from '../../contexts/AuthContext'
import { useSnackbar } from 'notistack'
import CopyButton from '../common/CopyButton'

const schema = yup.object({
  clientName: yup.string().required('اسم العميل مطلوب'),
  appName: yup.string().required('اسم التطبيق مطلوب'),
  cardNumber: yup.string()
    .required('رقم البطاقة مطلوب')
    .min(8, 'رقم البطاقة يجب أن يكون 8-11 رقم')
    .max(11, 'رقم البطاقة يجب أن يكون 8-11 رقم')
    .matches(/^\d+$/, 'رقم البطاقة يجب أن يحتوي على أرقام فقط'),
  password: yup.string()
    .required('كلمة المرور مطلوبة')
    .min(8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    .matches(/[A-Z]/, 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل')
    .matches(/[a-z]/, 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل')
    .matches(/\d/, 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل')
    .matches(/[!@#$%^&*]/, 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل (!@#$%^&*)'),
  ipAddress: yup.string()
    .required('عنوان IP مطلوب')
    .matches(
      /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
      'عنوان IP غير صحيح'
    ),
  status: yup.number().required('حالة العميل مطلوبة'),
  userId: yup.number().nullable()
})

const ClientForm = ({ client, onSuccess, readOnly = false }) => {
  const { api, user } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      clientName: '',
      appName: '',
      cardNumber: '',
      password: '',
      ipAddress: '',
      status: 1,
      dataId: null,
      userId: null
    }
  })

  // تم إزالة جلب الوكلاء والمستخدمين لأنها لا تظهر في النموذج

  // إضافة/تحديث عميل
  const mutation = useMutation(
    (data) => {
      if (client) {
        return api.put(`/api/clients/${client.id}`, data)
      } else {
        return api.post('/api/clients', data)
      }
    },
    {
      onSuccess: () => {
        enqueueSnackbar(
          client ? 'تم تحديث العميل بنجاح' : 'تم إضافة بيانات العميل بنجاح',
          { variant: 'success' }
        )
        onSuccess()
      },
      onError: (error) => {
        enqueueSnackbar(
          error.response?.data?.error || 'حدث خطأ في حفظ البيانات',
          { variant: 'error' }
        )
      }
    }
  )

  // تعبئة النموذج عند التعديل
  useEffect(() => {
    if (client) {
      reset({
        clientName: client.clientName || '',
        appName: client.appName || '',
        cardNumber: client.cardNumber || '',
        password: client.password || '',
        ipAddress: client.ipAddress || '',
        status: client.status || 1,
        agentId: client.agentId || null,
        userId: client.userId || null
      })
    }
  }, [client, reset])

  const onSubmit = (data) => {
    // تسجيل معلومات المستخدم للتشخيص
    console.log('🔍 معلومات المستخدم الحالي:');
    console.log('   - user:', user);
    console.log('   - user.id:', user?.id);
    console.log('   - client (وضع التحديث):', client);

    // تنظيف البيانات
    const cleanData = {
      clientName: data.clientName,
      appName: data.appName,
      cardNumber: data.cardNumber,
      password: data.password,
      ipAddress: data.ipAddress,
      status: data.status
    }

    // إضافة userId عند الإنشاء فقط (ليس عند التحديث)
    if (!client) {
      // جرب جميع الطرق للحصول على userId
      const userId = user?.id || user?.userId || 1; // استخدم 1 كقيمة افتراضية
      cleanData.userId = parseInt(userId); // تأكد من أنه رقم
      console.log('✅ تم إضافة userId للعميل الجديد:', userId);
      console.log('   - مصدر userId:', user?.id ? 'user.id' : user?.userId ? 'user.userId' : 'قيمة افتراضية');
      console.log('   - userId كرقم:', parseInt(userId));


    }

    // إزالة القيم الفارغة (لكن احتفظ بـ userId)
    Object.keys(cleanData).forEach(key => {
      if (key !== 'userId' && (cleanData[key] === '' || cleanData[key] === null || cleanData[key] === undefined)) {
        delete cleanData[key];
      }
    });

    console.log('📤 إرسال بيانات العميل:');
    console.log(JSON.stringify(cleanData, null, 2));
    console.log('🔍 تأكيد وجود userId:', cleanData.userId);
    console.log('🔍 جميع المفاتيح:', Object.keys(cleanData));
    console.log('🔍 هل userId موجود في المفاتيح؟', Object.keys(cleanData).includes('userId'));

    // إرسال البيانات
    mutation.mutate(cleanData)
  }

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        {/* معلومات المستخدم الحالي - فقط عند الإضافة */}
        {!client && (
          <Grid item xs={12}>
            <Box sx={{
              p: 2,
              backgroundColor: 'primary.light',
              borderRadius: 1,
              mb: 2,
              color: 'white'
            }}>
              <Typography variant="h6" gutterBottom>
                👤 معلومات المستخدم المسؤول
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Typography variant="body2">
                    <strong>الاسم:</strong> {user?.username || 'غير محدد'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography variant="body2">
                    <strong>اسم المستخدم:</strong> {user?.loginName || 'غير محدد'}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Typography variant="body2">
                    <strong>رقم المستخدم:</strong> {user?.id || 'غير محدد'}
                  </Typography>
                </Grid>
              </Grid>
              <Typography variant="caption" sx={{ mt: 1, display: 'block', opacity: 0.9 }}>
                ℹ️ سيتم ربط العميل الجديد بهذا المستخدم تلقائياً
              </Typography>
            </Box>
          </Grid>
        )}

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            معلومات العميل الأساسية
          </Typography>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="clientName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="اسم العميل"
                error={!!errors.clientName}
                helperText={errors.clientName?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="appName"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="اسم التطبيق"
                error={!!errors.appName}
                helperText={errors.appName?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="cardNumber"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="رقم البطاقة (8-11 رقم)"
                error={!!errors.cardNumber}
                helperText={errors.cardNumber?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="password"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="كلمة المرور (8+ أحرف، حروف كبيرة وصغيرة، أرقام، رموز)"
                type="password"
                error={!!errors.password}
                helperText={errors.password?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        {/* حقل رقم المستخدم */}
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="رقم المستخدم المسؤول"
            value={client ? (client.userId || client.user?.id || 'غير محدد') : (user?.id || 'غير محدد')}
            disabled={true}
            sx={{
              '& .MuiInputBase-input': {
                backgroundColor: '#f5f5f5',
                color: '#666'
              }
            }}
            helperText="يتم تعيين رقم المستخدم تلقائياً"
          />
        </Grid>

        {/* عرض التوكن للعملاء الموجودين فقط */}
        {client && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="التوكن الأمني"
              value={client.token || 'لم يتم توليده بعد'}
              disabled
              helperText="توكن مشفر فريد للعميل"
              InputProps={{
                endAdornment: client.token && (
                  <CopyButton
                    text={client.token}
                    tooltip="نسخ التوكن"
                    successMessage="تم نسخ التوكن بنجاح!"
                    errorMessage="فشل في نسخ التوكن"
                    size="small"
                    sx={{ mr: 1 }}
                  />
                )
              }}
            />
          </Grid>
        )}

        <Grid item xs={12} md={6}>
          <Controller
            name="ipAddress"
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                fullWidth
                label="عنوان IP"
                placeholder="***********"
                error={!!errors.ipAddress}
                helperText={errors.ipAddress?.message}
                disabled={readOnly}
              />
            )}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <Controller
            name="status"
            control={control}
            render={({ field }) => (
              <FormControl fullWidth error={!!errors.status}>
                <InputLabel>حالة العميل</InputLabel>
                <Select
                  {...field}
                  label="حالة العميل"
                  disabled={readOnly}
                >
                  <MenuItem value={1}>مفعل</MenuItem>
                  <MenuItem value={2}>محظور</MenuItem>
                </Select>
              </FormControl>
            )}
          />
        </Grid>





        {!readOnly && (
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                disabled={mutation.isLoading}
              >
                {mutation.isLoading ? 'جاري الحفظ...' : (client ? 'تحديث' : 'إضافة')}
              </Button>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  )
}

export default ClientForm
