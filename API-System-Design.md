# نظام API للوكلاء والعملاء - التصميم الشامل

## 🎯 **نظرة عامة**

نظام API يسمح للوكلاء والعملاء بالاتصال الخارجي للوصول إلى المعلومات والخدمات.

## 🌐 **عنوان API الأساسي**
```
http://185.11.8.26:8080/api/external/
```

## 🔐 **1. مصادقة الوكيل (Agent Authentication)**

### **المسار:**
```
POST /api/external/agent/auth
```

### **المعاملات المطلوبة:**
```json
{
  "login_name": "agent_username",
  "login_password": "agent_password"
}
```

### **الردود:**

#### **✅ نجح التحقق:**
```json
{
  "status": "success",
  "message": "Agent authenticated successfully",
  "agent_id": 123,
  "agent_name": "اسم الوكيل",
  "agency_type": "وكيل يمن موبايل",
  "token": "generated_session_token"
}
```

#### **❌ فشل التحقق:**
```json
{
  "status": "error",
  "message": "Invalid login credentials",
  "error_code": "AUTH_FAILED"
}
```

---

## 👤 **2. التحقق من بيانات العميل (Client Verification)**

### **المسار:**
```
POST /api/external/client/verify
```

### **المعاملات المطلوبة:**
```json
{
  "agent_token": "session_token_from_auth",
  "client_code": "CLIENT001",
  "token": "client_token_8chars"
}
```

### **الردود:**

#### **✅ العميل موجود ومفعل:**
```json
{
  "status": "success",
  "message": "Client verified successfully",
  "client_data": {
    "client_code": "CLIENT001",
    "client_name": "اسم العميل",
    "app_name": "تطبيق العميل",
    "status": 1,
    "ip_address": "*************",
    "created_date": "2025-06-30"
  }
}
```

#### **✅ العميل موجود وغير مفعل:**
```json
{
  "status": "success",
  "message": "Client found but inactive",
  "client_data": {
    "client_code": "CLIENT001",
    "status": 0
  }
}
```

#### **❌ العميل غير موجود:**
```json
{
  "status": "error",
  "message": "Client not found",
  "error_code": "CLIENT_NOT_FOUND"
}
```

#### **❌ التوكن غير مطابق:**
```json
{
  "status": "error",
  "message": "Invalid client token",
  "error_code": "TOKEN_MISMATCH"
}
```

#### **❌ توكن الوكيل غير صالح:**
```json
{
  "status": "error",
  "message": "Invalid agent session",
  "error_code": "INVALID_AGENT_TOKEN"
}
```

---

## 📊 **3. خدمات إضافية للوكلاء**

### **أ. قائمة العملاء المرتبطين:**
```
GET /api/external/agent/clients
Headers: Authorization: Bearer {agent_token}
```

### **ب. إحصائيات الوكيل:**
```
GET /api/external/agent/stats
Headers: Authorization: Bearer {agent_token}
```

### **ج. سجل العمليات:**
```
GET /api/external/agent/operations
Headers: Authorization: Bearer {agent_token}
```

---

## 🔒 **4. نظام الأمان**

### **التحقق من التوكن:**
- كل طلب يتطلب توكن صالح
- انتهاء صلاحية التوكن بعد 24 ساعة
- تسجيل جميع المحاولات في جدول login_attempts

### **تسجيل العمليات:**
- حفظ كل عملية في جدول data_records
- تتبع IP address للطلبات
- مراقبة المحاولات المشبوهة

### **حدود الاستخدام:**
- حد أقصى 1000 طلب/ساعة لكل وكيل
- حد أقصى 100 طلب/دقيقة لكل وكيل

---

## 📝 **5. أكواد الأخطاء**

| الكود | الوصف | الحل |
|-------|--------|------|
| AUTH_FAILED | فشل مصادقة الوكيل | تحقق من اسم المستخدم وكلمة المرور |
| CLIENT_NOT_FOUND | العميل غير موجود | تحقق من رمز العميل |
| TOKEN_MISMATCH | التوكن غير مطابق | تحقق من توكن العميل |
| INVALID_AGENT_TOKEN | توكن الوكيل غير صالح | قم بتسجيل الدخول مرة أخرى |
| RATE_LIMIT_EXCEEDED | تجاوز حد الطلبات | انتظر قبل إرسال طلبات جديدة |
| SERVER_ERROR | خطأ في الخادم | حاول مرة أخرى لاحقاً |

---

## 🧪 **6. أمثلة الاستخدام**

### **مثال كامل - التحقق من عميل:**

```bash
# 1. مصادقة الوكيل
curl -X POST http://185.11.8.26:8080/api/external/agent/auth \
  -H "Content-Type: application/json" \
  -d '{
    "login_name": "agent001",
    "login_password": "password123"
  }'

# 2. التحقق من العميل
curl -X POST http://185.11.8.26:8080/api/external/client/verify \
  -H "Content-Type: application/json" \
  -d '{
    "agent_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "client_code": "CLIENT001",
    "token": "ABC12345"
  }'
```

---

## 📋 **7. متطلبات التطبيق**

### **الملفات المطلوبة:**
1. `server/routes/external-api.js` - مسارات API الخارجية
2. `server/middleware/agent-auth.js` - مصادقة الوكلاء
3. `server/utils/token-manager.js` - إدارة التوكن
4. `server/utils/rate-limiter.js` - تحديد معدل الطلبات
5. `API-Documentation.md` - وثائق API باللغة الإنجليزية

### **قاعدة البيانات:**
- جدول `agent_sessions` لحفظ جلسات الوكلاء
- تحديث جدول `data_records` لتسجيل العمليات
- فهرسة الجداول لتحسين الأداء

---

## 🎯 **8. الخطوات التالية**

1. ✅ إنشاء مسارات API الخارجية
2. ✅ تطبيق نظام مصادقة الوكلاء
3. ✅ إنشاء نظام إدارة التوكن
4. ✅ تطبيق تحديد معدل الطلبات
5. ✅ إنشاء وثائق API الإنجليزية
6. ✅ اختبار النظام
7. ✅ نشر النظام

---

## 📞 **9. الدعم الفني**

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +967-xxx-xxxx
- الموقع: http://185.11.8.26:8080/api/docs
