const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Installing YemClient Server Dependencies...\n');

// التحقق من Node.js version
const nodeVersion = process.version;
const requiredVersion = 'v18.0.0';

console.log(`📋 Node.js Version: ${nodeVersion}`);

if (nodeVersion < requiredVersion) {
  console.error(`❌ Node.js ${requiredVersion} or higher is required`);
  process.exit(1);
}

// إنشاء مجلد logs
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('📁 Created logs directory');
}

try {
  // تثبيت التبعيات
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });
  
  // تثبيت PM2 عالمياً
  console.log('\n🔧 Installing PM2 globally...');
  try {
    execSync('npm install -g pm2', { stdio: 'inherit' });
    console.log('✅ PM2 installed successfully');
  } catch (error) {
    console.log('⚠️  PM2 installation failed, you may need to install it manually:');
    console.log('   npm install -g pm2');
  }
  
  // توليد Prisma client
  console.log('\n🗄️  Generating Prisma client...');
  execSync('npx prisma generate', { stdio: 'inherit' });
  
  console.log('\n✅ Installation completed successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Configure your .env file');
  console.log('2. Run database migrations: npm run db:migrate');
  console.log('3. Start development server: npm run dev');
  console.log('4. Or start production server: npm run pm2:start');
  
} catch (error) {
  console.error('\n❌ Installation failed:', error.message);
  process.exit(1);
}
