const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 8083;
const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Middleware
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

console.log('🚀 Starting debug server on port', PORT);

// Test endpoint
app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  console.log('🔥 Login request received:', {
    body: req.body,
    ip: req.ip
  });

  try {
    const { loginName, password, deviceId, userType = 'auto' } = req.body;
    
    console.log('📋 Login data:', {
      loginName,
      hasPassword: !!password,
      deviceId,
      userType
    });

    let user = null;
    let accountType = null;

    // Search for user
    if (userType === 'auto' || userType === 'user') {
      console.log('🔍 Searching for user...');
      try {
        user = await prisma.user.findUnique({
          where: { loginName }
        });
        
        if (user) {
          accountType = 'user';
          console.log('✅ User found:', {
            id: user.id,
            username: user.username,
            loginName: user.loginName
          });
        } else {
          console.log('❌ User not found');
        }
      } catch (error) {
        console.error('Error searching users:', error.message);
      }
    }

    // Search for client
    if (!user && (userType === 'auto' || userType === 'client')) {
      console.log('🔍 Searching for client...');
      try {
        const clientCode = parseInt(loginName);
        if (!isNaN(clientCode)) {
          user = await prisma.client.findFirst({
            where: { clientCode }
          });
          
          if (user) {
            accountType = 'client';
            console.log('✅ Client found:', {
              id: user.id,
              clientName: user.clientName,
              clientCode: user.clientCode
            });
          } else {
            console.log('❌ Client not found');
          }
        }
      } catch (error) {
        console.error('Error searching clients:', error.message);
      }
    }

    if (!user) {
      console.log('❌ Account not found');
      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // Verify password
    console.log('🔐 Verifying password...');
    let isValidPassword = false;
    
    if (accountType === 'client') {
      if (user.password) {
        if (user.password.startsWith('$2')) {
          isValidPassword = await bcrypt.compare(password, user.password);
        } else {
          isValidPassword = password === user.password;
        }
      } else {
        // Default passwords for clients
        const defaultPasswords = ['123456', 'client123', user.clientCode.toString()];
        isValidPassword = defaultPasswords.includes(password);
      }
    } else {
      // User password verification
      if (user.password && user.password.startsWith('$2')) {
        isValidPassword = await bcrypt.compare(password, user.password);
      } else {
        isValidPassword = password === user.password;
      }
    }

    console.log('🔐 Password verification result:', isValidPassword);

    if (!isValidPassword) {
      console.log('❌ Invalid password');
      return res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

    // Create response data
    let responseData;
    if (accountType === 'user') {
      responseData = {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        accountType: 'user',
        permissions: user.permissions || {},
        deviceId: deviceId || `auto-${user.loginName}-${Date.now()}`
      };
    } else {
      responseData = {
        id: user.id,
        username: user.clientName,
        loginName: user.clientCode.toString(),
        clientCode: user.clientCode,
        accountType: 'client',
        appName: user.appName,
        deviceId: deviceId || `auto-client-${user.clientCode}-${Date.now()}`
      };
    }

    // Create JWT token
    const token = jwt.sign({
      id: user.id,
      accountType,
      loginName: accountType === 'user' ? user.loginName : user.clientCode.toString(),
      username: accountType === 'user' ? user.username : user.clientName
    }, JWT_SECRET, { expiresIn: '24h' });

    console.log('🎉 Login successful!');

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: responseData,
      token,
      sessionId: 'test-session-' + Date.now()
    });

  } catch (error) {
    console.error('💥 Login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Debug server running on http://localhost:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`💚 Health: http://localhost:${PORT}/health`);
  console.log(`🔑 Login: http://localhost:${PORT}/api/auth/login`);
});

// Handle shutdown
process.on('SIGINT', async () => {
  console.log('🛑 Shutting down debug server...');
  await prisma.$disconnect();
  process.exit(0);
});
