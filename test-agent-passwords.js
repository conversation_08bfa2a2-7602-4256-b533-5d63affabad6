/**
 * اختبار كلمات مرور الوكلاء
 */

const bcrypt = require('bcrypt');

async function testAgentPasswords() {
  console.log('🔐 اختبار كلمات مرور الوكلاء...\n');

  // كلمات المرور المشفرة من قاعدة البيانات
  const agents = [
    {
      loginName: 'testuser',
      hashedPassword: '$2b$10$LYaBMrpUoX2R3r.Hx9DMnu/Fu.NqxCJNkbzKAfZl4mST1UrFZXk6u',
      possiblePasswords: ['test123', 'testuser', 'password', '123456', 'test']
    },
    {
      loginName: 'agent001',
      hashedPassword: '$2b$10$spwxypgCbWlz8pjUEmo.c.5nVaT8j9EBu.U49zFmIVGK1gPG8n4aW',
      possiblePasswords: ['agent123', 'agent001', 'password', '123456', 'agent']
    },
    {
      loginName: 'almutarib',
      hashedPassword: '$2b$10$y.wcXz3SByE8nIj2uXuyjOAQ3OYoF/92ZM6I/T7h7p.spTSVMn6aW',
      possiblePasswords: ['almutarib123', 'almutarib', 'password', '123456', 'mutarib']
    },
    {
      loginName: 'alghurasi',
      hashedPassword: '$2b$10$PjGpN8mLtb4oNw9fiyUk4eSIefkPzVVeW08O0kPZWG2Pe93WZsngC',
      possiblePasswords: ['alghurasi123', 'alghurasi', 'password', '123456', 'ghurasi']
    }
  ];

  for (const agent of agents) {
    console.log(`👤 اختبار الوكيل: ${agent.loginName}`);
    
    let foundPassword = null;
    
    for (const password of agent.possiblePasswords) {
      try {
        const isMatch = await bcrypt.compare(password, agent.hashedPassword);
        if (isMatch) {
          foundPassword = password;
          break;
        }
      } catch (error) {
        console.log(`   ❌ خطأ في اختبار كلمة المرور ${password}: ${error.message}`);
      }
    }
    
    if (foundPassword) {
      console.log(`   ✅ كلمة المرور الصحيحة: ${foundPassword}`);
    } else {
      console.log(`   ❌ لم يتم العثور على كلمة المرور الصحيحة`);
    }
    
    console.log('');
  }
}

testAgentPasswords().catch(console.error);
