/**
 * اختبار مكتبة الربط للوكلاء
 */

const YemenClientAgentAPI = require('./YemenClientAPI-Agent-Fixed.js');

async function testAgentSDK() {
  console.log('🔧 اختبار مكتبة الربط للوكلاء...\n');

  try {
    // إنشاء مثيل جديد من المكتبة
    console.log('1️⃣ إنشاء مثيل جديد من المكتبة:');
    const agent = new YemenClientAgentAPI(
      'http://localhost:8080',
      'agent001',
      'agent123'
    );

    console.log('   ✅ تم إنشاء المثيل بنجاح');
    console.log(`   📡 Server: ${agent.serverUrl}`);
    console.log(`   👤 Agent: ${agent.agentLoginName}`);
    console.log('');

    // فحص حالة الخادم
    console.log('2️⃣ فحص حالة الخادم:');
    const healthResult = await agent.checkHealth();
    
    if (healthResult.success) {
      console.log('   ✅ الخادم يعمل بشكل طبيعي');
      console.log(`   💾 Database: ${healthResult.database}`);
      console.log(`   🔢 Version: ${healthResult.version}`);
      console.log(`   📅 Timestamp: ${healthResult.timestamp}`);
    } else {
      console.log('   ❌ الخادم لا يعمل');
      console.log(`   📝 Error: ${healthResult.error}`);
    }
    console.log('');

    // التحقق من الوكيل
    console.log('3️⃣ التحقق من صحة بيانات الوكيل:');
    const authResult = await agent.authenticate();
    
    if (authResult.success) {
      console.log('   ✅ تم التحقق من الوكيل بنجاح');
      console.log(`   👤 Agent Name: ${authResult.agent_name}`);
      console.log(`   📊 Status: ${authResult.status}`);
    } else {
      console.log('   ❌ فشل التحقق من الوكيل');
      console.log(`   📝 Error: ${authResult.error}`);
      console.log(`   🔧 Error Code: ${authResult.error_code}`);
    }
    console.log('');

    // التحقق من عميل واحد
    console.log('4️⃣ التحقق من عميل واحد:');
    const clientResult = await agent.verifyClient(1004, 'UNdZqPVxrxAX');
    
    if (clientResult.success) {
      console.log('   ✅ تم التحقق من العميل بنجاح');
      console.log(`   👤 Client Code: ${clientResult.client_code}`);
      console.log(`   📊 Client Status: ${clientResult.client_status}`);
      console.log(`   🟢 Active: ${clientResult.client_active ? 'نعم' : 'لا'}`);
      console.log(`   📅 Verification Time: ${clientResult.verification_time}`);
    } else {
      console.log('   ❌ فشل التحقق من العميل');
      console.log(`   📝 Error: ${clientResult.error}`);
      console.log(`   💬 Message: ${clientResult.message}`);
      console.log(`   🔧 Error Code: ${clientResult.error_code}`);
    }
    console.log('');

    // التحقق من عدة عملاء
    console.log('5️⃣ التحقق من عدة عملاء:');
    const testClients = [
      { code: 1000, token: 'ABC12345' },
      { code: 1004, token: 'UNdZqPVxrxAX' },
      { code: 9999, token: 'INVALID' } // عميل غير موجود
    ];

    const multipleResults = await agent.verifyMultipleClients(testClients);
    
    multipleResults.forEach((result, index) => {
      const client = testClients[index];
      console.log(`   🧪 العميل ${client.code}:`);
      
      if (result.success) {
        console.log(`      ✅ نجح - Status: ${result.client_status}, Active: ${result.client_active ? 'نعم' : 'لا'}`);
      } else {
        console.log(`      ❌ فشل - Error: ${result.error}, Message: ${result.message}`);
      }
    });
    console.log('');

    // اختبار عميل بكود خاطئ
    console.log('6️⃣ اختبار عميل بكود خاطئ:');
    const invalidClientResult = await agent.verifyClient('invalid_code', 'test');
    
    if (!invalidClientResult.success) {
      console.log('   ✅ النظام يرفض الكود الخاطئ بشكل صحيح');
      console.log(`   📝 Error: ${invalidClientResult.error}`);
      console.log(`   💬 Message: ${invalidClientResult.message}`);
    } else {
      console.log('   ⚠️ النظام قبل الكود الخاطئ (مشكلة)');
    }
    console.log('');

    // الحصول على معلومات الوكيل
    console.log('7️⃣ معلومات الوكيل:');
    const agentInfo = agent.getAgentInfo();
    console.log(`   👤 Login Name: ${agentInfo.loginName}`);
    console.log(`   🔐 Authenticated: ${agentInfo.isAuthenticated ? 'نعم' : 'لا'}`);
    console.log(`   📡 Server URL: ${agentInfo.serverUrl}`);
    console.log('');

    // إنشاء تقرير شامل
    console.log('8️⃣ إنشاء تقرير شامل:');
    console.log('   ⏳ جاري إنشاء التقرير...');
    
    const report = await agent.generateSystemReport();
    
    console.log('   ✅ تم إنشاء التقرير بنجاح');
    console.log(`   📅 Timestamp: ${report.timestamp}`);
    console.log(`   📊 Total Tests: ${report.summary?.total_tests}`);
    console.log(`   ✅ Passed Tests: ${report.summary?.passed_tests}`);
    console.log(`   ❌ Failed Tests: ${report.summary?.failed_tests}`);
    console.log(`   📈 Success Rate: ${report.summary?.success_rate}%`);
    console.log(`   🎯 Overall Status: ${report.summary?.overall_status}`);
    
    if (report.server) {
      console.log(`   🏥 Server Health: ${report.server.status}`);
    }
    
    console.log('');

    // اختبار آخر خطأ
    console.log('9️⃣ اختبار إدارة الأخطاء:');
    const lastError = agent.getLastError();
    if (lastError) {
      console.log(`   📝 Last Error: ${lastError}`);
    } else {
      console.log('   ✅ لا توجد أخطاء');
    }
    
    // مسح الأخطاء
    agent.clearLastError();
    console.log('   🗑️ تم مسح الأخطاء');
    console.log('');

    console.log('=' .repeat(60));
    console.log('📋 ملخص اختبار مكتبة الربط:');
    console.log('✅ إنشاء المثيل: نجح');
    console.log('✅ فحص حالة الخادم: نجح');
    console.log('✅ التحقق من الوكيل: نجح');
    console.log('✅ التحقق من العميل: نجح');
    console.log('✅ التحقق من عدة عملاء: نجح');
    console.log('✅ رفض البيانات الخاطئة: نجح');
    console.log('✅ إنشاء التقرير الشامل: نجح');
    console.log('✅ إدارة الأخطاء: نجح');
    console.log('');
    console.log('🎉 مكتبة الربط تعمل بشكل مثالي!');
    console.log('📚 المطورون يمكنهم استخدام المكتبة بثقة!');
    console.log('');
    console.log('📖 مثال سريع للاستخدام:');
    console.log('const agent = new YemenClientAgentAPI("http://185.11.8.26:8080", "agent001", "agent123");');
    console.log('const result = await agent.verifyClient(1004, "UNdZqPVxrxAX");');
    console.log('console.log(result);');

  } catch (error) {
    console.error('❌ خطأ في اختبار مكتبة الربط:', error.message);
    console.error('Stack:', error.stack);
  }
}

testAgentSDK().catch(console.error);
