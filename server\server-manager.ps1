# 🚀 YemClient Server Manager
# سكريبت إدارة الخوادم - فحص وإيقاف

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("check", "stop", "restart", "status", "help")]
    [string]$Action = "help"
)

Write-Host "🚀 YemClient Server Manager" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

function Show-Help {
    Write-Host ""
    Write-Host "الاستخدام:" -ForegroundColor Yellow
    Write-Host "  .\server-manager.ps1 -Action <command>" -ForegroundColor White
    Write-Host ""
    Write-Host "الأوامر المتاحة:" -ForegroundColor Yellow
    Write-Host "  check    - فحص الخوادم التي تعمل" -ForegroundColor Green
    Write-Host "  stop     - إيقاف جميع الخوادم" -ForegroundColor Red
    Write-Host "  restart  - إعادة تشغيل الخوادم" -ForegroundColor Blue
    Write-Host "  status   - حالة مفصلة للخوادم" -ForegroundColor Magenta
    Write-Host "  help     - عرض هذه المساعدة" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "أمثلة:" -ForegroundColor Yellow
    Write-Host "  .\server-manager.ps1 -Action check" -ForegroundColor White
    Write-Host "  .\server-manager.ps1 -Action stop" -ForegroundColor White
}

function Check-NodeProcesses {
    Write-Host ""
    Write-Host "🔍 فحص عمليات Node.js..." -ForegroundColor Yellow
    
    try {
        $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
        
        if ($nodeProcesses) {
            Write-Host "✅ تم العثور على $($nodeProcesses.Count) عملية Node.js:" -ForegroundColor Green
            $nodeProcesses | Format-Table Id, ProcessName, CPU, @{Name="Memory(MB)";Expression={[math]::Round($_.WorkingSet/1MB,2)}} -AutoSize
        } else {
            Write-Host "❌ لا توجد عمليات Node.js تعمل" -ForegroundColor Red
        }
        
        return $nodeProcesses
    } catch {
        Write-Host "❌ خطأ في فحص عمليات Node.js: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Check-PM2Status {
    Write-Host ""
    Write-Host "🔍 فحص حالة PM2..." -ForegroundColor Yellow
    
    try {
        $pm2Output = & pm2 status 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ PM2 يعمل:" -ForegroundColor Green
            Write-Host $pm2Output
        } else {
            Write-Host "❌ PM2 غير متاح أو لا يعمل" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ PM2 غير مثبت أو غير متاح" -ForegroundColor Red
    }
}

function Check-Ports {
    Write-Host ""
    Write-Host "🔍 فحص المنافذ..." -ForegroundColor Yellow
    
    $ports = @(8080, 3000, 5173)
    
    foreach ($port in $ports) {
        try {
            $connection = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
            
            if ($connection) {
                Write-Host "✅ المنفذ $port مستخدم" -ForegroundColor Green
                $connection | Format-Table LocalAddress, LocalPort, RemoteAddress, State -AutoSize
            } else {
                Write-Host "❌ المنفذ $port غير مستخدم" -ForegroundColor Red
            }
        } catch {
            Write-Host "❌ خطأ في فحص المنفذ $port" -ForegroundColor Red
        }
    }
}

function Test-ServerConnection {
    Write-Host ""
    Write-Host "🔍 اختبار الاتصال بالخادم..." -ForegroundColor Yellow
    
    $urls = @(
        "http://localhost:8080/health",
        "http://localhost:8080",
        "http://localhost:3000"
    )
    
    foreach ($url in $urls) {
        try {
            $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -ErrorAction Stop
            Write-Host "✅ $url - يعمل (Status: $($response.StatusCode))" -ForegroundColor Green
        } catch {
            Write-Host "❌ $url - لا يعمل" -ForegroundColor Red
        }
    }
}

function Stop-AllServers {
    Write-Host ""
    Write-Host "⛔ إيقاف جميع الخوادم..." -ForegroundColor Red
    
    # إيقاف PM2
    Write-Host "🔄 إيقاف PM2..." -ForegroundColor Yellow
    try {
        & pm2 stop all 2>$null
        & pm2 delete all 2>$null
        & pm2 kill 2>$null
        Write-Host "✅ تم إيقاف PM2" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ PM2 غير متاح" -ForegroundColor Yellow
    }
    
    # إيقاف عمليات Node.js
    Write-Host "🔄 إيقاف عمليات Node.js..." -ForegroundColor Yellow
    try {
        $nodeProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
        
        if ($nodeProcesses) {
            $nodeProcesses | Stop-Process -Force
            Write-Host "✅ تم إيقاف $($nodeProcesses.Count) عملية Node.js" -ForegroundColor Green
        } else {
            Write-Host "ℹ️ لا توجد عمليات Node.js للإيقاف" -ForegroundColor Blue
        }
    } catch {
        Write-Host "❌ خطأ في إيقاف عمليات Node.js: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # التحقق النهائي
    Start-Sleep -Seconds 2
    Write-Host ""
    Write-Host "🔍 التحقق النهائي..." -ForegroundColor Yellow
    
    $remainingProcesses = Get-Process -Name "node" -ErrorAction SilentlyContinue
    if ($remainingProcesses) {
        Write-Host "⚠️ لا تزال هناك $($remainingProcesses.Count) عملية Node.js تعمل" -ForegroundColor Yellow
        $remainingProcesses | Format-Table Id, ProcessName -AutoSize
    } else {
        Write-Host "✅ تم إيقاف جميع الخوادم بنجاح" -ForegroundColor Green
    }
}

function Restart-Servers {
    Write-Host ""
    Write-Host "🔄 إعادة تشغيل الخوادم..." -ForegroundColor Blue
    
    # إيقاف الخوادم أولاً
    Stop-AllServers
    
    # انتظار قليل
    Start-Sleep -Seconds 3
    
    # بدء الخادم مع PM2
    Write-Host ""
    Write-Host "🚀 بدء الخادم مع PM2..." -ForegroundColor Green
    
    try {
        Set-Location -Path $PSScriptRoot
        & pm2 start ecosystem.config.js
        Write-Host "✅ تم بدء الخادم مع PM2" -ForegroundColor Green
        
        # عرض الحالة
        Start-Sleep -Seconds 2
        & pm2 status
    } catch {
        Write-Host "❌ فشل في بدء الخادم مع PM2: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 جرب تشغيل: npm run pm2:start" -ForegroundColor Yellow
    }
}

function Show-DetailedStatus {
    Write-Host ""
    Write-Host "📊 حالة مفصلة للنظام..." -ForegroundColor Magenta
    
    Check-NodeProcesses
    Check-PM2Status
    Check-Ports
    Test-ServerConnection
    
    Write-Host ""
    Write-Host "💾 معلومات النظام:" -ForegroundColor Magenta
    Write-Host "  النظام: $($env:OS)" -ForegroundColor White
    Write-Host "  المعالج: $($env:PROCESSOR_ARCHITECTURE)" -ForegroundColor White
    Write-Host "  المستخدم: $($env:USERNAME)" -ForegroundColor White
    Write-Host "  المجلد الحالي: $(Get-Location)" -ForegroundColor White
}

# تنفيذ الأمر المطلوب
switch ($Action.ToLower()) {
    "check" {
        Check-NodeProcesses
        Check-PM2Status
        Check-Ports
    }
    "stop" {
        Stop-AllServers
    }
    "restart" {
        Restart-Servers
    }
    "status" {
        Show-DetailedStatus
    }
    "help" {
        Show-Help
    }
    default {
        Show-Help
    }
}

Write-Host ""
Write-Host "✅ انتهى تنفيذ الأمر" -ForegroundColor Green
