import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 5173,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true
      },
      '/auth': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  },
  // base: './', // تم تعطيل المسارات النسبية - استخدام مطلقة
  build: {
    outDir: 'dist',
    sourcemap: false, // إزالة source maps للإنتاج
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          mui: ['@mui/material', '@mui/icons-material'],
          utils: ['axios', 'react-query']
        },
        // إزالة source map references
        sourcemapExcludeSources: true
      }
    },
    chunkSizeWarningLimit: 1000,
    assetsInlineLimit: 4096, // inline الملفات الصغيرة
    // تعطيل source maps بالكامل
    cssCodeSplit: true,
    reportCompressedSize: false
  }
})
