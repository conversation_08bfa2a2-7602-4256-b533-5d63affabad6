const express = require('express');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, checkPermission } = require('../middleware/auth');
const { hashPassword } = require('../utils/encryption');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على جميع الوكلاء
router.get('/', authenticateToken, checkPermission('agents', 'read'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', active } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (search) {
      where.OR = [
        { agentName: { contains: search, mode: 'insensitive' } },
        { agencyName: { contains: search, mode: 'insensitive' } },
        { agencyType: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (active !== undefined) {
      where.isActive = active === 'true';
    }

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          _count: {
            select: { dataRecords: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count({ where })
    ]);

    res.json({
      agents,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get agents error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على وكيل واحد
router.get('/:id', authenticateToken, checkPermission('agents', 'read'), async (req, res) => {
  try {
    const agent = await prisma.agent.findUnique({
      where: { id: parseInt(req.params.id) },
      include: {
        dataRecords: {
          select: {
            id: true,
            operationDate: true,
            operationStatus: true,
            agentReference: true,
            client: {
              select: {
                id: true,
                clientName: true,
                appName: true,
                clientCode: true
              }
            }
          }
        },
        _count: {
          select: { dataRecords: true }
        }
      }
    });

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    res.json(agent);
  } catch (error) {
    console.error('Get agent error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// إضافة وكيل جديد
router.post('/', [
  authenticateToken,
  checkPermission('agents', 'create'),
  body('agentName').notEmpty().withMessage('Agent name is required'),
  body('agencyName').notEmpty().withMessage('Agency name is required'),
  body('agencyType').notEmpty().withMessage('Agency type is required'),
  body('ipAddress').isIP().withMessage('Valid IP address is required'),
  body('loginName').optional().isLength({ min: 8 }).withMessage('اسم الدخول يجب أن يكون 8 أحرف على الأقل')
    .matches(/^[a-zA-Z0-9]+$/).withMessage('اسم الدخول يجب أن يحتوي على حروف وأرقام فقط'),
  body('loginPassword').optional().isLength({ min: 8 }).withMessage('كلمة مرور الدخول يجب أن تكون 8 أحرف على الأقل')
    .matches(/^[a-zA-Z0-9]+$/).withMessage('كلمة مرور الدخول يجب أن تحتوي على حروف وأرقام فقط')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { agentName, agencyName, agencyType, ipAddress, loginName, loginPassword } = req.body;

    // تشفير كلمة مرور الدخول إذا تم توفيرها
    let hashedLoginPassword = null;
    if (loginPassword && loginPassword.trim()) {
      hashedLoginPassword = await hashPassword(loginPassword);
    }

    const agent = await prisma.agent.create({
      data: {
        agentName,
        agencyName,
        agencyType,
        ipAddress,
        loginName: loginName && loginName.trim() ? loginName.trim() : null,
        loginPassword: hashedLoginPassword
      },
      include: {
        _count: {
          select: { dataRecords: true }
        }
      }
    });

    res.status(201).json(agent);
  } catch (error) {
    console.error('Create agent error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// تحديث وكيل
router.put('/:id', [
  authenticateToken,
  checkPermission('agents', 'update'),
  body('agentName').optional().notEmpty(),
  body('agencyName').optional().notEmpty(),
  body('agencyType').optional().notEmpty(),
  body('ipAddress').optional().isIP(),
  body('loginName').optional().isLength({ min: 8 }).withMessage('اسم الدخول يجب أن يكون 8 أحرف على الأقل')
    .matches(/^[a-zA-Z0-9]+$/).withMessage('اسم الدخول يجب أن يحتوي على حروف وأرقام فقط'),
  body('loginPassword').optional().isLength({ min: 8 }).withMessage('كلمة مرور الدخول يجب أن تكون 8 أحرف على الأقل')
    .matches(/^[a-zA-Z0-9]+$/).withMessage('كلمة مرور الدخول يجب أن تحتوي على حروف وأرقام فقط')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const agentId = parseInt(req.params.id);
    const updateData = req.body;

    // التحقق من وجود الوكيل
    const existingAgent = await prisma.agent.findUnique({
      where: { id: agentId }
    });

    if (!existingAgent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    // تشفير كلمة مرور الدخول إذا تم تحديثها
    if (updateData.loginPassword && updateData.loginPassword.trim()) {
      updateData.loginPassword = await hashPassword(updateData.loginPassword);
    } else if (updateData.loginPassword === '') {
      // إذا كانت فارغة، احذفها
      updateData.loginPassword = null;
    }

    const agent = await prisma.agent.update({
      where: { id: agentId },
      data: updateData,
      include: {
        _count: {
          select: { dataRecords: true }
        }
      }
    });

    res.json(agent);
  } catch (error) {
    console.error('Update agent error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// حذف وكيل
router.delete('/:id', authenticateToken, checkPermission('agents', 'delete'), async (req, res) => {
  try {
    const agentId = parseInt(req.params.id);

    const agent = await prisma.agent.findUnique({
      where: { id: agentId },
      include: {
        _count: {
          select: { dataRecords: true }
        }
      }
    });

    if (!agent) {
      return res.status(404).json({ error: 'Agent not found' });
    }

    // التحقق من وجود سجلات بيانات مرتبطة
    if (agent._count.dataRecords > 0) {
      return res.status(400).json({
        error: 'Cannot delete agent with associated data records',
        dataRecordsCount: agent._count.dataRecords
      });
    }

    await prisma.agent.delete({
      where: { id: agentId }
    });

    res.json({ message: 'Agent deleted successfully' });
  } catch (error) {
    console.error('Delete agent error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الموافقة على جهاز وكيل جديد
router.post('/:id/approve-device', authenticateToken, checkPermission('agents', 'update'), async (req, res) => {
  try {
    const { deviceId } = req.body;
    const agentId = parseInt(req.params.id);

    const agent = await prisma.agent.update({
      where: { id: agentId },
      data: { deviceId }
    });

    res.json({ message: 'Agent device approved successfully', agent });
  } catch (error) {
    console.error('Agent device approval error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
