# 🏢 دليل الوكلاء - نظام التحقق من العملاء
# Yemen Client Management System - Agents Guide

========================================
📋 نظرة عامة:
========================================

🎯 الغرض: التحقق من حالة العملاء
🔑 الوصول: الوكلاء المعتمدون فقط
📊 النتيجة: حالة العميل أو رسالة خطأ محددة
🌐 العنوان: http://YOUR_SERVER_IP:8080/api/external/verify-direct

========================================
🔐 متطلبات الوصول:
========================================

✅ حساب وكيل معتمد (اسم مستخدم + كلمة مرور)
✅ بيانات العميل (رمز العميل + توكن العميل)
❌ العملاء لا يملكون حسابات للوصول المباشر

========================================
📡 تفاصيل الـ API:
========================================

عنوان الخادم: http://YOUR_SERVER_IP:8080/api/external/verify-direct
طريقة الطلب: POST
نوع المحتوى: application/json

البيانات المطلوبة:
{
  "agent_login_name": "YOUR_AGENT_USERNAME",
  "agent_login_password": "YOUR_AGENT_PASSWORD",
  "client_code": "CLIENT_CODE",
  "client_token": "CLIENT_TOKEN"
}

========================================
📊 الردود المختلفة:
========================================

1. ✅ نجح التحقق - العميل نشط:
   {"status":"success","client_status":1}

2. ✅ نجح التحقق - العميل غير نشط:
   {"status":"success","client_status":0}

3. ❌ خطأ في بيانات الوكيل:
   {"status":"agent_error"}

4. ❌ خطأ في بيانات العميل:
   {"status":"client_error"}

5. ❌ خطأ في الخادم:
   {"status":"error"}

========================================
💻 أمثلة الكود:
========================================

# ========================================
# 1. كود cURL (سطر الأوامر):
# ========================================

curl -X POST http://YOUR_SERVER_IP:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "YOUR_AGENT_USERNAME",
    "agent_login_password": "YOUR_AGENT_PASSWORD",
    "client_code": "1000",
    "client_token": "ABC12345"
  }'

# ========================================
# 2. كود PHP:
# ========================================

<?php
class AgentVerifier {
    private $serverUrl = 'http://YOUR_SERVER_IP:8080/api/external/verify-direct';
    private $agentUser;
    private $agentPass;
    
    public function __construct($agentUser, $agentPass) {
        $this->agentUser = $agentUser;
        $this->agentPass = $agentPass;
    }
    
    public function verifyClient($clientCode, $clientToken) {
        $data = array(
            'agent_login_name' => $this->agentUser,
            'agent_login_password' => $this->agentPass,
            'client_code' => $clientCode,
            'client_token' => $clientToken
        );
        
        $options = array(
            'http' => array(
                'header' => "Content-type: application/json\r\n",
                'method' => 'POST',
                'content' => json_encode($data),
                'timeout' => 30
            )
        );
        
        $context = stream_context_create($options);
        $result = file_get_contents($this->serverUrl, false, $context);
        
        if ($result === FALSE) {
            return array('status' => 'error');
        }
        
        return json_decode($result, true);
    }
}

// الاستخدام:
$agent = new AgentVerifier('YOUR_AGENT_USERNAME', 'YOUR_AGENT_PASSWORD');
$result = $agent->verifyClient('1000', 'ABC12345');

switch ($result['status']) {
    case 'success':
        if ($result['client_status'] == 1) {
            echo "العميل نشط - يمكن المتابعة";
        } else {
            echo "العميل غير نشط - لا يمكن المتابعة";
        }
        break;
    case 'agent_error':
        echo "خطأ في بيانات الوكيل - تحقق من اسم المستخدم وكلمة المرور";
        break;
    case 'client_error':
        echo "خطأ في بيانات العميل - العميل غير موجود أو التوكن خاطئ";
        break;
    default:
        echo "خطأ في النظام - حاول مرة أخرى";
}
?>

# ========================================
# 3. كود JavaScript:
# ========================================

class AgentVerifier {
    constructor(agentUser, agentPass) {
        this.agentUser = agentUser;
        this.agentPass = agentPass;
        this.serverUrl = 'http://YOUR_SERVER_IP:8080/api/external/verify-direct';
    }
    
    async verifyClient(clientCode, clientToken) {
        try {
            const response = await fetch(this.serverUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    agent_login_name: this.agentUser,
                    agent_login_password: this.agentPass,
                    client_code: clientCode,
                    client_token: clientToken
                })
            });

            const result = await response.json();
            
            switch (result.status) {
                case 'success':
                    return {
                        success: true,
                        clientActive: result.client_status === 1,
                        message: result.client_status === 1 ? 'العميل نشط' : 'العميل غير نشط'
                    };
                case 'agent_error':
                    return {
                        success: false,
                        error: 'خطأ في بيانات الوكيل'
                    };
                case 'client_error':
                    return {
                        success: false,
                        error: 'خطأ في بيانات العميل'
                    };
                default:
                    return {
                        success: false,
                        error: 'خطأ في النظام'
                    };
            }
        } catch (error) {
            return {
                success: false,
                error: 'خطأ في الاتصال'
            };
        }
    }
}

// الاستخدام:
const agent = new AgentVerifier('YOUR_AGENT_USERNAME', 'YOUR_AGENT_PASSWORD');

const result = await agent.verifyClient('1000', 'ABC12345');

if (result.success) {
    console.log(result.message);
    if (result.clientActive) {
        // العميل نشط - تابع العملية
    } else {
        // العميل غير نشط - أوقف العملية
    }
} else {
    console.log('خطأ:', result.error);
}

# ========================================
# 4. كود Python:
# ========================================

import requests
import json

class AgentVerifier:
    def __init__(self, agent_user, agent_pass):
        self.agent_user = agent_user
        self.agent_pass = agent_pass
        self.server_url = 'http://YOUR_SERVER_IP:8080/api/external/verify-direct'
    
    def verify_client(self, client_code, client_token):
        data = {
            'agent_login_name': self.agent_user,
            'agent_login_password': self.agent_pass,
            'client_code': client_code,
            'client_token': client_token
        }
        
        try:
            response = requests.post(self.server_url, json=data, timeout=30)
            result = response.json()
            
            if result['status'] == 'success':
                return {
                    'success': True,
                    'client_active': result['client_status'] == 1,
                    'message': 'العميل نشط' if result['client_status'] == 1 else 'العميل غير نشط'
                }
            elif result['status'] == 'agent_error':
                return {
                    'success': False,
                    'error': 'خطأ في بيانات الوكيل'
                }
            elif result['status'] == 'client_error':
                return {
                    'success': False,
                    'error': 'خطأ في بيانات العميل'
                }
            else:
                return {
                    'success': False,
                    'error': 'خطأ في النظام'
                }
        except:
            return {
                'success': False,
                'error': 'خطأ في الاتصال'
            }

# الاستخدام:
agent = AgentVerifier('YOUR_AGENT_USERNAME', 'YOUR_AGENT_PASSWORD')
result = agent.verify_client('1000', 'ABC12345')

if result['success']:
    print(result['message'])
    if result['client_active']:
        print('يمكن المتابعة مع العميل')
    else:
        print('لا يمكن المتابعة - العميل غير نشط')
else:
    print(f'خطأ: {result["error"]}')

# ========================================
# 5. كود C#:
# ========================================

using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class AgentVerifier
{
    private static readonly HttpClient client = new HttpClient();
    private readonly string agentUser;
    private readonly string agentPass;
    private readonly string serverUrl = "http://YOUR_SERVER_IP:8080/api/external/verify-direct";
    
    public AgentVerifier(string agentUser, string agentPass)
    {
        this.agentUser = agentUser;
        this.agentPass = agentPass;
    }
    
    public async Task<dynamic> VerifyClient(string clientCode, string clientToken)
    {
        try
        {
            var data = new {
                agent_login_name = agentUser,
                agent_login_password = agentPass,
                client_code = clientCode,
                client_token = clientToken
            };

            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(serverUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            var parsed = JsonConvert.DeserializeObject<dynamic>(result);

            switch ((string)parsed.status)
            {
                case "success":
                    return new {
                        success = true,
                        clientActive = (int)parsed.client_status == 1,
                        message = (int)parsed.client_status == 1 ? "العميل نشط" : "العميل غير نشط"
                    };
                case "agent_error":
                    return new {
                        success = false,
                        error = "خطأ في بيانات الوكيل"
                    };
                case "client_error":
                    return new {
                        success = false,
                        error = "خطأ في بيانات العميل"
                    };
                default:
                    return new {
                        success = false,
                        error = "خطأ في النظام"
                    };
            }
        }
        catch
        {
            return new {
                success = false,
                error = "خطأ في الاتصال"
            };
        }
    }
}

// الاستخدام:
var agent = new AgentVerifier("YOUR_AGENT_USERNAME", "YOUR_AGENT_PASSWORD");
var result = await agent.VerifyClient("1000", "ABC12345");

if (result.success)
{
    Console.WriteLine(result.message);
    if (result.clientActive)
    {
        Console.WriteLine("يمكن المتابعة مع العميل");
    }
    else
    {
        Console.WriteLine("لا يمكن المتابعة - العميل غير نشط");
    }
}
else
{
    Console.WriteLine($"خطأ: {result.error}");
}

========================================
🔧 معالجة الأخطاء:
========================================

1. agent_error:
   - سبب: اسم المستخدم أو كلمة المرور خاطئة
   - الحل: تحقق من بيانات الوكيل

2. client_error:
   - سبب: العميل غير موجود أو التوكن خاطئ
   - الحل: تحقق من رمز العميل والتوكن

3. error:
   - سبب: خطأ في الخادم
   - الحل: حاول مرة أخرى أو اتصل بالدعم الفني

4. خطأ في الاتصال:
   - سبب: مشكلة في الشبكة أو الخادم غير متاح
   - الحل: تحقق من الاتصال وعنوان الخادم

========================================
🛡️ نصائح الأمان:
========================================

✅ احفظ بيانات الوكيل بشكل آمن
✅ لا تشارك كلمة المرور مع أحد
✅ استخدم HTTPS في الإنتاج
✅ تحقق من النتائج قبل اتخاذ إجراءات
✅ اعتمد على معالجة الأخطاء الشاملة

========================================
📞 الدعم الفني:
========================================

في حالة وجود مشاكل:
1. تحقق من عنوان الخادم
2. تأكد من صحة بيانات الوكيل
3. تحقق من اتصال الإنترنت
4. راجع رسائل الخطأ
5. اتصل بالدعم الفني إذا استمرت المشكلة

========================================
🎯 ملخص سريع:
========================================

🔹 عنوان: http://YOUR_SERVER_IP:8080/api/external/verify-direct
🔹 طريقة: POST
🔹 البيانات: بيانات الوكيل + بيانات العميل
🔹 النتائج: success (مع حالة العميل) أو رسالة خطأ محددة
🔹 الأمان: الوكلاء فقط - العملاء محميون

🎉 بسيط وآمن ومحدد!
