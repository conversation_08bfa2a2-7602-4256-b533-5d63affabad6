# كود وكيل الغراسي للاستخدام من خارج السيرفر
# Yemen Client Management System - External Agent Code

========================================
🚀 الطريقة الجديدة: طلب واحد فقط!
========================================

# ✅ للاستخدام من خارج السيرفر (الطريقة المفضلة)
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "agent001",
    "agent_login_password": "agent123",
    "client_code": "1000",
    "client_token": "ABC12345"
  }'

========================================
📊 النتيجة المتوقعة عند النجاح:
========================================

{
  "status": "success",
  "message": "Client verified successfully through direct verification",
  "data": {
    "verification_result": {
      "agent_verified": true,
      "client_verified": true,
      "token_verified": true
    },
    "agent_info": {
      "agent_id": 3,
      "agent_name": "وكيل اختبار",
      "agency_type": "وكيل يمن موبايل"
    },
    "client_info": {
      "client_code": 1000,
      "client_name": "محمد علي الحاشدي",
      "app_name": "تطبيق العميل",
      "status": 1,
      "status_text": "نشط",
      "ip_address": "*************",
      "created_date": "2025-06-30",
      "created_by_user": "admin"
    },
    "operation_info": {
      "timestamp": "2025-06-30T20:45:00.000Z",
      "ip_address": "************",
      "verification_method": "direct"
    }
  }
}

========================================
❌ النتائج المتوقعة عند الفشل:
========================================

# خطأ في بيانات الوكيل:
{
  "status": "error",
  "message": "Invalid agent credentials",
  "error_code": "AGENT_AUTH_FAILED"
}

# العميل غير موجود:
{
  "status": "error",
  "message": "Client not found",
  "error_code": "CLIENT_NOT_FOUND",
  "agent_info": {
    "agent_id": 3,
    "agent_name": "وكيل اختبار",
    "agency_type": "وكيل يمن موبايل"
  }
}

# التوكن غير مطابق:
{
  "status": "error",
  "message": "Invalid client token",
  "error_code": "TOKEN_MISMATCH",
  "agent_info": {
    "agent_id": 3,
    "agent_name": "وكيل اختبار",
    "agency_type": "وكيل يمن موبايل"
  },
  "client_info": {
    "client_code": 1000,
    "client_found": true,
    "token_match": false
  }
}

========================================
💻 أمثلة بلغات البرمجة المختلفة:
========================================

# JavaScript (Node.js أو المتصفح):
const response = await fetch('http://***********:8080/api/external/verify-direct', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: '1000',
        client_token: 'ABC12345'
    })
});

const result = await response.json();
console.log(result);

# Python:
import requests

data = {
    'agent_login_name': 'agent001',
    'agent_login_password': 'agent123',
    'client_code': '1000',
    'client_token': 'ABC12345'
}

response = requests.post(
    'http://***********:8080/api/external/verify-direct',
    json=data
)

result = response.json()
print(result)

# PHP:
<?php
$data = [
    'agent_login_name' => 'agent001',
    'agent_login_password' => 'agent123',
    'client_code' => '1000',
    'client_token' => 'ABC12345'
];

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents(
    'http://***********:8080/api/external/verify-direct',
    false,
    $context
);

$response = json_decode($result, true);
print_r($response);
?>

# C# (.NET):
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

var client = new HttpClient();
var data = new {
    agent_login_name = "agent001",
    agent_login_password = "agent123",
    client_code = "1000",
    client_token = "ABC12345"
};

var json = JsonConvert.SerializeObject(data);
var content = new StringContent(json, Encoding.UTF8, "application/json");

var response = await client.PostAsync(
    "http://***********:8080/api/external/verify-direct", 
    content
);

var result = await response.Content.ReadAsStringAsync();
Console.WriteLine(result);

# PowerShell:
$data = @{
    agent_login_name = "agent001"
    agent_login_password = "agent123"
    client_code = "1000"
    client_token = "ABC12345"
} | ConvertTo-Json

$response = Invoke-RestMethod -Uri "http://***********:8080/api/external/verify-direct" -Method POST -ContentType "application/json" -Body $data

$response | ConvertTo-Json -Depth 3

========================================
🔧 معالجة النتائج:
========================================

# JavaScript - معالجة شاملة:
async function verifyClient(agentLogin, agentPassword, clientCode, clientToken) {
    try {
        const response = await fetch('http://***********:8080/api/external/verify-direct', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                agent_login_name: agentLogin,
                agent_login_password: agentPassword,
                client_code: clientCode,
                client_token: clientToken
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            console.log('✅ تم التحقق بنجاح!');
            console.log('الوكيل:', result.data.agent_info.agent_name);
            console.log('العميل:', result.data.client_info.client_name);
            console.log('حالة العميل:', result.data.client_info.status_text);
            
            return {
                success: true,
                agent: result.data.agent_info,
                client: result.data.client_info,
                verification: result.data.verification_result
            };
        } else {
            console.log('❌ فشل التحقق:', result.message);
            console.log('كود الخطأ:', result.error_code);
            
            return {
                success: false,
                error: result.message,
                errorCode: result.error_code,
                agentInfo: result.agent_info || null,
                clientInfo: result.client_info || null
            };
        }
    } catch (error) {
        console.error('❌ خطأ في الاتصال:', error.message);
        return {
            success: false,
            error: 'خطأ في الاتصال: ' + error.message
        };
    }
}

# استخدام الدالة:
const result = await verifyClient('agent001', 'agent123', '1000', 'ABC12345');

if (result.success) {
    console.log('العميل نشط:', result.client.status === 1);
    console.log('اسم العميل:', result.client.client_name);
} else {
    console.log('سبب الفشل:', result.error);
}

========================================
🛡️ نصائح الأمان:
========================================

1. ✅ استخدم HTTPS في الإنتاج بدلاً من HTTP
2. ✅ لا تخزن كلمات المرور في الكود
3. ✅ استخدم متغيرات البيئة للبيانات الحساسة
4. ✅ تحقق من صحة البيانات قبل الإرسال
5. ✅ اعتمد على معالجة الأخطاء الشاملة

========================================
📞 معلومات الاتصال:
========================================

عنوان الخادم الخارجي: http://***********:8080
عنوان الخادم المحلي: http://localhost:8080
نقطة النهاية: /api/external/verify-direct
طريقة الطلب: POST
نوع المحتوى: application/json

========================================
🎯 الخلاصة:
========================================

الآن يمكنك كوكيل الغراسي:
✅ إرسال طلب واحد فقط بدلاً من طلبين
✅ التحقق من العميل مباشرة
✅ الحصول على معلومات شاملة
✅ معرفة سبب الفشل بالتفصيل
✅ استخدام الكود من أي لغة برمجة

🎉 سهل وسريع وآمن!
