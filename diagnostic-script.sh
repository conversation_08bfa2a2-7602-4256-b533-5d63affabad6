#!/bin/bash

# YemClient Diagnostic Script for Linux/Mac
# Usage: chmod +x diagnostic-script.sh && ./diagnostic-script.sh

# Configuration
SERVER_URL="${1:-http://***********:8080}"
TEST_USER="${2:-admin}"
TEST_PASSWORD="${3:-admin123}"
TEST_CLIENT="${4:-1001}"
TEST_CLIENT_PASSWORD="${5:-123456}"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Icons
CHECK="✅"
CROSS="❌"
WARNING="⚠️"
INFO="ℹ️"

echo -e "${CYAN}🔍 YemClient Diagnostic Script${NC}"
echo -e "${CYAN}================================${NC}"
echo -e "${YELLOW}Server URL: $SERVER_URL${NC}"
echo ""

# Function to make HTTP requests
make_request() {
    local url="$1"
    local method="${2:-GET}"
    local data="$3"
    local headers="$4"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" \
             -H "Content-Type: application/json" \
             ${headers:+-H "$headers"} \
             -d "$data" \
             "$url"
    else
        curl -s -X "$method" \
             ${headers:+-H "$headers"} \
             "$url"
    fi
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required tools
echo -e "${GREEN}🛠️ Checking Required Tools${NC}"
echo "----------------------------"

if command_exists curl; then
    echo -e "$CHECK curl is available"
else
    echo -e "$CROSS curl is not installed. Please install curl first."
    exit 1
fi

if command_exists jq; then
    echo -e "$CHECK jq is available"
    HAS_JQ=true
else
    echo -e "$WARNING jq is not installed. JSON parsing will be limited."
    HAS_JQ=false
fi

echo ""

# Test 1: Basic Connectivity
echo -e "${GREEN}📡 Test 1: Basic Connectivity${NC}"
echo "------------------------------"

if command_exists nc; then
    if nc -z *********** 8080 2>/dev/null; then
        echo -e "$CHECK Network connection successful"
    else
        echo -e "$CROSS Network connection failed"
        echo -e "${YELLOW}   Check firewall and network settings${NC}"
    fi
elif command_exists telnet; then
    if timeout 5 telnet *********** 8080 </dev/null 2>/dev/null | grep -q "Connected"; then
        echo -e "$CHECK Network connection successful"
    else
        echo -e "$CROSS Network connection failed"
    fi
else
    echo -e "$WARNING Cannot test network connectivity (nc/telnet not available)"
fi

# Test 2: Server Health
echo ""
echo -e "${GREEN}🏥 Test 2: Server Health${NC}"
echo "------------------------"

health_response=$(make_request "$SERVER_URL/health")
health_status=$?

if [ $health_status -eq 0 ] && [ -n "$health_response" ]; then
    echo -e "$CHECK Server health check passed"
    
    if [ "$HAS_JQ" = true ]; then
        status=$(echo "$health_response" | jq -r '.status // "unknown"')
        uptime=$(echo "$health_response" | jq -r '.uptime // 0')
        uptime_minutes=$(echo "scale=2; $uptime / 60" | bc 2>/dev/null || echo "unknown")
        
        echo -e "${CYAN}   Status: $status${NC}"
        echo -e "${CYAN}   Uptime: $uptime_minutes minutes${NC}"
    else
        echo -e "${CYAN}   Response: $health_response${NC}"
    fi
else
    echo -e "$CROSS Server health check failed"
    echo -e "${YELLOW}   Server may be down or unreachable${NC}"
fi

# Test 3: System Info
echo ""
echo -e "${GREEN}💻 Test 3: System Information${NC}"
echo "------------------------------"

system_response=$(make_request "$SERVER_URL/api/debug/system-info")
system_status=$?

if [ $system_status -eq 0 ] && [ -n "$system_response" ]; then
    echo -e "$CHECK System info retrieved"
    
    if [ "$HAS_JQ" = true ]; then
        node_version=$(echo "$system_response" | jq -r '.systemInfo.server.nodeVersion // "unknown"')
        platform=$(echo "$system_response" | jq -r '.systemInfo.server.platform // "unknown"')
        environment=$(echo "$system_response" | jq -r '.systemInfo.server.environment // "unknown"')
        memory_used=$(echo "$system_response" | jq -r '.systemInfo.server.memory.used // 0')
        memory_mb=$(echo "scale=2; $memory_used / 1024 / 1024" | bc 2>/dev/null || echo "unknown")
        
        echo -e "${CYAN}   Node.js: $node_version${NC}"
        echo -e "${CYAN}   Platform: $platform${NC}"
        echo -e "${CYAN}   Environment: $environment${NC}"
        echo -e "${CYAN}   Memory Used: $memory_mb MB${NC}"
    else
        echo -e "${CYAN}   Response: $system_response${NC}"
    fi
else
    echo -e "$CROSS System info failed"
fi

# Test 4: User Login
echo ""
echo -e "${GREEN}👤 Test 4: User Login${NC}"
echo "----------------------"

device_id="diagnostic-device-$(date +%Y%m%d%H%M%S)"
user_login_data="{
    \"loginName\": \"$TEST_USER\",
    \"password\": \"$TEST_PASSWORD\",
    \"deviceId\": \"$device_id\",
    \"userType\": \"user\"
}"

user_login_response=$(make_request "$SERVER_URL/api/auth/login" "POST" "$user_login_data")
user_login_status=$?

if [ $user_login_status -eq 0 ] && [ -n "$user_login_response" ]; then
    if [ "$HAS_JQ" = true ]; then
        success=$(echo "$user_login_response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            echo -e "$CHECK User login successful"
            username=$(echo "$user_login_response" | jq -r '.user.username // "unknown"')
            account_type=$(echo "$user_login_response" | jq -r '.user.accountType // "unknown"')
            token=$(echo "$user_login_response" | jq -r '.token // null')
            
            echo -e "${CYAN}   User: $username${NC}"
            echo -e "${CYAN}   Account Type: $account_type${NC}"
            echo -e "${CYAN}   Token: $([ "$token" != "null" ] && echo "Present" || echo "Missing")${NC}"
            
            # Test session validation
            if [ "$token" != "null" ]; then
                echo ""
                echo -e "${GREEN}🔐 Test 4.1: Session Validation${NC}"
                
                validate_response=$(make_request "$SERVER_URL/api/auth/validate" "GET" "" "Authorization: Bearer $token")
                
                if [ "$HAS_JQ" = true ]; then
                    validate_success=$(echo "$validate_response" | jq -r '.success // false')
                    if [ "$validate_success" = "true" ]; then
                        echo -e "$CHECK Session validation successful"
                    else
                        echo -e "$CROSS Session validation failed"
                        error_msg=$(echo "$validate_response" | jq -r '.message // "unknown error"')
                        echo -e "${YELLOW}   Error: $error_msg${NC}"
                    fi
                fi
            fi
        else
            echo -e "$CROSS User login failed"
            error_msg=$(echo "$user_login_response" | jq -r '.message // "unknown error"')
            echo -e "${YELLOW}   Error: $error_msg${NC}"
        fi
    else
        echo -e "${CYAN}   Response: $user_login_response${NC}"
    fi
else
    echo -e "$CROSS User login request failed"
fi

# Test 5: Client Login
echo ""
echo -e "${GREEN}🏢 Test 5: Client Login${NC}"
echo "------------------------"

client_login_data="{
    \"loginName\": \"$TEST_CLIENT\",
    \"password\": \"$TEST_CLIENT_PASSWORD\",
    \"deviceId\": \"$device_id\",
    \"userType\": \"client\"
}"

client_login_response=$(make_request "$SERVER_URL/api/auth/login" "POST" "$client_login_data")
client_login_status=$?

if [ $client_login_status -eq 0 ] && [ -n "$client_login_response" ]; then
    if [ "$HAS_JQ" = true ]; then
        success=$(echo "$client_login_response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            echo -e "$CHECK Client login successful"
            client_name=$(echo "$client_login_response" | jq -r '.user.username // "unknown"')
            client_code=$(echo "$client_login_response" | jq -r '.user.clientCode // "unknown"')
            account_type=$(echo "$client_login_response" | jq -r '.user.accountType // "unknown"')
            
            echo -e "${CYAN}   Client: $client_name${NC}"
            echo -e "${CYAN}   Client Code: $client_code${NC}"
            echo -e "${CYAN}   Account Type: $account_type${NC}"
        else
            echo -e "$CROSS Client login failed"
            error_msg=$(echo "$client_login_response" | jq -r '.message // "unknown error"')
            echo -e "${YELLOW}   Error: $error_msg${NC}"
            
            # Try alternative passwords
            echo ""
            echo -e "${YELLOW}🔄 Trying alternative client passwords...${NC}"
            
            for alt_password in "client123" "$TEST_CLIENT" "password" "123"; do
                alt_data="{
                    \"loginName\": \"$TEST_CLIENT\",
                    \"password\": \"$alt_password\",
                    \"deviceId\": \"$device_id\",
                    \"userType\": \"client\"
                }"
                
                alt_response=$(make_request "$SERVER_URL/api/auth/login" "POST" "$alt_data")
                alt_success=$(echo "$alt_response" | jq -r '.success // false' 2>/dev/null)
                
                if [ "$alt_success" = "true" ]; then
                    echo -e "$CHECK Client login successful with password: $alt_password"
                    break
                fi
            done
        fi
    else
        echo -e "${CYAN}   Response: $client_login_response${NC}"
    fi
else
    echo -e "$CROSS Client login request failed"
fi

# Test 6: Login Attempts
echo ""
echo -e "${GREEN}📋 Test 6: Recent Login Attempts${NC}"
echo "---------------------------------"

attempts_response=$(make_request "$SERVER_URL/api/debug/login-attempts?limit=5")
attempts_status=$?

if [ $attempts_status -eq 0 ] && [ -n "$attempts_response" ]; then
    if [ "$HAS_JQ" = true ]; then
        success=$(echo "$attempts_response" | jq -r '.success // false')
        if [ "$success" = "true" ]; then
            echo -e "$CHECK Login attempts retrieved"
            
            attempts_count=$(echo "$attempts_response" | jq '.attempts | length')
            if [ "$attempts_count" -gt 0 ]; then
                echo -e "${CYAN}   Recent attempts:${NC}"
                echo "$attempts_response" | jq -r '.attempts[0:5][] | 
                    (if .success then "✅" else "❌" end) + " " + 
                    (.timestamp | strptime("%Y-%m-%dT%H:%M:%S") | strftime("%H:%M:%S")) + " - " + 
                    .user + " (" + .userType + ") from " + .ipAddress' 2>/dev/null | 
                while read -r line; do
                    echo -e "${CYAN}   $line${NC}"
                done
            else
                echo -e "${YELLOW}   No recent attempts found${NC}"
            fi
        else
            echo -e "$CROSS Failed to retrieve login attempts"
        fi
    else
        echo -e "${CYAN}   Response: $attempts_response${NC}"
    fi
else
    echo -e "$CROSS Login attempts request failed"
fi

# Test 7: CORS Check
echo ""
echo -e "${GREEN}🌐 Test 7: CORS Configuration${NC}"
echo "------------------------------"

cors_response=$(curl -s -I -X OPTIONS \
    -H "Origin: http://localhost:3000" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: Content-Type,Authorization" \
    "$SERVER_URL/api/auth/login")

if echo "$cors_response" | grep -qi "access-control-allow-origin"; then
    echo -e "$CHECK CORS configured correctly"
    allowed_origin=$(echo "$cors_response" | grep -i "access-control-allow-origin" | cut -d' ' -f2- | tr -d '\r')
    echo -e "${CYAN}   Allowed Origin: $allowed_origin${NC}"
else
    echo -e "$WARNING CORS headers not found"
fi

# Summary
echo ""
echo -e "${MAGENTA}📊 Diagnostic Summary${NC}"
echo -e "${MAGENTA}=====================${NC}"

issues=()
[ $health_status -ne 0 ] && issues+=("Server health")
[ "$user_login_status" -ne 0 ] && issues+=("User login")
[ "$client_login_status" -ne 0 ] && issues+=("Client login")

if [ ${#issues[@]} -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! System is working correctly.${NC}"
else
    echo -e "${YELLOW}$WARNING Issues detected:${NC}"
    for issue in "${issues[@]}"; do
        echo -e "${RED}   - $issue${NC}"
    done
    
    echo ""
    echo -e "${CYAN}💡 Recommendations:${NC}"
    echo -e "   1. Check server logs: pm2 logs yemclient-server"
    echo -e "   2. Verify database connection"
    echo -e "   3. Check firewall settings for port 8080"
    echo -e "   4. Review TROUBLESHOOTING.md for detailed solutions"
fi

echo ""
echo -e "${CYAN}🔗 Useful URLs:${NC}"
echo -e "   Health Check: $SERVER_URL/health"
echo -e "   Test Page: Open test-login.html in browser"
echo -e "   Debug API: $SERVER_URL/api/debug/system-info"

echo ""
echo -e "${NC}Diagnostic completed at $(date)${NC}"
