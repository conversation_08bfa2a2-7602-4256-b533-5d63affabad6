const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function migrateToDevice1() {
  try {
    console.log('🔄 ترحيل النظام إلى عمود device1')
    console.log('================================')

    // الخطوة 1: إضافة عمود device1 جديد
    console.log('📝 الخطوة 1: إضافة عمود device1...')

    try {
      await prisma.$executeRaw`ALTER TABLE "users" ADD COLUMN "device1" TEXT;`
      console.log('✅ تم إضافة عمود device1 بنجاح')
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  عمود device1 موجود بالفعل')
      } else {
        throw error
      }
    }

    // الخطوة 2: نقل البيانات من deviceId إلى device1
    console.log('📝 الخطوة 2: نقل البيانات من deviceId إلى device1...')

    const users = await prisma.user.findMany({
      where: {
        deviceId: { not: null }
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true
      }
    })

    console.log(`📊 تم العثور على ${users.length} مستخدم لديهم أجهزة`)

    for (const user of users) {
      let primaryDevice = user.deviceId

      // إذا كان هناك أجهزة متعددة، خذ الأول فقط
      if (user.deviceId && user.deviceId.includes(',')) {
        primaryDevice = user.deviceId.split(',')[0].trim()
        console.log(`👤 ${user.username}: تحويل من أجهزة متعددة "${user.deviceId}" إلى جهاز واحد "${primaryDevice}"`)
      } else {
        console.log(`👤 ${user.username}: نقل الجهاز "${primaryDevice}"`)
      }

      // تحديث العمود الجديد
      await prisma.user.update({
        where: { id: user.id },
        data: { device1: primaryDevice }
      })
    }

    console.log('✅ تم نقل جميع البيانات بنجاح')

    // الخطوة 3: التحقق من النتائج
    console.log('📝 الخطوة 3: التحقق من النتائج...')

    const updatedUsers = await prisma.user.findMany({
      where: {
        device1: { not: null }
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        device1: true
      }
    })

    console.log('')
    console.log('📊 النتائج بعد الترحيل:')
    console.log('========================')

    updatedUsers.forEach(user => {
      console.log(`👤 ${user.username}:`)
      console.log(`   - deviceId (قديم): ${user.deviceId || 'فارغ'}`)
      console.log(`   - device1 (جديد): ${user.device1 || 'فارغ'}`)
      console.log('')
    })

    // الخطوة 4: اختبار تسجيل الدخول مع العمود الجديد
    console.log('📝 الخطوة 4: اختبار تسجيل الدخول...')

    const hashdiUser = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        device1: true
      }
    })

    if (hashdiUser && hashdiUser.device1) {
      console.log(`🧪 اختبار تسجيل الدخول للمستخدم ${hashdiUser.username}`)
      console.log(`📱 الجهاز: ${hashdiUser.device1}`)

      // ملاحظة: سنحتاج لتحديث الكود أولاً قبل اختبار تسجيل الدخول
      console.log('⚠️  ملاحظة: يجب تحديث الكود ليستخدم device1 قبل اختبار تسجيل الدخول')
    }

    console.log('')
    console.log('🎉 تم الترحيل بنجاح!')
    console.log('📝 الخطوات التالية:')
    console.log('   1. تحديث الكود ليستخدم device1 بدلاً من deviceId')
    console.log('   2. اختبار تسجيل الدخول')
    console.log('   3. حذف عمود deviceId القديم (اختياري)')

  } catch (error) {
    console.error('❌ خطأ في الترحيل:', error)
  } finally {
    await prisma.$disconnect()
  }
}

migrateToDevice1()
