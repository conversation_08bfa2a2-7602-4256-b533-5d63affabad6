{"name": "yem-client-server", "version": "2.0.0", "description": "Production-ready backend server for YemClient Management System", "main": "production-server.js", "scripts": {"dev": "nodemon database-only-server.js", "start": "node production-server.js", "prod": "NODE_ENV=production node production-server.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:reload": "pm2 reload ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:logs": "pm2 logs yemclient-server", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "pm2:startup": "pm2 startup", "pm2:save": "pm2 save", "db:generate": "npx prisma generate", "db:migrate": "npx prisma migrate dev", "db:studio": "npx prisma studio", "db:seed": "node prisma/seed.js", "logs:clear": "rm -rf logs/*.log", "test": "node server/basic-test.js", "health": "curl http://localhost:8080/health"}, "dependencies": {"@prisma/client": "^5.6.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.11.3", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2", "prisma": "^5.6.0", "pm2": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["nodejs", "express", "prisma", "postgresql", "pm2", "production", "yemclient"], "author": "YemClient Team", "license": "MIT"}