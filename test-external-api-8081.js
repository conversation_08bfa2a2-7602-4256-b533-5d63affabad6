/**
 * اختبار خادم API الخارجي على منفذ 8081
 */

async function testExternalAPI8081() {
  console.log('🔧 اختبار خادم API الخارجي على منفذ 8081...\n');

  try {
    // اختبار 1: Health Check
    console.log('1️⃣ اختبار Health Check:');
    const healthResponse = await fetch('http://localhost:8081/health');
    console.log(`   📡 Status: ${healthResponse.status}`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('   ✅ Health Check يعمل!');
      console.log(`   📊 Service: ${healthData.service}`);
      console.log(`   📡 Port: ${healthData.port}`);
      console.log(`   💾 Database: ${healthData.database}`);
    } else {
      console.log('   ❌ Health Check فشل!');
    }
    console.log('');

    // اختبار 2: Root Endpoint
    console.log('2️⃣ اختبار Root Endpoint:');
    const rootResponse = await fetch('http://localhost:8081/');
    console.log(`   📡 Status: ${rootResponse.status}`);
    
    if (rootResponse.ok) {
      const rootData = await rootResponse.json();
      console.log('   ✅ Root Endpoint يعمل!');
      console.log(`   📊 Service: ${rootData.service}`);
      console.log(`   📡 Port: ${rootData.port}`);
      console.log(`   📋 Endpoints: ${rootData.endpoints?.length} متاح`);
    } else {
      console.log('   ❌ Root Endpoint فشل!');
    }
    console.log('');

    // اختبار 3: External Health Check
    console.log('3️⃣ اختبار External Health Check:');
    const extHealthResponse = await fetch('http://localhost:8081/api/external/health');
    console.log(`   📡 Status: ${extHealthResponse.status}`);
    
    if (extHealthResponse.ok) {
      const extHealthData = await extHealthResponse.json();
      console.log('   ✅ External Health Check يعمل!');
      console.log(`   📊 Status: ${extHealthData.status}`);
      console.log(`   🔢 Version: ${extHealthData.data?.version}`);
      console.log(`   📡 Port: ${extHealthData.data?.port}`);
    } else {
      console.log('   ❌ External Health Check فشل!');
    }
    console.log('');

    // اختبار 4: Agent Authentication
    console.log('4️⃣ اختبار Agent Authentication:');
    const agentAuthResponse = await fetch('http://localhost:8081/api/external/agent/auth', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        login_name: 'agent001',
        login_password: 'agent123'
      })
    });

    console.log(`   📡 Status: ${agentAuthResponse.status}`);
    
    if (agentAuthResponse.ok) {
      const agentAuthData = await agentAuthResponse.json();
      console.log('   ✅ Agent Authentication يعمل!');
      console.log(`   📊 Status: ${agentAuthData.status}`);
      console.log(`   👤 Agent: ${agentAuthData.agent_name}`);
    } else {
      const errorData = await agentAuthResponse.json();
      console.log('   ❌ Agent Authentication فشل!');
      console.log(`   📝 Error: ${errorData.status}`);
    }
    console.log('');

    // اختبار 5: Client Verification
    console.log('5️⃣ اختبار Client Verification:');
    const clientVerifyResponse = await fetch('http://localhost:8081/api/external/client/verify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      })
    });

    console.log(`   📡 Status: ${clientVerifyResponse.status}`);
    
    if (clientVerifyResponse.ok) {
      const clientVerifyData = await clientVerifyResponse.json();
      console.log('   ✅ Client Verification يعمل!');
      console.log(`   📊 Status: ${clientVerifyData.status}`);
      console.log(`   👤 Client Status: ${clientVerifyData.client_status}`);
    } else {
      const errorData = await clientVerifyResponse.json();
      console.log('   ❌ Client Verification فشل!');
      console.log(`   📝 Error: ${errorData.status}`);
    }
    console.log('');

    // اختبار 6: Direct Verification (Combined)
    console.log('6️⃣ اختبار Direct Verification:');
    const directVerifyResponse = await fetch('http://localhost:8081/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      })
    });

    console.log(`   📡 Status: ${directVerifyResponse.status}`);
    
    if (directVerifyResponse.ok) {
      const directVerifyData = await directVerifyResponse.json();
      console.log('   ✅ Direct Verification يعمل!');
      console.log(`   📊 Status: ${directVerifyData.status}`);
      console.log(`   👤 Client Status: ${directVerifyData.client_status}`);
    } else {
      const errorData = await directVerifyResponse.json();
      console.log('   ❌ Direct Verification فشل!');
      console.log(`   📝 Error: ${errorData.status}`);
    }
    console.log('');

    // اختبار 7: External Stats
    console.log('7️⃣ اختبار External Stats:');
    const statsResponse = await fetch('http://localhost:8081/api/external/stats');
    console.log(`   📡 Status: ${statsResponse.status}`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('   ✅ External Stats يعمل!');
      console.log(`   👥 إجمالي العملاء: ${statsData.data?.totalClients}`);
      console.log(`   ✅ العملاء النشطين: ${statsData.data?.activeClients}`);
      console.log(`   🤝 إجمالي الوكلاء: ${statsData.data?.totalAgents}`);
      console.log(`   ✅ الوكلاء النشطين: ${statsData.data?.activeAgents}`);
    } else {
      console.log('   ❌ External Stats فشل!');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار خادم API الخارجي (منفذ 8081):');
    console.log('✅ Health Check: متاح');
    console.log('✅ Root Endpoint: متاح');
    console.log('✅ External Health Check: متاح');
    console.log('✅ Agent Authentication: متاح');
    console.log('✅ Client Verification: متاح');
    console.log('✅ Direct Verification: متاح');
    console.log('✅ External Stats: متاح');
    console.log('');
    console.log('🎉 خادم API الخارجي يعمل بشكل مثالي على منفذ 8081!');
    console.log('🔧 المطورون يمكنهم استخدام APIs الخارجية!');
    console.log('🌐 متاح على جميع الروابط:');
    console.log('   📍 محلي: http://localhost:8081');
    console.log('   📍 داخلي: http://**************:8081');
    console.log('   📍 خارجي: http://***********:8081');

  } catch (error) {
    console.error('❌ خطأ في اختبار خادم API الخارجي:', error.message);
  }
}

testExternalAPI8081().catch(console.error);
