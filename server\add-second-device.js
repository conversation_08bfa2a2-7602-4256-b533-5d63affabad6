const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function addSecondDevice() {
  try {
    const loginName = 'hash8080'
    
    console.log(`🔧 إضافة جهاز ثاني للمستخدم: ${loginName}`)
    
    // البحث عن المستخدم أولاً
    const user = await prisma.user.findUnique({
      where: { loginName }
    })
    
    if (!user) {
      console.error(`❌ لم يتم العثور على المستخدم: ${loginName}`)
      return
    }
    
    console.log(`👤 تم العثور على المستخدم: ${user.username}`)
    console.log(`📱 الجهاز الحالي: ${user.deviceId}`)
    
    // إضافة جهاز ثاني (سنولد device ID جديد للاختبار)
    const newDeviceId = `device_external_${Math.random().toString(36).substr(2, 9)}_${Date.now()}`
    const updatedDeviceId = user.deviceId ? `${user.deviceId},${newDeviceId}` : newDeviceId
    
    console.log(`📱 الجهاز الجديد: ${newDeviceId}`)
    console.log(`📱 قائمة الأجهزة المحدثة: ${updatedDeviceId}`)
    
    // تحديث قائمة الأجهزة
    await prisma.user.update({
      where: { loginName },
      data: { deviceId: updatedDeviceId }
    })
    
    console.log(`✅ تم إضافة الجهاز الثاني بنجاح للمستخدم: ${user.username}`)
    console.log(`📱 قائمة الأجهزة النهائية: ${updatedDeviceId}`)
    
    // عرض الأجهزة المسموحة
    const allowedDevices = updatedDeviceId.split(',').map(id => id.trim())
    console.log(`📱 الأجهزة المسموحة:`)
    allowedDevices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device}`)
    })
    
  } catch (error) {
    console.error('❌ خطأ في إضافة الجهاز الثاني:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addSecondDevice()
