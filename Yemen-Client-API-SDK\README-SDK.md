# 🇾🇪 نظام إدارة العملاء اليمني - مكتبات الربط
## Yemen Client Management System - SDK Libraries

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/yemclient/api)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-stable-brightgreen.svg)](http://***********:8080/api/external/health)

---

## 🎯 **نظرة عامة**

مكتبات JavaScript للوصول إلى نظام إدارة العملاء اليمني من التطبيقات الخارجية. تدعم Node.js والمتصفحات الحديثة.

### **الميزات الرئيسية:**
- ✅ **مصادقة آمنة** للوكلاء والعملاء
- ✅ **التحقق من بيانات العملاء** في الوقت الفعلي
- ✅ **إحصائيات مفصلة** للعمليات
- ✅ **حماية من الاستخدام المفرط** (Rate Limiting)
- ✅ **دعم Node.js والمتصفحات**
- ✅ **وثائق شاملة** باللغتين العربية والإنجليزية

---

## 📦 **الملفات المتضمنة**

```
📁 Yemen-Client-API-SDK/
├── 📄 YemenClientAPI-Agent.js      # مكتبة الوكلاء
├── 📄 YemenClientAPI-Client.js     # مكتبة العملاء
├── 📄 دليل-المطور-API.md           # دليل المطور العربي
├── 📄 API-Documentation-EN.md      # وثائق API الإنجليزية
├── 📄 API-System-Design.md         # تصميم النظام
├── 📄 test-sdk.js                  # ملف الاختبار
└── 📄 README-SDK.md               # هذا الملف
```

---

## 🚀 **البدء السريع**

### **1. للوكلاء (Agents):**

```javascript
// استيراد المكتبة
const YemenClientAgentAPI = require('./YemenClientAPI-Agent.js')

// إنشاء مثيل جديد
const agent = new YemenClientAgentAPI(
  'http://***********:8080',  // عنوان الخادم
  'your_agent_login',         // اسم تسجيل الدخول
  'your_agent_password'       // كلمة المرور
)

// الاستخدام
async function example() {
  // تسجيل الدخول
  await agent.authenticate()
  
  // التحقق من عميل
  const result = await agent.verifyClient('1000', 'ABC12345')
  console.log('Client:', result.client_name)
  
  // الحصول على الإحصائيات
  const stats = await agent.getStats()
  console.log('Success Rate:', stats.success_rate)
  
  // تسجيل الخروج
  await agent.logout()
}

example().catch(console.error)
```

### **2. للعملاء (Clients):**

```javascript
// استيراد المكتبة
const YemenClientAPI = require('./YemenClientAPI-Client.js')

// إنشاء مثيل جديد
const client = new YemenClientAPI(
  'http://***********:8080',  // عنوان الخادم
  'your_client_code',         // رمز العميل
  'your_client_token'         // توكن العميل
)

// الاستخدام
async function example() {
  // التحقق من العميل عبر وكيل
  const result = await client.verifyThroughAgent(
    'agent_login',     // اسم الوكيل
    'agent_password'   // كلمة مرور الوكيل
  )
  
  if (result.success) {
    console.log('Client verified:', result.client.client_name)
    console.log('Through agent:', result.agent.name)
  } else {
    console.log('Verification failed:', result.error)
  }
}

example().catch(console.error)
```

---

## 🛠️ **التثبيت**

### **Node.js:**
```bash
# تثبيت المتطلبات
npm install node-fetch

# نسخ ملفات المكتبة
cp YemenClientAPI-*.js ./your-project/
```

### **المتصفح:**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Yemen Client API</title>
</head>
<body>
    <!-- تضمين المكتبة -->
    <script src="YemenClientAPI-Agent.js"></script>
    
    <script>
        // الاستخدام مباشرة
        const agent = new YemenClientAgentAPI(
            'http://***********:8080',
            'agent_login',
            'agent_password'
        )
        
        agent.authenticate().then(() => {
            console.log('Agent authenticated!')
        })
    </script>
</body>
</html>
```

---

## 📚 **الوثائق**

### **الوثائق العربية:**
- 📖 **دليل المطور الشامل:** `دليل-المطور-API.md`
- 🏗️ **تصميم النظام:** `API-System-Design.md`

### **English Documentation:**
- 📖 **Complete API Reference:** `API-Documentation-EN.md`
- 🧪 **Testing Guide:** `test-sdk.js`

---

## 🔧 **الوظائف المتاحة**

### **مكتبة الوكلاء (Agent SDK):**

| الوظيفة | الوصف | المعاملات |
|---------|--------|-----------|
| `authenticate()` | تسجيل دخول الوكيل | - |
| `verifyClient(code, token)` | التحقق من عميل | رمز العميل، التوكن |
| `getStats()` | إحصائيات الوكيل | - |
| `getOperations(page, limit)` | سجل العمليات | الصفحة، العدد |
| `logout()` | تسجيل الخروج | - |
| `checkHealth()` | فحص حالة النظام | - |

### **مكتبة العملاء (Client SDK):**

| الوظيفة | الوصف | المعاملات |
|---------|--------|-----------|
| `verifyThroughAgent(login, pass)` | التحقق عبر وكيل | اسم الوكيل، كلمة المرور |
| `checkSystemHealth()` | فحص حالة النظام | - |
| `validateCredentials()` | التحقق من صحة البيانات | - |
| `updateCredentials(code, token)` | تحديث بيانات الاعتماد | الرمز الجديد، التوكن الجديد |

---

## 🧪 **الاختبار**

```bash
# تشغيل الاختبارات الشاملة
node test-sdk.js

# النتيجة المتوقعة:
# ✅ Agent SDK Test: PASSED
# ✅ Client SDK Test: PASSED  
# ✅ Performance Test: PASSED
# 📈 Success Rate: 100.0%
```

---

## 🛡️ **الأمان**

### **أفضل الممارسات:**
- 🔐 **لا تخزن كلمات المرور في الكود**
- 🌍 **استخدم متغيرات البيئة**
- ⏰ **اعتمد على انتهاء صلاحية التوكن التلقائي**
- 📝 **سجل الأخطاء بدون كشف معلومات حساسة**

### **الحدود الأمنية:**
- 🚦 **100 طلب/دقيقة** لكل وكيل
- 🚦 **1000 طلب/ساعة** لكل وكيل
- ⏱️ **انتهاء صلاحية التوكن: 24 ساعة**

---

## 📊 **الأداء**

### **النتائج المقاسة:**
- ⚡ **زمن المصادقة:** ~115ms
- ⚡ **زمن التحقق من العميل:** ~23ms
- ⚡ **زمن الإحصائيات:** ~17ms
- 🎯 **متوسط زمن الاستجابة:** ~52ms
- 🏆 **تقييم الأداء:** ممتاز

---

## 🌐 **عناوين الخادم**

### **الإنتاج:**
```
http://***********:8080
```

### **التطوير المحلي:**
```
http://localhost:8080
```

### **فحص الحالة:**
```
GET /api/external/health
```

---

## 🔗 **أمثلة متقدمة**

### **نظام تحقق متعدد العملاء:**
```javascript
const agent = new YemenClientAgentAPI(/* ... */)
await agent.authenticate()

const clients = [
  { code: '1000', token: 'ABC12345' },
  { code: '1001', token: 'XYZ67890' },
  { code: '1002', token: 'DEF54321' }
]

const results = await Promise.all(
  clients.map(client => 
    agent.verifyClient(client.code, client.token)
  )
)

console.log('Verification results:', results)
await agent.logout()
```

### **مراقبة دورية للعميل:**
```javascript
const client = new YemenClientAPI(/* ... */)

setInterval(async () => {
  const result = await client.verifyThroughAgent('agent001', 'password')
  console.log(`[${new Date().toISOString()}] Status:`, 
    result.success ? 'Active' : 'Inactive')
}, 5 * 60 * 1000) // كل 5 دقائق
```

---

## 📞 **الدعم والمساعدة**

### **التواصل:**
- 📧 **البريد الإلكتروني:** <EMAIL>
- 📱 **الهاتف:** +967-xxx-xxxx
- 🌐 **الموقع:** http://***********:8080
- 📖 **الوثائق:** http://***********:8080/api/docs

### **الإبلاغ عن المشاكل:**
1. تحقق من **حالة النظام** أولاً
2. راجع **دليل المطور** للحلول الشائعة
3. تواصل مع **فريق الدعم** مع تفاصيل المشكلة

---

## 📄 **الترخيص**

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

---

## 🏆 **الإصدارات**

### **v1.0.0** (الحالي)
- ✅ مكتبة الوكلاء كاملة
- ✅ مكتبة العملاء كاملة  
- ✅ نظام أمان متقدم
- ✅ وثائق شاملة
- ✅ اختبارات شاملة

---

## 🎉 **شكر خاص**

شكراً لفريق تطوير نظام إدارة العملاء اليمني على الجهود المبذولة في إنشاء هذا النظام المتقدم.

---

**🚀 ابدأ الآن واستمتع بتجربة برمجية متميزة!**
