<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إصلاح localStorage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        
        .current-data {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 15px 0;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s;
            width: 100%;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }
        
        .clear-btn {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .instructions {
            background: #d1ecf1;
            border: 2px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin: 0 0 15px 0;
            color: #0c5460;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-right: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح مشكلة حالة المستخدم</h1>
            <p>مسح البيانات القديمة من localStorage</p>
        </div>

        <!-- البيانات الحالية -->
        <div class="section">
            <h3>📊 البيانات الحالية في localStorage</h3>
            <div id="currentData" class="current-data">جاري التحقق...</div>
            <button onclick="checkCurrentData()">🔍 فحص البيانات الحالية</button>
        </div>

        <!-- حالة المشكلة -->
        <div class="section">
            <h3>🔍 تشخيص المشكلة</h3>
            <div id="problemStatus" class="status warning">
                ⚠️ جاري فحص البيانات...
            </div>
        </div>

        <!-- إجراءات الإصلاح -->
        <div class="section">
            <h3>🔧 إجراءات الإصلاح</h3>
            <button onclick="clearUserData()" class="clear-btn">
                🗑️ مسح بيانات المستخدم
            </button>
            <button onclick="clearAllData()" class="clear-btn">
                🧹 مسح جميع البيانات
            </button>
        </div>

        <!-- التعليمات -->
        <div class="instructions">
            <h3>📋 تعليمات الإصلاح</h3>
            <ol>
                <li><strong>فحص البيانات:</strong> انقر على "فحص البيانات الحالية"</li>
                <li><strong>تشخيص المشكلة:</strong> تحقق من وجود isActive</li>
                <li><strong>مسح البيانات:</strong> انقر على "مسح بيانات المستخدم"</li>
                <li><strong>تسجيل دخول جديد:</strong> سجل دخول مرة أخرى</li>
                <li><strong>التحقق:</strong> افتح الملف الشخصي وتحقق من الحالة</li>
            </ol>
        </div>

        <!-- نتيجة الإصلاح -->
        <div class="section">
            <h3>✅ نتيجة الإصلاح</h3>
            <div id="fixResult" class="status warning">
                ⏳ لم يتم تطبيق الإصلاح بعد
            </div>
        </div>
    </div>

    <script>
        function checkCurrentData() {
            const currentDataDiv = document.getElementById('currentData');
            const problemStatusDiv = document.getElementById('problemStatus');
            
            try {
                // فحص بيانات المستخدم
                const userDataStr = localStorage.getItem('user');
                const tokenStr = localStorage.getItem('token');
                
                let output = '📊 محتويات localStorage:\n\n';
                
                if (userDataStr) {
                    const userData = JSON.parse(userDataStr);
                    output += '👤 بيانات المستخدم:\n';
                    output += `   الرقم: ${userData.id}\n`;
                    output += `   الاسم: ${userData.username}\n`;
                    output += `   اسم الدخول: ${userData.loginName}\n`;
                    output += `   isActive موجود: ${userData.isActive !== undefined ? 'نعم ✅' : 'لا ❌'}\n`;
                    output += `   قيمة isActive: ${userData.isActive}\n`;
                    output += `   الحالة: ${userData.isActive ? 'نشط ✅' : 'غير نشط ❌'}\n`;
                    output += `   مدير النظام: ${userData.permissions?.isAdmin ? 'نعم ✅' : 'لا ❌'}\n\n`;
                    
                    // تشخيص المشكلة
                    if (userData.isActive === undefined) {
                        problemStatusDiv.className = 'status error';
                        problemStatusDiv.innerHTML = '❌ المشكلة: isActive غير موجود في البيانات المحفوظة<br>🔧 الحل: مسح البيانات وتسجيل دخول جديد';
                    } else if (userData.isActive === true) {
                        problemStatusDiv.className = 'status success';
                        problemStatusDiv.innerHTML = '✅ البيانات صحيحة: isActive موجود ويظهر نشط<br>💡 إذا كانت المشكلة مستمرة، تحقق من المكون';
                    } else {
                        problemStatusDiv.className = 'status warning';
                        problemStatusDiv.innerHTML = '⚠️ المستخدم غير نشط في البيانات المحفوظة<br>🔧 الحل: مسح البيانات وتسجيل دخول جديد';
                    }
                } else {
                    output += '❌ لا توجد بيانات مستخدم محفوظة\n\n';
                    problemStatusDiv.className = 'status warning';
                    problemStatusDiv.innerHTML = '⚠️ لا توجد بيانات مستخدم محفوظة<br>💡 سجل دخول للنظام أولاً';
                }
                
                if (tokenStr) {
                    output += `🔑 التوكن: ${tokenStr.substring(0, 20)}...\n\n`;
                } else {
                    output += '❌ لا يوجد توكن محفوظ\n\n';
                }
                
                // عرض جميع مفاتيح localStorage
                output += '🗂️ جميع المفاتيح المحفوظة:\n';
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    output += `   ${i + 1}. ${key}\n`;
                }
                
                currentDataDiv.textContent = output;
                
            } catch (error) {
                currentDataDiv.textContent = `❌ خطأ في قراءة البيانات: ${error.message}`;
                problemStatusDiv.className = 'status error';
                problemStatusDiv.innerHTML = '❌ خطأ في قراءة localStorage';
            }
        }
        
        function clearUserData() {
            try {
                localStorage.removeItem('user');
                localStorage.removeItem('token');
                
                const fixResultDiv = document.getElementById('fixResult');
                fixResultDiv.className = 'status success';
                fixResultDiv.innerHTML = '✅ تم مسح بيانات المستخدم والتوكن بنجاح<br>💡 الآن سجل دخول جديد للنظام';
                
                // تحديث العرض
                checkCurrentData();
                
            } catch (error) {
                const fixResultDiv = document.getElementById('fixResult');
                fixResultDiv.className = 'status error';
                fixResultDiv.innerHTML = `❌ خطأ في مسح البيانات: ${error.message}`;
            }
        }
        
        function clearAllData() {
            try {
                localStorage.clear();
                
                const fixResultDiv = document.getElementById('fixResult');
                fixResultDiv.className = 'status success';
                fixResultDiv.innerHTML = '✅ تم مسح جميع البيانات من localStorage<br>💡 الآن سجل دخول جديد للنظام';
                
                // تحديث العرض
                checkCurrentData();
                
            } catch (error) {
                const fixResultDiv = document.getElementById('fixResult');
                fixResultDiv.className = 'status error';
                fixResultDiv.innerHTML = `❌ خطأ في مسح البيانات: ${error.message}`;
            }
        }
        
        // فحص البيانات عند تحميل الصفحة
        window.onload = function() {
            checkCurrentData();
        };
    </script>
</body>
</html>
