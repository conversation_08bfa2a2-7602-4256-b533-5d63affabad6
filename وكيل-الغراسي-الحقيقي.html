<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 وكيل الغراسي - النظام الحقيقي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            text-align: center;
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 32px;
        }
        
        .subtitle {
            margin: 10px 0 0 0;
            font-size: 18px;
            opacity: 0.9;
        }
        
        .agent-info {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-right: 5px solid #3498db;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        input, select, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
            transition: all 0.3s;
        }
        
        input:focus, select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }
        
        button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 0;
            transition: all 0.3s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        }
        
        .result {
            background: #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            border: 2px solid #bdc3c7;
            min-height: 60px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .success {
            background: #d5f4e6;
            border-color: #27ae60;
            color: #1e8449;
        }
        
        .error {
            background: #fadbd8;
            border-color: #e74c3c;
            color: #c0392b;
        }
        
        .info {
            background: #d6eaf8;
            border-color: #3498db;
            color: #2471a3;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #3498db;
            font-weight: bold;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #ecf0f1;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .quick-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-card {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .test-card:hover {
            border-color: #3498db;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .status-unknown { background: #f39c12; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 وكيل الغراسي</h1>
            <p class="subtitle">نظام التحقق من العملاء - الإصدار الحقيقي</p>
        </div>

        <div class="agent-info">
            <h3>👤 معلومات الوكيل</h3>
            <p><strong>الاسم:</strong> الغراسي</p>
            <p><strong>اسم المستخدم:</strong> alghurasi</p>
            <p><strong>النوع:</strong> وكيل معتمد</p>
            <p><strong>الحالة:</strong> نشط <span class="status-indicator status-online"></span></p>
        </div>

        <!-- إعدادات الاتصال -->
        <div class="section">
            <h3>🌐 إعدادات الاتصال</h3>
            <div class="form-group">
                <label>نوع الاتصال:</label>
                <select id="connectionType" onchange="updateConnection()">
                    <option value="local">محلي (localhost:8080)</option>
                    <option value="external" selected>خارجي (***********:8080)</option>
                </select>
            </div>
            <div class="form-group">
                <label>عنوان الخادم:</label>
                <input type="text" id="serverUrl" value="http://***********:8080" readonly>
                <small>هذا هو العنوان الذي ستستخدمه من خارج السيرفر</small>
            </div>
        </div>

        <!-- بيانات العميل للتحقق -->
        <div class="section">
            <h3>👤 بيانات العميل للتحقق</h3>
            <div class="form-group">
                <label>رمز العميل:</label>
                <input type="text" id="clientCode" value="1000" placeholder="أدخل رمز العميل">
            </div>
            <div class="form-group">
                <label>توكن العميل:</label>
                <input type="text" id="clientToken" value="ABC12345" placeholder="أدخل توكن العميل">
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="section">
            <h3>⚡ اختبارات سريعة</h3>
            <div class="quick-test">
                <div class="test-card" onclick="testClient('1000', 'ABC12345')">
                    <h4>عميل 1000</h4>
                    <p>ABC12345</p>
                </div>
                <div class="test-card" onclick="testClient('1001', 'XYZ67890')">
                    <h4>عميل 1001</h4>
                    <p>XYZ67890</p>
                </div>
                <div class="test-card" onclick="testClient('1002', 'DEF54321')">
                    <h4>عميل 1002</h4>
                    <p>DEF54321</p>
                </div>
                <div class="test-card" onclick="testClient('9999', 'INVALID')">
                    <h4>عميل وهمي</h4>
                    <p>اختبار الفشل</p>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="section">
            <button onclick="verifyClient()" id="verifyBtn">
                🚀 التحقق من العميل
            </button>
            <button onclick="testConnection()" id="testBtn">
                🔍 اختبار الاتصال
            </button>
            <button onclick="clearResults()" id="clearBtn">
                🗑️ مسح النتائج
            </button>
        </div>

        <!-- منطقة التحميل -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            جاري المعالجة...
        </div>

        <!-- النتائج -->
        <div class="section">
            <h3>📊 نتائج التحقق</h3>
            <div id="result" class="result">
                في انتظار التحقق من العميل...
                
استخدم الأزرار أعلاه لبدء الاختبار أو أدخل بيانات العميل يدوياً.
            </div>
        </div>

        <!-- معلومات الكود -->
        <div class="section">
            <h3>💻 الكود للاستخدام الخارجي</h3>
            <div class="result info" id="codeExample">
# للاستخدام من خارج السيرفر:
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "alghurasi",
    "agent_login_password": "alghurasi123",
    "client_code": "1000",
    "client_token": "ABC12345"
  }'
            </div>
        </div>
    </div>

    <script>
        // تحديث إعدادات الاتصال
        function updateConnection() {
            const type = document.getElementById('connectionType').value;
            const serverUrl = document.getElementById('serverUrl');
            
            if (type === 'local') {
                serverUrl.value = 'http://localhost:8080';
            } else {
                serverUrl.value = 'http://***********:8080';
            }
            
            updateCodeExample();
        }

        // تحديث مثال الكود
        function updateCodeExample() {
            const serverUrl = document.getElementById('serverUrl').value;
            const clientCode = document.getElementById('clientCode').value;
            const clientToken = document.getElementById('clientToken').value;
            
            const codeExample = document.getElementById('codeExample');
            codeExample.textContent = `# للاستخدام من خارج السيرفر:
curl -X POST ${serverUrl}/api/external/verify-direct \\
  -H "Content-Type: application/json" \\
  -d '{
    "agent_login_name": "alghurasi",
    "agent_login_password": "alghurasi123",
    "client_code": "${clientCode}",
    "client_token": "${clientToken}"
  }'

# JavaScript:
const response = await fetch('${serverUrl}/api/external/verify-direct', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        agent_login_name: 'alghurasi',
        agent_login_password: 'alghurasi123',
        client_code: '${clientCode}',
        client_token: '${clientToken}'
    })
});

const result = await response.json();
console.log(result);`;
        }

        // عرض حالة التحميل
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // عرض النتيجة
        function showResult(message, type = 'info') {
            const element = document.getElementById('result');
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // اختبار الاتصال
        async function testConnection() {
            showLoading(true);
            showResult('جاري اختبار الاتصال...', 'info');
            
            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const response = await fetch(`${serverUrl}/api/external/health`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    showResult(`✅ الاتصال ناجح!

🌐 الخادم: ${serverUrl}
📊 حالة النظام: ${data.message}
🗄️ قاعدة البيانات: ${data.data.database}
👥 الجلسات النشطة: ${data.data.active_sessions}
📅 الوقت: ${new Date(data.data.timestamp).toLocaleString('ar-SA')}
🔢 الإصدار: ${data.data.version}`, 'success');
                } else {
                    showResult(`❌ فشل الاتصال: ${data.message}`, 'error');
                }
            } catch (error) {
                showResult(`❌ خطأ في الاتصال: ${error.message}

🔧 تحقق من:
   - عنوان الخادم صحيح
   - الخادم يعمل
   - لا توجد مشاكل في الشبكة
   - المنفذ 8080 مفتوح`, 'error');
            }
            
            showLoading(false);
        }

        // التحقق من العميل
        async function verifyClient() {
            showLoading(true);
            showResult('جاري التحقق من العميل...', 'info');
            
            try {
                const serverUrl = document.getElementById('serverUrl').value;
                const clientCode = document.getElementById('clientCode').value;
                const clientToken = document.getElementById('clientToken').value;

                if (!clientCode || !clientToken) {
                    showResult('❌ يرجى إدخال رمز العميل والتوكن', 'error');
                    showLoading(false);
                    return;
                }

                console.log('🚀 بدء التحقق من العميل...');
                console.log('الخادم:', serverUrl);
                console.log('العميل:', clientCode);

                const response = await fetch(`${serverUrl}/api/external/verify-direct`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_login_name: 'alghurasi',
                        agent_login_password: 'alghurasi123',
                        client_code: clientCode,
                        client_token: clientToken
                    })
                });

                const data = await response.json();
                console.log('📥 استجابة الخادم:', data);

                if (data.status === 'success') {
                    const agent = data.data.agent_info;
                    const client = data.data.client_info;
                    const verification = data.data.verification_result;
                    
                    const successMessage = `🎉 تم التحقق بنجاح!

🤝 معلومات الوكيل:
   الاسم: ${agent.agent_name}
   النوع: ${agent.agency_type}
   الرقم: ${agent.agent_id}

👤 معلومات العميل:
   الاسم: ${client.client_name}
   التطبيق: ${client.app_name}
   الرمز: ${client.client_code}
   الحالة: ${client.status_text}
   عنوان IP: ${client.ip_address}
   تاريخ الإنشاء: ${client.created_date}
   المنشئ: ${client.created_by_user}

✅ نتائج التحقق:
   الوكيل: ${verification.agent_verified ? 'مصادق ✅' : 'فشل ❌'}
   العميل: ${verification.client_verified ? 'موجود ✅' : 'غير موجود ❌'}
   التوكن: ${verification.token_verified ? 'مطابق ✅' : 'غير مطابق ❌'}

⏰ معلومات العملية:
   الوقت: ${new Date(data.data.operation_info.timestamp).toLocaleString('ar-SA')}
   عنوان IP: ${data.data.operation_info.ip_address}`;

                    showResult(successMessage, 'success');
                    
                } else {
                    let errorMessage = `❌ فشل التحقق: ${data.message}\n\n`;
                    
                    if (data.error_code === 'AGENT_AUTH_FAILED') {
                        errorMessage += '🔐 خطأ في بيانات الوكيل';
                    } else if (data.error_code === 'CLIENT_NOT_FOUND') {
                        errorMessage += `🔍 العميل برمز ${clientCode} غير موجود في النظام`;
                        if (data.agent_info) {
                            errorMessage += `\n✅ الوكيل مصادق: ${data.agent_info.agent_name}`;
                        }
                    } else if (data.error_code === 'TOKEN_MISMATCH') {
                        errorMessage += `🔐 التوكن ${clientToken} غير مطابق`;
                        if (data.agent_info) {
                            errorMessage += `\n✅ الوكيل مصادق: ${data.agent_info.agent_name}`;
                        }
                        if (data.client_info) {
                            errorMessage += `\n✅ العميل موجود: ${data.client_info.client_code}`;
                        }
                    }
                    
                    errorMessage += `\n\n🔧 كود الخطأ: ${data.error_code}`;
                    showResult(errorMessage, 'error');
                }

            } catch (error) {
                console.error('❌ خطأ في الاتصال:', error);
                let errorMsg = `❌ خطأ في الاتصال: ${error.message}\n\n`;
                
                if (error.message.includes('Failed to fetch')) {
                    errorMsg += '🌐 تحقق من:\n';
                    errorMsg += '   - عنوان الخادم صحيح\n';
                    errorMsg += '   - الخادم يعمل\n';
                    errorMsg += '   - لا توجد مشاكل في الشبكة\n';
                    errorMsg += '   - استخدم العنوان الخارجي من خارج السيرفر\n';
                    errorMsg += '   - المنفذ 8080 مفتوح في جدار الحماية';
                }
                
                showResult(errorMsg, 'error');
            }

            showLoading(false);
        }

        // اختبار عميل محدد
        function testClient(code, token) {
            document.getElementById('clientCode').value = code;
            document.getElementById('clientToken').value = token;
            updateCodeExample();
            verifyClient();
        }

        // مسح النتائج
        function clearResults() {
            showResult('في انتظار التحقق من العميل...\n\nاستخدم الأزرار أعلاه لبدء الاختبار أو أدخل بيانات العميل يدوياً.', 'info');
        }

        // تحديث الكود عند تحميل الصفحة
        window.onload = function() {
            updateCodeExample();
            
            // تحديث الكود عند تغيير القيم
            document.getElementById('clientCode').addEventListener('input', updateCodeExample);
            document.getElementById('clientToken').addEventListener('input', updateCodeExample);
            
            // اختبار الاتصال تلقائياً
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
