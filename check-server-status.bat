@echo off
echo ========================================
echo      فحص حالة خادم النظام اليمني
echo ========================================
echo.

echo 1. فحص عمليات Node.js...
tasklist /FI "IMAGENAME eq node.exe" 2>nul | find /I "node.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ توجد عمليات Node.js تعمل:
    tasklist /FI "IMAGENAME eq node.exe"
) else (
    echo ❌ لا توجد عمليات Node.js تعمل
)
echo.

echo 2. فحص المنفذ 8080...
netstat -ano | findstr :8080 >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ المنفذ 8080 مستخدم:
    netstat -ano | findstr :8080
) else (
    echo ❌ المنفذ 8080 فارغ
)
echo.

echo 3. فحص ملفات الخادم...
if exist "C:\yemclinet\server\working-server.js" (
    echo ✅ ملف الخادم موجود: working-server.js
) else (
    echo ❌ ملف الخادم مفقود: working-server.js
)

if exist "C:\yemclinet\server\package.json" (
    echo ✅ ملف package.json موجود
) else (
    echo ❌ ملف package.json مفقود
)

if exist "C:\yemclinet\server\node_modules" (
    echo ✅ مجلد node_modules موجود
) else (
    echo ❌ مجلد node_modules مفقود - يجب تشغيل npm install
)
echo.

echo 4. فحص ملفات العميل...
if exist "C:\yemclinet\client\dist\index.html" (
    echo ✅ ملفات العميل موجودة
) else (
    echo ❌ ملفات العميل مفقودة - يجب تشغيل npm run build
)
echo.

echo 5. اختبار الاتصال بالخادم...
echo جاري اختبار http://localhost:8080/health...
curl -s http://localhost:8080/health >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ الخادم يستجيب على المنفذ 8080
    curl -s http://localhost:8080/health
) else (
    echo ❌ الخادم لا يستجيب على المنفذ 8080
)
echo.

echo ========================================
echo           تشخيص المشاكل
echo ========================================
echo.
echo إذا كان الخادم لا يعمل:
echo 1. تشغيل: start-yemen-server.bat
echo 2. أو يدوياً: cd C:\yemclinet\server && node working-server.js
echo.
echo إذا كانت هناك أخطاء:
echo 1. تحقق من قاعدة البيانات
echo 2. تحقق من ملفات node_modules
echo 3. تحقق من ملفات العميل المبنية
echo.

pause
