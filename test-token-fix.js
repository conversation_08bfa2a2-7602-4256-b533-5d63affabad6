/**
 * اختبار إصلاح Token
 */

async function testTokenFix() {
  console.log('🔧 اختبار إصلاح Token...\n');

  try {
    // اختبار تسجيل دخول مع فحص Token
    console.log('1️⃣ اختبار تسجيل دخول admin مع فحص Token:');
    const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        loginName: 'admin',
        password: 'admin123',
        deviceId: 'test_token_' + Date.now()
      })
    });

    console.log(`   📡 Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('   ✅ تسجيل الدخول نجح!');
      console.log(`   👤 المستخدم: ${loginData.user?.username}`);
      console.log(`   🔑 Token موجود: ${loginData.token ? 'نعم ✅' : 'لا ❌'}`);
      
      if (loginData.token) {
        console.log(`   📝 Token: ${loginData.token.substring(0, 30)}...`);
        
        // اختبار API مع Token
        console.log('\n2️⃣ اختبار API البيانات مع Token:');
        const apiResponse = await fetch('http://localhost:8080/api/data-records?page=1&limit=3', {
          headers: {
            'Authorization': `Bearer ${loginData.token}`,
            'X-Device-ID': 'test_token_' + Date.now(),
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`   📡 API Status: ${apiResponse.status}`);
        
        if (apiResponse.ok) {
          const apiData = await apiResponse.json();
          console.log('   ✅ API يعمل مع Token!');
          console.log(`   📊 عدد السجلات: ${apiData.dataRecords?.length}`);
          console.log(`   📋 إجمالي: ${apiData.total}`);
        } else {
          console.log('   ❌ API فشل مع Token');
          const errorText = await apiResponse.text();
          console.log(`   📝 الخطأ: ${errorText}`);
        }
        
      } else {
        console.log('   ❌ Token غير موجود في response!');
      }
      
    } else {
      const errorData = await loginResponse.json();
      console.log('   ❌ تسجيل الدخول فشل!');
      console.log(`   📝 الخطأ: ${errorData.message || errorData.error}`);
    }

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error.message);
  }
}

testTokenFix().catch(console.error);
