# 🚀 دليل أوامر الخوادم - YemClient

## 📁 **الملف الرئيسي: `server-control.ps1`**

### 🎯 **يعمل من أي مسار - لا يحتاج مجلد محدد**

---

## 🔧 **الأوامر الأساسية:**

### 1. **فحص الخوادم:**
```powershell
.\server-control.ps1 check
```
**يفحص:**
- ✅ عمليات Node.js
- ✅ تطبيقات PM2  
- ✅ المنافذ النشطة

### 2. **إيقاف بلطف:**
```powershell
.\server-control.ps1 stop
```
**يوقف:**
- 🛑 PM2 (stop → delete → kill)
- 🛑 عمليات Node.js (بلطف)

### 3. **إيقاف بالقوة:**
```powershell
.\server-control.ps1 kill
```
**يوقف:**
- ⚡ جميع العمليات بالقوة
- ⚡ حتى العمليات المعلقة

### 4. **حالة شاملة:**
```powershell
.\server-control.ps1 status
```
**يعرض:**
- 📊 تفاصيل كاملة
- 📊 اختبار صحة الخوادم
- 📊 ملخص النظام

### 5. **المساعدة:**
```powershell
.\server-control.ps1 help
```

---

## ⚡ **الأوامر السريعة (بدون ملف):**

### **فحص سريع:**
```powershell
# فحص Node.js
Get-Process -Name "node"

# فحص المنفذ 8080
Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue

# فحص PM2
pm2 status
```

### **إيقاف سريع:**
```powershell
# إيقاف جميع Node.js
Get-Process -Name "node" | Stop-Process -Force

# إيقاف PM2
pm2 kill
```

### **اختبار الخادم:**
```powershell
# اختبار HTTP
Invoke-WebRequest -Uri "http://localhost:8080/health" -TimeoutSec 5
```

---

## 📍 **المسارات والاستخدام:**

### **يعمل من أي مكان:**
- ✅ `server-control.ps1` (الملف الجديد)
- ✅ `Get-Process -Name "node"`
- ✅ `Get-NetTCPConnection -LocalPort 8080`

### **يحتاج مجلد server:**
- ❌ `pm2 start ecosystem.config.js`
- ❌ `npm run pm2:start`
- ❌ `node production-server.js`

---

## 🎯 **أمثلة عملية:**

### **سيناريو 1: فحص سريع**
```powershell
# من أي مكان
.\server-control.ps1 check
```

### **سيناريو 2: إيقاف كامل**
```powershell
# من أي مكان
.\server-control.ps1 kill
```

### **سيناريو 3: فحص مفصل**
```powershell
# من أي مكان
.\server-control.ps1 status
```

### **سيناريو 4: بدء خادم جديد**
```powershell
# إيقاف القديم
.\server-control.ps1 kill

# الانتقال لمجلد server
cd server

# بدء جديد
pm2 start ecosystem.config.js
```

---

## 📊 **مخرجات الأوامر:**

### **عند الفحص:**
```
🔍 فحص عمليات Node.js...
✅ عدد عمليات Node.js: 3

PID     اسم العملية    الذاكرة (MB)  وقت البدء
----    -----------    -----------   ----------
1234    <USER>          <GROUP>.2          14:30:15
5678    node          52.1          14:31:20
9012    node          38.9          14:32:10

🔍 فحص PM2...
✅ عدد تطبيقات PM2: 1
  📱 yemclient-server: 🟢 يعمل (الذاكرة: 48.5MB)
```

### **عند الإيقاف:**
```
⛔ إيقاف بالقوة لجميع الخوادم...

🔄 إيقاف PM2...
  ✅ تم إيقاف PM2

🔄 إيقاف عمليات Node.js...
  ✅ تم إيقاف 3 عملية بالقوة

🔍 فحص نهائي...
  ✅ تم إيقاف جميع الخوادم بنجاح
```

---

## 🚨 **حالات الطوارئ:**

### **إذا لم تتوقف العمليات:**
```powershell
# إيقاف بالقوة
.\server-control.ps1 kill

# أو يدوياً
Get-Process -Name "node" | Stop-Process -Force
```

### **إذا كان المنفذ محجوز:**
```powershell
# فحص من يستخدم المنفذ
Get-NetTCPConnection -LocalPort 8080

# إيقاف العملية حسب PID
Stop-Process -Id [PID] -Force
```

### **إذا كان PM2 معلق:**
```powershell
# إيقاف PM2 daemon
pm2 kill

# أو حذف مجلد PM2
Remove-Item -Recurse -Force $env:USERPROFILE\.pm2
```

---

## 💡 **نصائح مهمة:**

### **1. الاستخدام اليومي:**
- استخدم `check` للفحص السريع
- استخدم `stop` للإيقاف العادي
- استخدم `kill` عند المشاكل

### **2. قبل بدء خادم جديد:**
```powershell
.\server-control.ps1 kill
cd server
pm2 start ecosystem.config.js
```

### **3. للمراقبة المستمرة:**
```powershell
# فحص كل 30 ثانية
while ($true) { 
    .\server-control.ps1 check
    Start-Sleep 30 
}
```

---

## 🎯 **الخلاصة:**

### **ملف واحد يحل كل شيء:**
- 📁 `server-control.ps1`
- 🌍 يعمل من أي مكان
- 🔍 يفحص كل شيء
- ⛔ يوقف كل شيء
- 📊 يعرض تفاصيل شاملة

### **الأوامر الأساسية:**
```powershell
.\server-control.ps1 check    # فحص
.\server-control.ps1 stop     # إيقاف
.\server-control.ps1 kill     # إيقاف بالقوة
.\server-control.ps1 status   # حالة شاملة
```

**🎉 الآن لديك تحكم كامل في جميع الخوادم من ملف واحد!**
