const http = require('http');

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 8080,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: parsed
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function testRealDatabaseData() {
  console.log('🧪 Testing Real Database Data...\n');
  
  try {
    // 1. اختبار فحص الصحة مع إحصائيات قاعدة البيانات
    console.log('1. Testing Health Check with Database Stats:');
    const healthResult = await makeRequest('/health');
    
    if (healthResult.status === 200 && healthResult.data.database) {
      console.log('✅ Health check successful');
      console.log(`   Database Status: ${healthResult.data.database.status}`);
      console.log(`   Users in DB: ${healthResult.data.database.users}`);
      console.log(`   Clients in DB: ${healthResult.data.database.clients}`);
      console.log(`   Agents in DB: ${healthResult.data.database.agents}`);
    } else {
      console.log('❌ Health check failed');
    }
    console.log('');

    // 2. اختبار تسجيل الدخول مع مستخدم حقيقي
    console.log('2. Testing Real User Login:');
    const loginResult = await makeRequest('/api/auth/login', 'POST', {
      loginName: 'admin',
      password: 'admin123456',
      deviceId: 'test-device'
    });
    
    if (loginResult.status === 200 && loginResult.data.success) {
      console.log('✅ Real user login successful');
      console.log(`   User ID: ${loginResult.data.user.id}`);
      console.log(`   Username: ${loginResult.data.user.username}`);
      console.log(`   Login Name: ${loginResult.data.user.loginName}`);
      console.log(`   Active: ${loginResult.data.user.isActive}`);
      console.log(`   Has Permissions: ${Object.keys(loginResult.data.user.permissions || {}).length > 0}`);
    } else {
      console.log('❌ Real user login failed');
      console.log(`   Message: ${loginResult.data.message}`);
    }
    console.log('');

    // 3. اختبار قائمة المستخدمين الحقيقية
    console.log('3. Testing Real Users List:');
    const usersResult = await makeRequest('/api/users');
    
    if (usersResult.status === 200 && usersResult.data.users) {
      console.log('✅ Real users list retrieved');
      console.log(`   Total Users: ${usersResult.data.pagination.total}`);
      console.log(`   Users in Response: ${usersResult.data.users.length}`);
      
      if (usersResult.data.users.length > 0) {
        const firstUser = usersResult.data.users[0];
        console.log(`   First User: ${firstUser.username} (${firstUser.loginName})`);
        console.log(`   Created: ${new Date(firstUser.createdAt).toLocaleDateString('ar-SA')}`);
      }
    } else {
      console.log('❌ Real users list failed');
    }
    console.log('');

    // 4. اختبار قائمة العملاء الحقيقية
    console.log('4. Testing Real Clients List:');
    const clientsResult = await makeRequest('/api/clients');
    
    if (clientsResult.status === 200 && clientsResult.data.clients) {
      console.log('✅ Real clients list retrieved');
      console.log(`   Total Clients: ${clientsResult.data.pagination.total}`);
      console.log(`   Clients in Response: ${clientsResult.data.clients.length}`);
      
      if (clientsResult.data.clients.length > 0) {
        const firstClient = clientsResult.data.clients[0];
        console.log(`   First Client: ${firstClient.clientName} (Code: ${firstClient.clientCode})`);
        console.log(`   App: ${firstClient.appName}`);
        console.log(`   Status: ${firstClient.status === 1 ? 'Active' : 'Inactive'}`);
        if (firstClient.user) {
          console.log(`   Owner: ${firstClient.user.username}`);
        }
      }
    } else {
      console.log('❌ Real clients list failed');
    }
    console.log('');

    // 5. اختبار قائمة الوكلاء الحقيقية
    console.log('5. Testing Real Agents List:');
    const agentsResult = await makeRequest('/api/agents');
    
    if (agentsResult.status === 200 && agentsResult.data.agents) {
      console.log('✅ Real agents list retrieved');
      console.log(`   Total Agents: ${agentsResult.data.pagination.total}`);
      console.log(`   Agents in Response: ${agentsResult.data.agents.length}`);
      
      if (agentsResult.data.agents.length > 0) {
        const firstAgent = agentsResult.data.agents[0];
        console.log(`   First Agent: ${firstAgent.agentName}`);
        console.log(`   Agency: ${firstAgent.agencyName}`);
        console.log(`   Type: ${firstAgent.agencyType}`);
        console.log(`   Active: ${firstAgent.isActive}`);
      }
    } else {
      console.log('❌ Real agents list failed');
    }
    console.log('');

    // 6. اختبار سجلات البيانات الحقيقية
    console.log('6. Testing Real Data Records:');
    const dataRecordsResult = await makeRequest('/api/data-records');
    
    if (dataRecordsResult.status === 200 && dataRecordsResult.data.dataRecords) {
      console.log('✅ Real data records retrieved');
      console.log(`   Total Records: ${dataRecordsResult.data.pagination.total}`);
      console.log(`   Records in Response: ${dataRecordsResult.data.dataRecords.length}`);
      
      if (dataRecordsResult.data.dataRecords.length > 0) {
        const firstRecord = dataRecordsResult.data.dataRecords[0];
        console.log(`   First Record: Client ${firstRecord.clientCode}`);
        console.log(`   Status: ${firstRecord.operationStatus}`);
        console.log(`   Date: ${new Date(firstRecord.operationDate).toLocaleDateString('ar-SA')}`);
        if (firstRecord.agent) {
          console.log(`   Agent: ${firstRecord.agent.agentName}`);
        }
        if (firstRecord.client) {
          console.log(`   Client: ${firstRecord.client.clientName}`);
        }
      }
    } else {
      console.log('❌ Real data records failed');
    }
    console.log('');

    // 7. اختبار إحصائيات الأمان الحقيقية
    console.log('7. Testing Real Security Stats:');
    const securityResult = await makeRequest('/api/security/stats');
    
    if (securityResult.status === 200) {
      console.log('✅ Real security stats retrieved');
      console.log(`   Total Login Attempts: ${securityResult.data.totalAttempts}`);
      console.log(`   Successful Logins: ${securityResult.data.successfulLogins}`);
      console.log(`   Failed Logins: ${securityResult.data.failedLogins}`);
      console.log(`   Active Users: ${securityResult.data.summary?.totalUsers || 'N/A'}`);
      console.log(`   Active Clients: ${securityResult.data.summary?.totalClients || 'N/A'}`);
      console.log(`   Active Agents: ${securityResult.data.summary?.totalAgents || 'N/A'}`);
    } else {
      console.log('❌ Real security stats failed');
    }
    console.log('');

    // 8. اختبار محاولات تسجيل الدخول الحقيقية
    console.log('8. Testing Real Login Attempts:');
    const attemptsResult = await makeRequest('/api/security/login-attempts');
    
    if (attemptsResult.status === 200 && attemptsResult.data.attempts) {
      console.log('✅ Real login attempts retrieved');
      console.log(`   Total Attempts: ${attemptsResult.data.total}`);
      console.log(`   Attempts in Response: ${attemptsResult.data.attempts.length}`);
      
      if (attemptsResult.data.attempts.length > 0) {
        const recentAttempt = attemptsResult.data.attempts[0];
        console.log(`   Most Recent: ${recentAttempt.username} (${recentAttempt.type})`);
        console.log(`   IP: ${recentAttempt.ip}`);
        console.log(`   Time: ${new Date(recentAttempt.timestamp).toLocaleString('ar-SA')}`);
      }
    } else {
      console.log('❌ Real login attempts failed');
    }

    console.log('\n🎉 Real Database Test Completed!');
    console.log('✅ All data is coming from the real PostgreSQL database');
    console.log('✅ Authentication is working with real user accounts');
    console.log('✅ All APIs are connected to the database');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testRealDatabaseData();
