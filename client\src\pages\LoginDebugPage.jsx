import React, { useState } from 'react';
import { 
  Container, 
  Paper, 
  TextField, 
  Button, 
  Typography, 
  Box,
  Alert,
  Divider,
  Grid
} from '@mui/material';

function LoginDebugPage() {
  const [loginData, setLoginData] = useState({
    loginName: 'admin',
    password: 'admin123',
    deviceId: 'debug-device-' + Date.now(),
    userType: 'user'
  });
  
  const [clientData, setClientData] = useState({
    loginName: '1001',
    password: '123456',
    deviceId: 'debug-device-' + Date.now(),
    userType: 'client'
  });

  const [results, setResults] = useState([]);

  const addResult = (message, type = 'info', data = null) => {
    const timestamp = new Date().toLocaleTimeString();
    setResults(prev => [...prev, { timestamp, message, type, data }]);
  };

  const testUserLogin = async () => {
    addResult('🔄 بدء اختبار دخول المستخدم...', 'info');
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(loginData)
      });

      const result = await response.json();
      
      addResult(`📡 استجابة الخادم: ${response.status}`, response.ok ? 'success' : 'error');
      addResult(`📊 البيانات: ${JSON.stringify(result, null, 2)}`, 'info', result);
      
      if (response.ok && result.success) {
        addResult('✅ تسجيل دخول المستخدم نجح!', 'success');
        
        // حفظ في localStorage
        if (result.token) {
          localStorage.setItem('token', result.token);
          addResult('💾 تم حفظ Token', 'success');
        }
        if (result.user) {
          localStorage.setItem('user', JSON.stringify(result.user));
          addResult('💾 تم حفظ بيانات المستخدم', 'success');
        }
      } else {
        addResult(`❌ فشل تسجيل الدخول: ${result.message}`, 'error');
      }
    } catch (error) {
      addResult(`💥 خطأ في الطلب: ${error.message}`, 'error');
    }
  };

  const testClientLogin = async () => {
    addResult('🔄 بدء اختبار دخول العميل...', 'info');
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(clientData)
      });

      const result = await response.json();
      
      addResult(`📡 استجابة الخادم: ${response.status}`, response.ok ? 'success' : 'error');
      addResult(`📊 البيانات: ${JSON.stringify(result, null, 2)}`, 'info', result);
      
      if (response.ok && result.success) {
        addResult('✅ تسجيل دخول العميل نجح!', 'success');
        
        // حفظ في localStorage
        if (result.token) {
          localStorage.setItem('token', result.token);
          addResult('💾 تم حفظ Token', 'success');
        }
        if (result.user) {
          localStorage.setItem('user', JSON.stringify(result.user));
          addResult('💾 تم حفظ بيانات العميل', 'success');
        }
      } else {
        addResult(`❌ فشل تسجيل الدخول: ${result.message}`, 'error');
      }
    } catch (error) {
      addResult(`💥 خطأ في الطلب: ${error.message}`, 'error');
    }
  };

  const clearResults = () => {
    setResults([]);
  };

  const checkLocalStorage = () => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    
    addResult('🔍 فحص localStorage:', 'info');
    addResult(`Token: ${token ? 'موجود' : 'غير موجود'}`, token ? 'success' : 'warning');
    addResult(`User: ${user ? 'موجود' : 'غير موجود'}`, user ? 'success' : 'warning');
    
    if (user) {
      try {
        const userData = JSON.parse(user);
        addResult(`بيانات المستخدم: ${JSON.stringify(userData, null, 2)}`, 'info');
      } catch (e) {
        addResult('خطأ في تحليل بيانات المستخدم', 'error');
      }
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom align="center">
        🔧 صفحة تشخيص تسجيل الدخول
      </Typography>
      
      <Grid container spacing={3}>
        {/* User Login Test */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              👤 اختبار دخول المستخدم
            </Typography>
            
            <TextField
              fullWidth
              label="اسم المستخدم"
              value={loginData.loginName}
              onChange={(e) => setLoginData({...loginData, loginName: e.target.value})}
              margin="normal"
            />
            
            <TextField
              fullWidth
              label="كلمة المرور"
              type="password"
              value={loginData.password}
              onChange={(e) => setLoginData({...loginData, password: e.target.value})}
              margin="normal"
            />
            
            <TextField
              fullWidth
              label="معرف الجهاز"
              value={loginData.deviceId}
              onChange={(e) => setLoginData({...loginData, deviceId: e.target.value})}
              margin="normal"
            />
            
            <Button
              fullWidth
              variant="contained"
              onClick={testUserLogin}
              sx={{ mt: 2 }}
            >
              اختبار دخول المستخدم
            </Button>
          </Paper>
        </Grid>

        {/* Client Login Test */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              🏢 اختبار دخول العميل
            </Typography>
            
            <TextField
              fullWidth
              label="رقم العميل"
              value={clientData.loginName}
              onChange={(e) => setClientData({...clientData, loginName: e.target.value})}
              margin="normal"
            />
            
            <TextField
              fullWidth
              label="كلمة المرور"
              type="password"
              value={clientData.password}
              onChange={(e) => setClientData({...clientData, password: e.target.value})}
              margin="normal"
            />
            
            <TextField
              fullWidth
              label="معرف الجهاز"
              value={clientData.deviceId}
              onChange={(e) => setClientData({...clientData, deviceId: e.target.value})}
              margin="normal"
            />
            
            <Button
              fullWidth
              variant="contained"
              onClick={testClientLogin}
              sx={{ mt: 2 }}
            >
              اختبار دخول العميل
            </Button>
          </Paper>
        </Grid>

        {/* Controls */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button variant="outlined" onClick={checkLocalStorage}>
                فحص localStorage
              </Button>
              <Button variant="outlined" onClick={clearResults}>
                مسح النتائج
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* Results */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              📋 نتائج الاختبار
            </Typography>
            
            <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
              {results.map((result, index) => (
                <Alert 
                  key={index} 
                  severity={result.type} 
                  sx={{ mb: 1 }}
                >
                  <Typography variant="body2">
                    <strong>{result.timestamp}</strong> - {result.message}
                  </Typography>
                </Alert>
              ))}
              
              {results.length === 0 && (
                <Typography color="text.secondary" align="center">
                  لا توجد نتائج بعد. جرب أحد الاختبارات أعلاه.
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
}

export default LoginDebugPage;
