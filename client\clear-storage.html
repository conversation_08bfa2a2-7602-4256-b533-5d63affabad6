<!DOCTYPE html>
<html>
<head>
    <title>تنظيف البيانات المحفوظة - YemClient</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            max-width: 500px;
            margin: 0 auto;
        }
        button {
            background: #ff4757;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
        }
        button:hover {
            background: #ff3742;
            transform: translateY(-2px);
        }
        .success {
            background: #2ed573;
            color: white;
        }
        .info {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .redirect-btn {
            background: #5352ed;
        }
        .redirect-btn:hover {
            background: #3742fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 تنظيف البيانات المحفوظة</h1>
        
        <div class="info">
            <h3>المشكلة:</h3>
            <p>يوجد بيانات قديمة محفوظة في المتصفح تمنع تسجيل الدخول</p>
        </div>

        <div id="status"></div>
        
        <button onclick="clearAllStorage()">🗑️ تنظيف البيانات المحفوظة</button>
        <button onclick="showStorageInfo()">📋 عرض البيانات الحالية</button>
        
        <div id="storage-info"></div>
        
        <button id="redirect-btn" class="redirect-btn" onclick="redirectToLogin()" style="display:none;">
            🚀 الذهاب لصفحة تسجيل الدخول
        </button>
    </div>

    <script>
        function clearAllStorage() {
            try {
                // تنظيف localStorage
                const keysToRemove = ['token', 'user', 'deviceId'];
                keysToRemove.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        console.log(`تم حذف: ${key}`);
                    }
                });
                
                // تنظيف sessionStorage
                sessionStorage.clear();
                
                // تنظيف الكوكيز
                document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                });
                
                document.getElementById('status').innerHTML = `
                    <div class="success">
                        <h3>✅ تم التنظيف بنجاح!</h3>
                        <p>تم حذف جميع البيانات المحفوظة القديمة</p>
                        <p>يمكنك الآن تسجيل الدخول بشكل طبيعي</p>
                    </div>
                `;
                
                document.getElementById('redirect-btn').style.display = 'inline-block';
                
            } catch (error) {
                document.getElementById('status').innerHTML = `
                    <div style="background: #ff4757; padding: 15px; border-radius: 8px;">
                        <h3>❌ حدث خطأ</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }
        
        function showStorageInfo() {
            const info = [];
            
            // فحص localStorage
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            const deviceId = localStorage.getItem('deviceId');
            
            info.push(`🔑 Token: ${token ? 'موجود' : 'غير موجود'}`);
            info.push(`👤 User: ${user ? 'موجود' : 'غير موجود'}`);
            info.push(`📱 Device ID: ${deviceId ? deviceId : 'غير موجود'}`);
            
            document.getElementById('storage-info').innerHTML = `
                <div class="info">
                    <h3>📊 البيانات الحالية:</h3>
                    ${info.map(item => `<p>${item}</p>`).join('')}
                </div>
            `;
        }
        
        function redirectToLogin() {
            // العودة للصفحة الرئيسية
            window.location.href = '/';
        }
        
        // عرض معلومات البيانات عند تحميل الصفحة
        window.onload = function() {
            showStorageInfo();
        };
    </script>
</body>
</html>
