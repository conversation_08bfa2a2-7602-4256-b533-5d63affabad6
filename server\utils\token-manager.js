const jwt = require('jsonwebtoken')
const crypto = require('crypto')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// مفتاح سري لتوقيع التوكن
const JWT_SECRET = process.env.JWT_SECRET || 'yemclient_api_secret_key_2025'
const TOKEN_EXPIRY = '24h' // انتهاء صلاحية التوكن بعد 24 ساعة

class TokenManager {
  /**
   * إنشاء توكن جديد للوكيل
   * @param {Object} agent - بيانات الوكيل
   * @param {string} ipAddress - عنوان IP
   * @param {string} userAgent - معلومات المتصفح
   * @returns {Object} - التوكن وتاريخ انتهاء الصلاحية
   */
  static async createAgentToken(agent, ipAddress, userAgent = null) {
    try {
      // إنشاء payload للتوكن
      const payload = {
        agentId: agent.id,
        agentName: agent.agentName,
        agencyType: agent.agencyType,
        type: 'agent_api',
        iat: Math.floor(Date.now() / 1000)
      }

      // إنشاء التوكن
      const token = jwt.sign(payload, JWT_SECRET, { expiresIn: TOKEN_EXPIRY })

      // حساب تاريخ انتهاء الصلاحية
      const expiresAt = new Date()
      expiresAt.setHours(expiresAt.getHours() + 24)

      // حفظ الجلسة في قاعدة البيانات
      const session = await prisma.agentSession.create({
        data: {
          agentId: agent.id,
          token,
          ipAddress,
          userAgent,
          expiresAt,
          isActive: true
        }
      })

      return {
        token,
        expiresAt: expiresAt.toISOString(),
        sessionId: session.id
      }
    } catch (error) {
      console.error('Error creating agent token:', error)
      throw new Error('Failed to create authentication token')
    }
  }

  /**
   * التحقق من صحة التوكن
   * @param {string} token - التوكن المراد التحقق منه
   * @returns {Object|null} - بيانات الوكيل أو null
   */
  static async verifyAgentToken(token) {
    try {
      // التحقق من التوكن باستخدام JWT
      const decoded = jwt.verify(token, JWT_SECRET)

      // التحقق من وجود الجلسة في قاعدة البيانات
      const session = await prisma.agentSession.findFirst({
        where: {
          token,
          isActive: true,
          expiresAt: {
            gt: new Date()
          }
        },
        include: {
          agent: true
        }
      })

      if (!session) {
        return null
      }

      // تحديث آخر نشاط
      await prisma.agentSession.update({
        where: { id: session.id },
        data: { updatedAt: new Date() }
      })

      return {
        agentId: session.agent.id,
        agentName: session.agent.agentName,
        agencyType: session.agent.agencyType,
        sessionId: session.id,
        agent: session.agent
      }
    } catch (error) {
      console.error('Error verifying agent token:', error)
      return null
    }
  }

  /**
   * إلغاء تفعيل التوكن
   * @param {string} token - التوكن المراد إلغاء تفعيله
   * @returns {boolean} - نجح الإلغاء أم لا
   */
  static async revokeAgentToken(token) {
    try {
      const result = await prisma.agentSession.updateMany({
        where: { token },
        data: { isActive: false }
      })

      return result.count > 0
    } catch (error) {
      console.error('Error revoking agent token:', error)
      return false
    }
  }

  /**
   * إلغاء تفعيل جميع جلسات الوكيل
   * @param {number} agentId - رقم الوكيل
   * @returns {number} - عدد الجلسات المُلغاة
   */
  static async revokeAllAgentTokens(agentId) {
    try {
      const result = await prisma.agentSession.updateMany({
        where: {
          agentId,
          isActive: true
        },
        data: { isActive: false }
      })

      return result.count
    } catch (error) {
      console.error('Error revoking all agent tokens:', error)
      return 0
    }
  }

  /**
   * تنظيف الجلسات المنتهية الصلاحية
   * @returns {number} - عدد الجلسات المحذوفة
   */
  static async cleanupExpiredSessions() {
    try {
      const result = await prisma.agentSession.deleteMany({
        where: {
          OR: [
            { expiresAt: { lt: new Date() } },
            { isActive: false }
          ]
        }
      })

      console.log(`Cleaned up ${result.count} expired sessions`)
      return result.count
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error)
      return 0
    }
  }

  /**
   * الحصول على إحصائيات الجلسات النشطة
   * @returns {Object} - إحصائيات الجلسات
   */
  static async getSessionStats() {
    try {
      const [activeSessions, totalSessions, expiredSessions] = await Promise.all([
        prisma.agentSession.count({
          where: {
            isActive: true,
            expiresAt: { gt: new Date() }
          }
        }),
        prisma.agentSession.count(),
        prisma.agentSession.count({
          where: {
            OR: [
              { expiresAt: { lt: new Date() } },
              { isActive: false }
            ]
          }
        })
      ])

      return {
        activeSessions,
        totalSessions,
        expiredSessions,
        cleanupNeeded: expiredSessions > 0
      }
    } catch (error) {
      console.error('Error getting session stats:', error)
      return {
        activeSessions: 0,
        totalSessions: 0,
        expiredSessions: 0,
        cleanupNeeded: false
      }
    }
  }

  /**
   * إنشاء توكن عشوائي آمن
   * @param {number} length - طول التوكن
   * @returns {string} - التوكن العشوائي
   */
  static generateSecureToken(length = 32) {
    return crypto.randomBytes(length).toString('hex')
  }

  /**
   * التحقق من قوة التوكن
   * @param {string} token - التوكن المراد فحصه
   * @returns {boolean} - قوي أم لا
   */
  static isStrongToken(token) {
    // يجب أن يكون التوكن 8 أحرف على الأقل ويحتوي على أحرف وأرقام
    const minLength = 8
    const hasLetters = /[a-zA-Z]/.test(token)
    const hasNumbers = /[0-9]/.test(token)

    return token.length >= minLength && hasLetters && hasNumbers
  }
}

// تشغيل تنظيف تلقائي كل ساعة
setInterval(async () => {
  try {
    await TokenManager.cleanupExpiredSessions()
  } catch (error) {
    console.error('Auto cleanup error:', error)
  }
}, 60 * 60 * 1000) // كل ساعة

module.exports = TokenManager
