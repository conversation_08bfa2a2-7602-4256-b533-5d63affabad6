/**
 * فحص بيانات العملاء في قاعدة البيانات
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function checkClientsData() {
  console.log('🔍 فحص بيانات العملاء في قاعدة البيانات...\n');

  try {
    // جلب جميع العملاء
    const clients = await prisma.client.findMany({
      select: {
        id: true,
        clientCode: true,
        clientName: true,
        password: true,
        status: true,
        appName: true,
        ipAddress: true,
        token: true
      }
    });

    console.log(`📊 إجمالي العملاء: ${clients.length}\n`);

    for (const client of clients) {
      console.log(`🏢 العميل ${client.id}:`);
      console.log(`   📝 رمز العميل: ${client.clientCode}`);
      console.log(`   👤 اسم العميل: ${client.clientName}`);
      console.log(`   📱 التطبيق: ${client.appName || 'غير محدد'}`);
      console.log(`   🌐 عنوان IP: ${client.ipAddress || 'غير محدد'}`);
      console.log(`   📊 الحالة: ${client.status}`);
      console.log(`   🎫 التوكن: ${client.token || 'غير محدد'}`);
      console.log(`   🔐 كلمة المرور (مشفرة): ${client.password?.substring(0, 20)}...`);
      
      // اختبار كلمات المرور المحتملة
      const possiblePasswords = ['Hash2020@', '112223333', 'hash2020', 'Hash2020', '123456'];
      
      for (const testPassword of possiblePasswords) {
        try {
          const isValid = await bcrypt.compare(testPassword, client.password);
          if (isValid) {
            console.log(`   ✅ كلمة المرور الصحيحة: ${testPassword}`);
            break;
          }
        } catch (error) {
          // تجاهل الأخطاء
        }
      }
      
      console.log('');
    }

    // اختبار تسجيل دخول العميل 1001
    console.log('🧪 اختبار تسجيل دخول العميل 1001:');
    const client1001 = clients.find(c => c.clientCode === 1001);
    
    if (client1001) {
      console.log(`   📝 العميل موجود: ${client1001.clientName}`);
      console.log(`   📊 الحالة: ${client1001.status}`);
      
      // اختبار كلمات مرور مختلفة
      const passwords = ['Hash2020@', '112223333', 'hash2020', 'Hash2020', '123456', 'password'];
      
      for (const password of passwords) {
        try {
          const isValid = await bcrypt.compare(password, client1001.password);
          console.log(`   🔐 كلمة المرور "${password}": ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
        } catch (error) {
          console.log(`   🔐 كلمة المرور "${password}": ❌ خطأ في المقارنة`);
        }
      }
    } else {
      console.log('   ❌ العميل 1001 غير موجود');
    }

    console.log('\n🧪 اختبار تسجيل دخول العميل 1000:');
    const client1000 = clients.find(c => c.clientCode === 1000);
    
    if (client1000) {
      console.log(`   📝 العميل موجود: ${client1000.clientName}`);
      console.log(`   📊 الحالة: ${client1000.status}`);
      
      // اختبار كلمات مرور مختلفة
      const passwords = ['112223333', 'Hash2020@', 'hash2020', 'Hash2020', '123456', 'password'];
      
      for (const password of passwords) {
        try {
          const isValid = await bcrypt.compare(password, client1000.password);
          console.log(`   🔐 كلمة المرور "${password}": ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
        } catch (error) {
          console.log(`   🔐 كلمة المرور "${password}": ❌ خطأ في المقارنة`);
        }
      }
    } else {
      console.log('   ❌ العميل 1000 غير موجود');
    }

  } catch (error) {
    console.error('❌ خطأ في فحص بيانات العملاء:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkClientsData().catch(console.error);
