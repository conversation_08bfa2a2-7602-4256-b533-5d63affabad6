/**
 * الصلاحيات الافتراضية للمستخدمين
 */

// صلاحيات الأدمن الكاملة
const adminPermissions = {
  isAdmin: true,
  users: {
    read: true,
    create: true,
    update: true,
    delete: true
  },
  clients: {
    read: true,
    create: true,
    update: true,
    delete: true
  },
  agents: {
    read: true,
    create: true,
    update: true,
    delete: true
  },
  dataRecords: {
    read: true,
    create: true,
    update: true,
    delete: true
  },
  security: {
    read: true,
    create: true,
    update: true,
    delete: true
  }
};

// صلاحيات المستخدم العادي (قراءة فقط)
const userPermissions = {
  isAdmin: false,
  users: {
    read: false,
    create: false,
    update: false,
    delete: false
  },
  clients: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  agents: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  dataRecords: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  security: {
    read: false,
    create: false,
    update: false,
    delete: false
  }
};

// صلاحيات مدير العملاء
const clientManagerPermissions = {
  isAdmin: false,
  users: {
    read: false,
    create: false,
    update: false,
    delete: false
  },
  clients: {
    read: true,
    create: true,
    update: true,
    delete: false
  },
  agents: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  dataRecords: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  security: {
    read: false,
    create: false,
    update: false,
    delete: false
  }
};

// صلاحيات مدير الوكلاء
const agentManagerPermissions = {
  isAdmin: false,
  users: {
    read: false,
    create: false,
    update: false,
    delete: false
  },
  clients: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  agents: {
    read: true,
    create: true,
    update: true,
    delete: false
  },
  dataRecords: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  security: {
    read: false,
    create: false,
    update: false,
    delete: false
  }
};

// صلاحيات مدير البيانات
const dataManagerPermissions = {
  isAdmin: false,
  users: {
    read: false,
    create: false,
    update: false,
    delete: false
  },
  clients: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  agents: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  dataRecords: {
    read: true,
    create: true,
    update: true,
    delete: true
  },
  security: {
    read: false,
    create: false,
    update: false,
    delete: false
  }
};

// صلاحيات مدير الأمان
const securityManagerPermissions = {
  isAdmin: false,
  users: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  clients: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  agents: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  dataRecords: {
    read: true,
    create: false,
    update: false,
    delete: false
  },
  security: {
    read: true,
    create: true,
    update: true,
    delete: false
  }
};

// قوالب الصلاحيات المحددة مسبقاً
const permissionTemplates = {
  admin: adminPermissions,
  user: userPermissions,
  clientManager: clientManagerPermissions,
  agentManager: agentManagerPermissions,
  dataManager: dataManagerPermissions,
  securityManager: securityManagerPermissions
};

/**
 * الحصول على صلاحيات حسب النوع
 */
function getPermissionsByType(type) {
  return permissionTemplates[type] || userPermissions;
}

/**
 * التحقق من صلاحية محددة
 */
function hasPermission(userPermissions, resource, action) {
  // الأدمن له كل الصلاحيات
  if (userPermissions.isAdmin) {
    return true;
  }

  // التحقق من الصلاحية المحددة
  return userPermissions[resource] && userPermissions[resource][action];
}

/**
 * الحصول على قائمة الموارد المتاحة
 */
function getAvailableResources() {
  return ['users', 'clients', 'agents', 'dataRecords', 'security'];
}

/**
 * الحصول على قائمة الأعمال المتاحة
 */
function getAvailableActions() {
  return ['read', 'create', 'update', 'delete'];
}

/**
 * إنشاء صلاحيات مخصصة
 */
function createCustomPermissions(permissions) {
  const defaultPerms = JSON.parse(JSON.stringify(userPermissions));
  
  // دمج الصلاحيات المخصصة مع الافتراضية
  Object.keys(permissions).forEach(resource => {
    if (defaultPerms[resource]) {
      Object.keys(permissions[resource]).forEach(action => {
        defaultPerms[resource][action] = permissions[resource][action];
      });
    }
  });

  return defaultPerms;
}

module.exports = {
  adminPermissions,
  userPermissions,
  clientManagerPermissions,
  agentManagerPermissions,
  dataManagerPermissions,
  securityManagerPermissions,
  permissionTemplates,
  getPermissionsByType,
  hasPermission,
  getAvailableResources,
  getAvailableActions,
  createCustomPermissions
};
