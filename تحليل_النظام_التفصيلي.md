# 📊 التحليل التفصيلي لنظام إدارة العملاء والوكلاء

## 🎯 نظرة عامة على النظام

### نوع النظام
- **نظام إدارة العملاء والوكلاء** (Client & Agent Management System)
- **نظام يمني** للاتصالات والخدمات المالية
- **نظام ويب** متعدد المستخدمين مع صلاحيات متقدمة

### الهدف من النظام
- إدارة العملاء ومعلوماتهم الشخصية والمالية
- إدارة الوكلاء وأنواع الوكالات المختلفة
- تتبع العمليات والمعاملات المالية
- نظام أمان متقدم مع مصادقة الأجهزة

---

## 🏗️ هيكل النظام العام

```
C:\yemclinet\
├── client\                 # الواجهة الأمامية (React)
├── server\                 # الخادم (Node.js)
├── fonts\                  # الخطوط العربية
└── themeforest\           # قالب التصميم Lucid
```

---

## 💻 التقنيات المستخدمة

### Frontend (الواجهة الأمامية)
- **React.js 18** - مكتبة JavaScript للواجهات
- **Material-UI (MUI) 5** - مكتبة التصميم والمكونات
- **Vite 4** - أداة البناء والتطوير السريع
- **React Query** - إدارة البيانات والـ API calls
- **React Hook Form** - إدارة النماذج والتحقق
- **Yup** - مكتبة التحقق من صحة البيانات
- **React Router** - التنقل بين الصفحات
- **Notistack** - إشعارات المستخدم

### Backend (الخادم)
- **Node.js 18+** - بيئة تشغيل JavaScript
- **Express.js 4** - إطار عمل الخادم
- **Prisma 5** - ORM لقاعدة البيانات
- **JWT** - نظام المصادقة والتوكن
- **bcryptjs** - تشفير كلمات المرور
- **express-validator** - التحقق من البيانات
- **cors** - إدارة الطلبات عبر النطاقات
- **helmet** - أمان الخادم

### قاعدة البيانات
- **PostgreSQL 15** - قاعدة بيانات علائقية
- **المنفذ:** 5432
- **اسم القاعدة:** yemclient_db
- **المستخدم:** postgres
- **كلمة المرور:** yemen123

---

## 🗄️ هيكل قاعدة البيانات

### جدول Users (المستخدمين)
```sql
- id: رقم المستخدم (Primary Key)
- username: اسم المستخدم الكامل
- loginName: اسم تسجيل الدخول
- password: كلمة المرور المشفرة
- deviceId: رقم الجهاز المصرح له
- device1: رقم الجهاز الجديد (نظام واحد)
- permissions: صلاحيات JSON
- createdAt: تاريخ الإنشاء
- updatedAt: تاريخ آخر تحديث
```

### جدول Clients (العملاء)
```sql
- id: رقم العميل (Primary Key)
- clientCode: رقم العميل التلقائي
- clientName: اسم العميل
- appName: اسم التطبيق
- cardNumber: رقم البطاقة (8-11 رقم)
- password: كلمة مرور العميل
- ipAddress: عنوان IP المصرح
- status: حالة العميل (1=نشط، 2=محظور)
- userId: رقم المستخدم المنشئ (Foreign Key)
- dataId: رقم البيانات (Foreign Key)
- createdAt: تاريخ الإنشاء
- updatedAt: تاريخ آخر تحديث
```

### جدول Agents (الوكلاء)
```sql
- id: رقم الوكيل (Primary Key)
- agentNumber: رقم الوكيل
- agentName: اسم الوكيل
- agentType: نوع الوكالة
- phoneNumber: رقم الهاتف
- address: العنوان
- status: حالة الوكيل (1=نشط، 2=محظور)
- createdAt: تاريخ الإنشاء
- updatedAt: تاريخ آخر تحديث
```

### جدول Data (البيانات/العمليات)
```sql
- id: رقم السجل (Primary Key)
- agentId: رقم الوكيل (Foreign Key)
- clientId: رقم العميل (Foreign Key)
- clientCode: رقم العميل
- clientPassword: كلمة مرور العميل
- clientIpAddress: عنوان IP العميل
- operationDate: تاريخ العملية
- operationStatus: حالة العملية (1=نجح، 0=فشل)
- agentReferenceNumber: رقم مرجع الوكيل
- createdAt: تاريخ الإنشاء
```

---

## 🔗 العلاقات بين الجداول (Database Relations)

### مخطط العلاقات العام
```
┌─────────────────┐
│     Users       │
│   (المستخدمين)   │
│                 │
│ • id (PK)       │
│ • username      │
│ • loginName     │
│ • password      │
│ • deviceId      │
│ • permissions   │
└─────────────────┘
         │
         │ 1:N (One-to-Many)
         │ userId (FK)
         ▼
┌─────────────────┐
│    Clients      │
│    (العملاء)     │
│                 │
│ • id (PK)       │
│ • clientCode    │
│ • clientName    │
│ • cardNumber    │
│ • userId (FK)   │
└─────────────────┘
         │
         │ 1:N (One-to-Many)
         │ clientId (FK)
         ▼
┌─────────────────┐      ┌─────────────────┐
│      Data       │      │     Agents      │
│ (البيانات/العمليات) │      │    (الوكلاء)    │
│                 │      │                 │
│ • id (PK)       │      │ • id (PK)       │
│ • agentId (FK)  │◄─────┤ • agentNumber   │
│ • clientId (FK) │      │ • agentName     │
│ • operationDate │      │ • agentType     │
│ • operationStatus│      │ • status        │
└─────────────────┘      └─────────────────┘
                                  │
                                  │ 1:N (One-to-Many)
                                  │ agentId (FK)
                                  ▲
                         ┌────────┘
                         │
                    Many-to-One
```

### ملخص العلاقات
- **Users → Clients:** مستخدم واحد يمكنه إنشاء عدة عملاء
- **Clients → Data:** عميل واحد يمكن أن تتم له عدة عمليات
- **Agents → Data:** وكيل واحد يمكنه إجراء عدة عمليات
- **Data:** جدول الوسط الذي يربط العملاء بالوكلاء عبر العمليات

### العلاقات التفصيلية

#### 1. علاقة Users → Clients (واحد إلى متعدد)
```sql
Users.id → Clients.userId (Foreign Key)
```
- **النوع:** One-to-Many (واحد إلى متعدد)
- **الوصف:** مستخدم واحد يمكنه إنشاء عدة عملاء
- **القيد:** userId مطلوب في جدول Clients
- **السلوك عند الحذف:** RESTRICT (منع حذف المستخدم إذا كان له عملاء)

**مثال:**
```javascript
// جلب مستخدم مع جميع عملائه
const userWithClients = await prisma.user.findUnique({
  where: { id: 1 },
  include: {
    clients: {
      select: {
        id: true,
        clientName: true,
        clientCode: true,
        status: true
      }
    }
  }
});
```

#### 2. علاقة Agents → Data (واحد إلى متعدد)
```sql
Agents.id → Data.agentId (Foreign Key)
```
- **النوع:** One-to-Many (واحد إلى متعدد)
- **الوصف:** وكيل واحد يمكنه إجراء عدة عمليات
- **القيد:** agentId مطلوب في جدول Data
- **السلوك عند الحذف:** CASCADE (حذف جميع العمليات عند حذف الوكيل)

**مثال:**
```javascript
// جلب وكيل مع جميع عملياته
const agentWithOperations = await prisma.agent.findUnique({
  where: { id: 1 },
  include: {
    data: {
      select: {
        id: true,
        operationDate: true,
        operationStatus: true,
        agentReferenceNumber: true
      }
    }
  }
});
```

#### 3. علاقة Clients → Data (واحد إلى متعدد)
```sql
Clients.id → Data.clientId (Foreign Key)
```
- **النوع:** One-to-Many (واحد إلى متعدد)
- **الوصف:** عميل واحد يمكن أن تتم له عدة عمليات
- **القيد:** clientId مطلوب في جدول Data
- **السلوك عند الحذف:** CASCADE (حذف جميع العمليات عند حذف العميل)

**مثال:**
```javascript
// جلب عميل مع جميع عملياته
const clientWithOperations = await prisma.client.findUnique({
  where: { id: 1 },
  include: {
    data: {
      select: {
        id: true,
        operationDate: true,
        operationStatus: true,
        agent: {
          select: {
            agentName: true,
            agentNumber: true
          }
        }
      }
    }
  }
});
```

### مخطط Prisma Schema للعلاقات

```prisma
model User {
  id          Int      @id @default(autoincrement())
  username    String
  loginName   String   @unique
  password    String
  deviceId    String?
  device1     String?
  permissions Json

  // العلاقة: مستخدم واحد → عدة عملاء
  clients     Client[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("users")
}

model Client {
  id          Int      @id @default(autoincrement())
  clientCode  Int      @unique
  clientName  String
  appName     String
  cardNumber  String   @unique
  password    String
  ipAddress   String
  status      Int      @default(1)

  // العلاقة: عميل ← ينتمي لمستخدم واحد
  userId      Int
  user        User     @relation(fields: [userId], references: [id], onDelete: Restrict)

  // العلاقة: عميل واحد → عدة عمليات
  data        Data[]

  dataId      Int?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("clients")
}

model Agent {
  id           Int      @id @default(autoincrement())
  agentNumber  String   @unique
  agentName    String
  agentType    String
  phoneNumber  String?
  address      String?
  status       Int      @default(1)

  // العلاقة: وكيل واحد → عدة عمليات
  data         Data[]

  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("agents")
}

model Data {
  id                    Int      @id @default(autoincrement())

  // العلاقة: عملية ← تنتمي لوكيل واحد
  agentId               Int
  agent                 Agent    @relation(fields: [agentId], references: [id], onDelete: Cascade)

  // العلاقة: عملية ← تنتمي لعميل واحد
  clientId              Int
  client                Client   @relation(fields: [clientId], references: [id], onDelete: Cascade)

  clientCode            String
  clientPassword        String
  clientIpAddress       String
  operationDate         DateTime @default(now())
  operationStatus       Int      // 1=نجح، 0=فشل
  agentReferenceNumber  String?

  createdAt             DateTime @default(now())

  @@map("data")
}
```

### استعلامات العلاقات المتقدمة

#### 1. جلب جميع البيانات مع العلاقات
```javascript
const allDataWithRelations = await prisma.data.findMany({
  include: {
    agent: {
      select: {
        agentName: true,
        agentNumber: true,
        agentType: true
      }
    },
    client: {
      select: {
        clientName: true,
        clientCode: true,
        user: {
          select: {
            username: true,
            loginName: true
          }
        }
      }
    }
  },
  orderBy: {
    operationDate: 'desc'
  }
});
```

#### 2. إحصائيات العمليات حسب الوكيل
```javascript
const agentStats = await prisma.agent.findMany({
  include: {
    _count: {
      select: {
        data: true
      }
    },
    data: {
      select: {
        operationStatus: true
      }
    }
  }
});
```

#### 3. إحصائيات العملاء حسب المستخدم
```javascript
const userStats = await prisma.user.findMany({
  include: {
    _count: {
      select: {
        clients: true
      }
    },
    clients: {
      select: {
        status: true,
        _count: {
          select: {
            data: true
          }
        }
      }
    }
  }
});
```

### قواعد العمل (Business Rules)

#### قواعد إنشاء العمليات
1. **العملية تتطلب وكيل وعميل موجودين**
2. **العميل يجب أن يكون نشط (status = 1)**
3. **الوكيل يجب أن يكون نشط (status = 1)**
4. **العملية تُنشأ فقط عبر API الوكلاء**
5. **لا يمكن إنشاء عمليات يدوياً من الواجهة**

#### قواعد الحذف
1. **لا يمكن حذف مستخدم له عملاء**
2. **حذف العميل يحذف جميع عملياته**
3. **حذف الوكيل يحذف جميع عملياته**
4. **حذف العملية لا يؤثر على العميل أو الوكيل**

#### قواعد التحديث
1. **لا يمكن تغيير userId للعميل بعد الإنشاء**
2. **لا يمكن تغيير agentId أو clientId للعملية**
3. **يمكن تحديث حالة العميل/الوكيل**
4. **يمكن تحديث معلومات العميل/الوكيل الأساسية**

### مؤشرات قاعدة البيانات (Database Indexes)

```sql
-- مؤشرات للأداء
CREATE INDEX idx_clients_user_id ON clients(userId);
CREATE INDEX idx_clients_status ON clients(status);
CREATE INDEX idx_clients_card_number ON clients(cardNumber);

CREATE INDEX idx_agents_status ON agents(status);
CREATE INDEX idx_agents_type ON agents(agentType);
CREATE INDEX idx_agents_number ON agents(agentNumber);

CREATE INDEX idx_data_agent_id ON data(agentId);
CREATE INDEX idx_data_client_id ON data(clientId);
CREATE INDEX idx_data_operation_date ON data(operationDate);
CREATE INDEX idx_data_operation_status ON data(operationStatus);

CREATE INDEX idx_users_login_name ON users(loginName);
CREATE INDEX idx_users_device_id ON users(deviceId);
```

### أمثلة على التقارير باستخدام العلاقات

#### 1. تقرير العمليات اليومية
```javascript
const dailyReport = await prisma.data.findMany({
  where: {
    operationDate: {
      gte: new Date(new Date().setHours(0, 0, 0, 0)),
      lt: new Date(new Date().setHours(23, 59, 59, 999))
    }
  },
  include: {
    agent: true,
    client: {
      include: {
        user: true
      }
    }
  }
});
```

#### 2. تقرير أداء الوكلاء
```javascript
const agentPerformance = await prisma.agent.findMany({
  include: {
    data: {
      where: {
        operationDate: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // آخر 30 يوم
        }
      }
    },
    _count: {
      select: {
        data: true
      }
    }
  }
});
```

#### 3. تقرير نشاط المستخدمين
```javascript
const userActivity = await prisma.user.findMany({
  include: {
    clients: {
      include: {
        data: {
          where: {
            operationDate: {
              gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // آخر 7 أيام
            }
          }
        }
      }
    }
  }
});
```

---

## 🔐 نظام الأمان والمصادقة

### مصادقة المستخدم
1. **تسجيل الدخول:** username + password + deviceId
2. **JWT Token:** يحتوي على معلومات المستخدم
3. **Device Validation:** التحقق من رقم الجهاز
4. **Session Management:** إدارة جلسات المستخدم

### نظام الصلاحيات
```json
{
  "isAdmin": true/false,
  "users": { "read": true, "write": true, "delete": true },
  "clients": { "read": true, "write": true, "delete": true },
  "agents": { "read": true, "write": true, "delete": true },
  "data": { "read": true, "write": true, "delete": true }
}
```

### حماية البيانات
- **تشفير كلمات المرور:** bcryptjs
- **حماية الطلبات:** CORS + Helmet
- **التحقق من البيانات:** express-validator
- **حماية من SQL Injection:** Prisma ORM

---

## 📁 هيكل مجلد Frontend (client/)

```
client/
├── public/                 # الملفات العامة
├── src/
│   ├── components/         # المكونات القابلة لإعادة الاستخدام
│   │   ├── forms/         # نماذج الإدخال
│   │   ├── layout/        # تخطيط الصفحة
│   │   └── common/        # مكونات مشتركة
│   ├── contexts/          # React Contexts
│   ├── hooks/             # Custom Hooks
│   ├── pages/             # صفحات التطبيق
│   ├── services/          # خدمات API
│   ├── utils/             # وظائف مساعدة
│   └── App.jsx           # المكون الرئيسي
├── package.json           # تبعيات المشروع
└── vite.config.js        # إعدادات Vite
```

### المكونات الرئيسية

#### AuthContext.jsx
- إدارة حالة المصادقة
- تخزين معلومات المستخدم
- إدارة التوكن والجلسة

#### Layout Components
- **Sidebar:** القائمة الجانبية
- **Header:** رأس الصفحة
- **Footer:** تذييل الصفحة

#### Form Components
- **ClientForm:** نموذج العملاء
- **AgentForm:** نموذج الوكلاء
- **UserForm:** نموذج المستخدمين

---

## 📁 هيكل مجلد Backend (server/)

```
server/
├── prisma/
│   ├── schema.prisma      # تعريف قاعدة البيانات
│   └── migrations/        # ملفات الهجرة
├── routes/                # مسارات API
│   ├── auth.js           # مصادقة المستخدم
│   ├── users.js          # إدارة المستخدمين
│   ├── clients.js        # إدارة العملاء
│   ├── agents.js         # إدارة الوكلاء
│   └── data.js           # إدارة البيانات
├── middleware/            # الوسطاء
│   └── auth.js           # مصادقة الطلبات
├── app.js                # تطبيق Express الرئيسي
└── package.json          # تبعيات المشروع
```

### API Endpoints

#### Authentication (/api/auth)
- `POST /login` - تسجيل الدخول
- `POST /logout` - تسجيل الخروج
- `GET /me` - معلومات المستخدم الحالي

#### Users (/api/users)
- `GET /` - جلب جميع المستخدمين
- `GET /:id` - جلب مستخدم محدد
- `POST /` - إنشاء مستخدم جديد
- `PUT /:id` - تحديث مستخدم
- `DELETE /:id` - حذف مستخدم

#### Clients (/api/clients)
- `GET /` - جلب جميع العملاء
- `GET /:id` - جلب عميل محدد
- `POST /` - إنشاء عميل جديد
- `PUT /:id` - تحديث عميل
- `DELETE /:id` - حذف عميل

#### Agents (/api/agents)
- `GET /` - جلب جميع الوكلاء
- `GET /:id` - جلب وكيل محدد
- `POST /` - إنشاء وكيل جديد
- `PUT /:id` - تحديث وكيل
- `DELETE /:id` - حذف وكيل

#### Data (/api/data)
- `GET /` - جلب جميع البيانات
- `GET /:id` - جلب سجل محدد
- `POST /` - إنشاء سجل جديد (عبر API فقط)

---

## 🌐 إعدادات الشبكة والنشر

### المنافذ
- **8080:** الخادم الداخلي (localhost)
- **3000:** الوصول الخارجي

### عناوين IP
- ****************:** الشبكة الداخلية
- **************:** شبكة فرعية
- *************:** العنوان الخارجي

### إعدادات الجدار الناري
- **منفذ 8080:** مفتوح للشبكة المحلية
- **منفذ 3000:** مفتوح للوصول الخارجي

---

## 🎨 التصميم والواجهة

### نظام التصميم
- **اتجاه النص:** من اليمين لليسار (RTL)
- **اللغة:** العربية بالكامل
- **الخط:** Khalid-Art-bold
- **القالب:** Lucid Vue.js HRMS

### ألوان النظام
- **الأساسي:** أزرق (#1976d2)
- **الثانوي:** رمادي (#757575)
- **النجاح:** أخضر (#4caf50)
- **الخطر:** أحمر (#f44336)

### مكونات الواجهة
- **الجداول:** عرض البيانات مع فلترة وبحث
- **النماذج:** إدخال وتحديث البيانات
- **الإشعارات:** تأكيد العمليات والأخطاء
- **المودال:** نوافذ منبثقة للتفاصيل

---

## 🔄 تدفق البيانات

### إنشاء عميل جديد
1. المستخدم يملأ نموذج العميل
2. التحقق من صحة البيانات (Frontend)
3. إرسال البيانات مع userId للخادم
4. التحقق من المصادقة والصلاحيات
5. التحقق من عدم تكرار رقم البطاقة
6. إنشاء رقم عميل تلقائي
7. حفظ البيانات في قاعدة البيانات
8. إرجاع البيانات المحفوظة
9. تحديث الواجهة وإظهار إشعار النجاح

### تحديث بيانات عميل
1. جلب البيانات الحالية للعميل
2. عرض النموذج مع البيانات المحملة
3. المستخدم يعدل البيانات المطلوبة
4. التحقق من صحة البيانات
5. إرسال البيانات المحدثة (بدون userId)
6. التحقق من المصادقة والصلاحيات
7. تحديث البيانات في قاعدة البيانات
8. إرجاع البيانات المحدثة
9. تحديث الواجهة

---

## 📊 إدارة الحالة (State Management)

### React Query
- **جلب البيانات:** useQuery
- **تحديث البيانات:** useMutation
- **التخزين المؤقت:** Automatic caching
- **إعادة التحميل:** Background refetching

### React Context
- **AuthContext:** حالة المصادقة والمستخدم
- **ThemeContext:** إعدادات التصميم
- **LanguageContext:** إعدادات اللغة

### Local Storage
- **التوكن:** حفظ JWT token
- **إعدادات المستخدم:** تفضيلات الواجهة
- **بيانات مؤقتة:** تحسين الأداء

---

## 🛠️ أدوات التطوير

### Frontend Development
```bash
cd client
npm install          # تثبيت التبعيات
npm run dev          # تشغيل وضع التطوير
npm run build        # بناء الإنتاج
npm run preview      # معاينة البناء
```

### Backend Development
```bash
cd server
npm install          # تثبيت التبعيات
npm start            # تشغيل الخادم
npm run dev          # تشغيل وضع التطوير
npx prisma migrate   # تطبيق تغييرات قاعدة البيانات
npx prisma studio    # واجهة إدارة قاعدة البيانات
```

### Database Management
```bash
# الاتصال بقاعدة البيانات
psql -h localhost -p 5432 -U postgres -d yemclient_db

# نسخ احتياطية
pg_dump -h localhost -p 5432 -U postgres yemclient_db > backup.sql

# استعادة النسخة الاحتياطية
psql -h localhost -p 5432 -U postgres yemclient_db < backup.sql
```

---

## 🔧 ملفات الإعداد المهمة

### package.json (Frontend)
```json
{
  "name": "yemclient-frontend",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "react": "^18.2.0",
    "@mui/material": "^5.14.0",
    "react-query": "^3.39.0"
  }
}
```

### package.json (Backend)
```json
{
  "name": "yemclient-backend",
  "scripts": {
    "start": "node app.js",
    "dev": "nodemon app.js"
  },
  "dependencies": {
    "express": "^4.18.0",
    "prisma": "^5.0.0",
    "jsonwebtoken": "^9.0.0"
  }
}
```

### schema.prisma
```prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          Int      @id @default(autoincrement())
  username    String
  loginName   String   @unique
  password    String
  deviceId    String?
  device1     String?
  permissions Json
  clients     Client[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}
```

---

## 📈 مؤشرات الأداء

### Frontend Performance
- **First Contentful Paint:** < 1.5s
- **Largest Contentful Paint:** < 2.5s
- **Bundle Size:** ~500KB gzipped
- **Code Splitting:** تحميل تدريجي للصفحات

### Backend Performance
- **Response Time:** < 200ms للطلبات البسيطة
- **Database Queries:** محسنة مع Prisma
- **Memory Usage:** ~100MB في الوضع العادي
- **Concurrent Users:** يدعم 100+ مستخدم متزامن

### Database Performance
- **Indexes:** على الحقول المهمة
- **Connection Pooling:** إدارة الاتصالات
- **Query Optimization:** استعلامات محسنة
- **Backup Strategy:** نسخ احتياطية يومية

---

## 🚀 خطة التطوير المستقبلية

### المرحلة الأولى (قريباً)
- [ ] تحسين نظام التقارير
- [ ] إضافة إشعارات فورية
- [ ] تحسين واجهة الموبايل
- [ ] إضافة نظام النسخ الاحتياطي التلقائي

### المرحلة الثانية (متوسط المدى)
- [ ] API للتطبيقات الخارجية
- [ ] نظام الأرشفة التلقائية
- [ ] تحليلات متقدمة ولوحة معلومات
- [ ] نظام الإشعارات عبر البريد الإلكتروني

### المرحلة الثالثة (طويل المدى)
- [ ] تطبيق موبايل (React Native)
- [ ] نظام الذكاء الاصطناعي للتحليل
- [ ] التكامل مع أنظمة خارجية
- [ ] نظام إدارة المحتوى

---

## 📞 معلومات الدعم الفني

### بيانات الاتصال
- **المطور:** فريق التطوير اليمني
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +967-xxx-xxx-xxx

### ساعات الدعم
- **الأحد - الخميس:** 8:00 ص - 5:00 م
- **الجمعة - السبت:** حسب الحاجة
- **الطوارئ:** 24/7

### مستويات الدعم
1. **المستوى الأول:** مشاكل المستخدمين العادية
2. **المستوى الثاني:** مشاكل تقنية متقدمة
3. **المستوى الثالث:** تطوير وتحديثات النظام

---

---

## 🔍 تفاصيل تقنية إضافية

### متغيرات البيئة (.env)
```bash
# قاعدة البيانات
DATABASE_URL="postgresql://postgres:yemen123@localhost:5432/yemclient_db"

# JWT
JWT_SECRET="your-secret-key-here"
JWT_EXPIRES_IN="24h"

# الخادم
PORT=8080
NODE_ENV="production"

# CORS
ALLOWED_ORIGINS="http://localhost:3000,http://***********:3000"
```

### هيكل الاستجابة API
```json
{
  "success": true,
  "data": {
    "id": 1,
    "clientName": "عميل تجريبي",
    "clientCode": 1001,
    "status": 1,
    "user": {
      "id": 1,
      "username": "محمد الحاشدي"
    }
  },
  "message": "تم إنشاء العميل بنجاح",
  "timestamp": "2024-12-28T22:30:21.518Z"
}
```

### رموز الأخطاء
- **400:** Bad Request - بيانات غير صحيحة
- **401:** Unauthorized - غير مصرح
- **403:** Forbidden - ممنوع الوصول
- **404:** Not Found - غير موجود
- **409:** Conflict - تضارب في البيانات
- **500:** Internal Server Error - خطأ في الخادم

### أنواع الوكالات
1. **وكيل يمن موبايل** - خدمات يمن موبايل
2. **وكيل سبافون** - خدمات سبافون
3. **وكيل يو** - خدمات شركة يو
4. **وكيل واي** - خدمات شركة واي
5. **وكيل خدمات البريد** - الخدمات البريدية
6. **وكيل العاب وبطائق** - الألعاب والبطائق
7. **اخرى** - خدمات أخرى

### حالات النظام
#### حالات المستخدم
- **1:** نشط
- **0:** غير نشط

#### حالات العميل/الوكيل
- **1:** نشط
- **2:** محظور

#### حالات العملية
- **1:** نجحت
- **0:** فشلت

### نصائح للتطوير
1. **استخدم Prisma Studio** لإدارة قاعدة البيانات بصرياً
2. **فعل Hot Reload** في وضع التطوير
3. **استخدم React DevTools** لتتبع المكونات
4. **راقب Network Tab** لتتبع طلبات API
5. **استخدم Console.log** بذكاء للتشخيص
6. **اختبر على متصفحات مختلفة**
7. **احتفظ بنسخ احتياطية منتظمة**

### أدوات مفيدة للتطوير
- **Postman:** اختبار API endpoints
- **pgAdmin:** إدارة قاعدة بيانات PostgreSQL
- **VS Code:** محرر الكود المفضل
- **Git:** نظام إدارة الإصدارات
- **Chrome DevTools:** أدوات تطوير المتصفح

---

*تم إنشاء هذا التحليل في: ديسمبر 2024*
*آخر تحديث: 28 ديسمبر 2024*
*الإصدار: 1.0*
