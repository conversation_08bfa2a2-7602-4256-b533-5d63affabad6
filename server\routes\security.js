const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, checkPermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على إحصائيات الأمان
router.get('/stats', authenticateToken, checkPermission('security', 'read'), async (req, res) => {
  try {
    // حساب إحصائيات محاولات تسجيل الدخول
    const [
      totalAttempts,
      successfulLogins,
      failedLogins,
      uniqueIPs,
      activeDevices
    ] = await Promise.all([
      prisma.loginAttempt.count(),
      prisma.loginAttempt.count({ where: { success: true } }),
      prisma.loginAttempt.count({ where: { success: false } }),
      prisma.loginAttempt.groupBy({
        by: ['ipAddress'],
        _count: { ipAddress: true }
      }),
      prisma.user.count({ where: { deviceId: { not: null } } })
    ]);

    // حساب المحاولات المشبوهة (أكثر من 5 محاولات فاشلة من نفس IP)
    const suspiciousIPs = await prisma.loginAttempt.groupBy({
      by: ['ipAddress'],
      where: { success: false },
      _count: { ipAddress: true },
      having: { ipAddress: { _count: { gt: 5 } } }
    });

    // آخر فحص أمني (محاكاة)
    const lastSecurityScan = new Date().toISOString();

    res.json({
      successfulLogins,
      failedLogins,
      totalAttempts,
      blockedIPs: suspiciousIPs.length,
      activeDevices,
      suspiciousActivity: suspiciousIPs.length,
      uniqueIPs: uniqueIPs.length,
      lastSecurityScan
    });

  } catch (error) {
    console.error('Security stats error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على محاولات تسجيل الدخول
router.get('/login-attempts', authenticateToken, checkPermission('security', 'read'), async (req, res) => {
  try {
    const { page = 1, limit = 20, success, userType } = req.query;
    const skip = (page - 1) * limit;

    const where = {};

    if (success !== undefined) {
      where.success = success === 'true';
    }

    if (userType) {
      where.userType = userType;
    }

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        orderBy: { timestamp: 'desc' },
        include: {
          user: {
            select: { id: true, username: true, loginName: true }
          },
          agent: {
            select: { id: true, agentName: true, agencyName: true }
          }
        }
      }),
      prisma.loginAttempt.count({ where })
    ]);

    // تحويل البيانات للتنسيق المطلوب
    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id,
      type: attempt.success ? 'success' : 'failure',
      username: attempt.user?.loginName || attempt.agent?.agentName || 'Unknown',
      ip: attempt.ipAddress,
      userAgent: 'Browser', // يمكن إضافة هذا للجدول لاحقاً
      timestamp: attempt.timestamp,
      deviceId: attempt.deviceId,
      reason: attempt.success ? null : 'Authentication failed'
    }));

    res.json({
      attempts: formattedAttempts,
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    console.error('Login attempts error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على الأجهزة المشبوهة
router.get('/suspicious-devices', authenticateToken, checkPermission('security', 'read'), async (req, res) => {
  try {
    // البحث عن الأجهزة التي لها محاولات دخول فاشلة متعددة
    const suspiciousDevices = await prisma.loginAttempt.groupBy({
      by: ['deviceId', 'ipAddress'],
      where: { 
        success: false,
        deviceId: { not: null }
      },
      _count: { deviceId: true },
      having: { deviceId: { _count: { gt: 3 } } }
    });

    res.json(suspiciousDevices);

  } catch (error) {
    console.error('Suspicious devices error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// حظر IP معين
router.post('/block-ip', authenticateToken, checkPermission('security', 'manage'), async (req, res) => {
  try {
    const { ipAddress, reason } = req.body;

    if (!ipAddress) {
      return res.status(400).json({ error: 'IP address is required' });
    }

    // هنا يمكن إضافة جدول للـ IPs المحظورة
    // حالياً سنرجع نجاح العملية فقط
    res.json({ 
      message: 'IP blocked successfully',
      ipAddress,
      reason: reason || 'Security violation'
    });

  } catch (error) {
    console.error('Block IP error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
