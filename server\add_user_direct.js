const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function addUser() {
    try {
        console.log('🔍 الاتصال بقاعدة البيانات...');
        
        // تشفير كلمة المرور
        const password = 'Hash8080';
        const hashedPassword = await bcrypt.hash(password, 12);
        
        console.log('🔐 تم تشفير كلمة المرور');
        
        // التحقق من عدم وجود المستخدم
        const existingUser = await prisma.users.findUnique({
            where: { login_name: 'hash8080' }
        });
        
        if (existingUser) {
            console.log('⚠️ المستخدم موجود مسبقاً!');
            console.log('معلومات المستخدم الحالي:');
            console.log(`- ID: ${existingUser.user_id}`);
            console.log(`- الاسم: ${existingUser.username}`);
            console.log(`- اسم المستخدم: ${existingUser.login_name}`);
            return;
        }
        
        // إنشاء المستخدم الجديد
        const newUser = await prisma.users.create({
            data: {
                username: 'محمد الحاشدي',
                device_id: null,
                login_name: 'hash8080',
                password: hashedPassword,
                permissions: {
                    isAdmin: true,
                    clients: {
                        create: true,
                        read: true,
                        update: true,
                        delete: true
                    },
                    agents: {
                        create: true,
                        read: true,
                        update: true,
                        delete: true
                    },
                    users: {
                        create: true,
                        read: true,
                        update: true,
                        delete: true
                    },
                    dashboard: {
                        read: true
                    },
                    security: {
                        read: true,
                        manage: true
                    }
                },
                is_active: true
            }
        });
        
        console.log('✅ تم إنشاء المستخدم بنجاح!');
        console.log('');
        console.log('📋 معلومات المستخدم الجديد:');
        console.log(`- ID: ${newUser.user_id}`);
        console.log(`- الاسم: ${newUser.username}`);
        console.log(`- اسم المستخدم: ${newUser.login_name}`);
        console.log(`- نشط: ${newUser.is_active ? 'نعم' : 'لا'}`);
        console.log(`- تاريخ الإنشاء: ${newUser.created_at}`);
        console.log('');
        console.log('🔑 بيانات تسجيل الدخول:');
        console.log('- اسم المستخدم: hash8080');
        console.log('- كلمة المرور: Hash8080');
        console.log('- الصلاحيات: أدمن كامل الصلاحيات');
        
        // عرض جميع المستخدمين
        const allUsers = await prisma.users.findMany({
            select: {
                user_id: true,
                username: true,
                login_name: true,
                is_active: true,
                created_at: true,
                permissions: true
            },
            orderBy: {
                created_at: 'desc'
            }
        });
        
        console.log('');
        console.log('👥 جميع المستخدمين في النظام:');
        allUsers.forEach(user => {
            const isAdmin = user.permissions?.isAdmin ? 'أدمن' : 'مستخدم عادي';
            console.log(`- ${user.username} (${user.login_name}) - ${isAdmin} - ${user.is_active ? 'نشط' : 'غير نشط'}`);
        });
        
    } catch (error) {
        console.error('❌ خطأ في إنشاء المستخدم:', error);
        
        if (error.code === 'P2002') {
            console.log('⚠️ اسم المستخدم موجود مسبقاً');
        }
    } finally {
        await prisma.$disconnect();
        console.log('');
        console.log('🔌 تم قطع الاتصال بقاعدة البيانات');
    }
}

// تشغيل الدالة
addUser();
