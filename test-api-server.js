/**
 * اختبار خادم API
 */

async function testAPIServer() {
  console.log('🔧 اختبار خادم API...\n');

  try {
    // اختبار Health Check
    console.log('1️⃣ اختبار Health Check:');
    const healthResponse = await fetch('http://localhost:8080/health');
    console.log(`   📡 Status: ${healthResponse.status}`);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('   ✅ Health Check يعمل!');
      console.log(`   📊 Status: ${healthData.status}`);
      console.log(`   💾 Database: ${healthData.database}`);
      console.log(`   🖥️ Server: ${healthData.server}`);
    } else {
      console.log('   ❌ Health Check فشل!');
    }
    console.log('');

    // اختبار External Health Check
    console.log('2️⃣ اختبار External Health Check:');
    const extHealthResponse = await fetch('http://localhost:8080/api/external/health');
    console.log(`   📡 Status: ${extHealthResponse.status}`);
    
    if (extHealthResponse.ok) {
      const extHealthData = await extHealthResponse.json();
      console.log('   ✅ External Health Check يعمل!');
      console.log(`   📊 Status: ${extHealthData.status}`);
      console.log(`   🔢 Version: ${extHealthData.data?.version}`);
    } else {
      console.log('   ❌ External Health Check فشل!');
    }
    console.log('');

    // اختبار Verify Direct
    console.log('3️⃣ اختبار Verify Direct:');
    const verifyResponse = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      })
    });

    console.log(`   📡 Status: ${verifyResponse.status}`);
    
    if (verifyResponse.ok) {
      const verifyData = await verifyResponse.json();
      console.log('   ✅ Verify Direct يعمل!');
      console.log(`   📊 Status: ${verifyData.status}`);
      console.log(`   👤 Client Status: ${verifyData.client_status}`);
    } else {
      const errorData = await verifyResponse.json();
      console.log('   ❌ Verify Direct فشل!');
      console.log(`   📝 Error: ${errorData.status}`);
    }
    console.log('');

    // اختبار Dashboard Stats
    console.log('4️⃣ اختبار Dashboard Stats:');
    const statsResponse = await fetch('http://localhost:8080/api/dashboard/stats');
    console.log(`   📡 Status: ${statsResponse.status}`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('   ✅ Dashboard Stats يعمل!');
      console.log(`   👤 المستخدمين: ${statsData.totalUsers}`);
      console.log(`   👥 العملاء: ${statsData.totalClients}`);
      console.log(`   🤝 الوكلاء: ${statsData.totalAgents}`);
      console.log(`   📊 سجلات البيانات: ${statsData.totalDataRecords}`);
    } else {
      console.log('   ❌ Dashboard Stats فشل!');
    }
    console.log('');

    // اختبار Data Records
    console.log('5️⃣ اختبار Data Records:');
    const dataResponse = await fetch('http://localhost:8080/api/data-records?page=1&limit=5');
    console.log(`   📡 Status: ${dataResponse.status}`);
    
    if (dataResponse.ok) {
      const dataData = await dataResponse.json();
      console.log('   ✅ Data Records يعمل!');
      console.log(`   📊 إجمالي السجلات: ${dataData.total}`);
      console.log(`   📋 السجلات في هذه الصفحة: ${dataData.dataRecords?.length}`);
      
      if (dataData.dataRecords && dataData.dataRecords.length > 0) {
        console.log(`   📋 أول سجل: ID ${dataData.dataRecords[0].id} - ${dataData.dataRecords[0].agentName}`);
      }
    } else {
      console.log('   ❌ Data Records فشل!');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار خادم API:');
    console.log('✅ Health Check: متاح');
    console.log('✅ External Health Check: متاح');
    console.log('✅ Verify Direct API: متاح');
    console.log('✅ Dashboard Stats: متاح');
    console.log('✅ Data Records: متاح');
    console.log('');
    console.log('🎉 خادم API يعمل بشكل مثالي!');
    console.log('🔧 المطورون يمكنهم استخدام النظام!');
    console.log('🏠 النظام الأساسي متاح!');

  } catch (error) {
    console.error('❌ خطأ في اختبار خادم API:', error.message);
  }
}

testAPIServer().catch(console.error);
