const fs = require('fs');
const path = require('path');

// ملف تسجيل الأمان
const securityLogFile = path.join(__dirname, '../logs/security.log');

/**
 * تسجيل أحداث الأمان
 */
function logSecurityEvent(type, message, ip, userAgent = '', additionalData = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    type,
    message,
    ip,
    userAgent,
    additionalData
  };
  
  const logLine = JSON.stringify(logEntry) + '\n';
  
  // كتابة في ملف الأمان
  fs.appendFileSync(securityLogFile, logLine);
  
  // طباعة في الكونسول
  console.log(`🚨 [SECURITY] ${type}: ${message} from ${ip}`);
}

/**
 * مراقبة محاولات الاختراق
 */
class SecurityMonitor {
  constructor() {
    this.suspiciousIPs = new Map();
    this.blockedIPs = new Set();
    this.loginAttempts = new Map();
    this.attackPatterns = new Map();
  }
  
  /**
   * تسجيل محاولة مشبوهة
   */
  recordSuspiciousActivity(ip, type, details) {
    const now = Date.now();
    
    if (!this.suspiciousIPs.has(ip)) {
      this.suspiciousIPs.set(ip, []);
    }
    
    this.suspiciousIPs.get(ip).push({
      type,
      details,
      timestamp: now
    });
    
    // تنظيف الأنشطة القديمة (أكثر من ساعة)
    const oneHourAgo = now - (60 * 60 * 1000);
    this.suspiciousIPs.set(ip, 
      this.suspiciousIPs.get(ip).filter(activity => activity.timestamp > oneHourAgo)
    );
    
    // فحص إذا كان IP يجب حظره
    this.checkForBlocking(ip);
    
    logSecurityEvent(type, details, ip);
  }
  
  /**
   * فحص إذا كان IP يجب حظره
   */
  checkForBlocking(ip) {
    const activities = this.suspiciousIPs.get(ip) || [];
    
    // حظر إذا كان هناك أكثر من 10 أنشطة مشبوهة في الساعة
    if (activities.length > 10) {
      this.blockIP(ip, 'Multiple suspicious activities');
    }
    
    // حظر إذا كان هناك أنماط هجوم متعددة
    const attackTypes = new Set(activities.map(a => a.type));
    if (attackTypes.size > 3) {
      this.blockIP(ip, 'Multiple attack patterns detected');
    }
  }
  
  /**
   * حظر IP
   */
  blockIP(ip, reason) {
    this.blockedIPs.add(ip);
    logSecurityEvent('IP_BLOCKED', `IP blocked: ${reason}`, ip);
    
    // إزالة الحظر بعد 24 ساعة
    setTimeout(() => {
      this.unblockIP(ip);
    }, 24 * 60 * 60 * 1000);
  }
  
  /**
   * إلغاء حظر IP
   */
  unblockIP(ip) {
    this.blockedIPs.delete(ip);
    logSecurityEvent('IP_UNBLOCKED', 'IP unblocked after timeout', ip);
  }
  
  /**
   * فحص إذا كان IP محظور
   */
  isBlocked(ip) {
    return this.blockedIPs.has(ip);
  }
  
  /**
   * تسجيل محاولة تسجيل دخول
   */
  recordLoginAttempt(ip, username, success) {
    const now = Date.now();
    const key = `${ip}:${username}`;
    
    if (!this.loginAttempts.has(key)) {
      this.loginAttempts.set(key, []);
    }
    
    this.loginAttempts.get(key).push({
      success,
      timestamp: now
    });
    
    // تنظيف المحاولات القديمة (أكثر من 15 دقيقة)
    const fifteenMinutesAgo = now - (15 * 60 * 1000);
    this.loginAttempts.set(key,
      this.loginAttempts.get(key).filter(attempt => attempt.timestamp > fifteenMinutesAgo)
    );
    
    // فحص محاولات فاشلة متتالية
    const attempts = this.loginAttempts.get(key);
    const failedAttempts = attempts.filter(a => !a.success);
    
    if (failedAttempts.length >= 5) {
      this.recordSuspiciousActivity(ip, 'BRUTE_FORCE', `Multiple failed login attempts for ${username}`);
    }
    
    logSecurityEvent(
      success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED',
      `Login attempt for ${username}`,
      ip,
      '',
      { username, success }
    );
  }
  
  /**
   * الحصول على إحصائيات الأمان
   */
  getSecurityStats() {
    return {
      suspiciousIPs: this.suspiciousIPs.size,
      blockedIPs: this.blockedIPs.size,
      totalLoginAttempts: Array.from(this.loginAttempts.values())
        .reduce((total, attempts) => total + attempts.length, 0),
      recentAttacks: this.getRecentAttacks()
    };
  }
  
  /**
   * الحصول على الهجمات الأخيرة
   */
  getRecentAttacks() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    const recentAttacks = [];
    
    for (const [ip, activities] of this.suspiciousIPs) {
      const recentActivities = activities.filter(a => a.timestamp > oneHourAgo);
      if (recentActivities.length > 0) {
        recentAttacks.push({
          ip,
          activities: recentActivities.length,
          types: [...new Set(recentActivities.map(a => a.type))]
        });
      }
    }
    
    return recentAttacks.sort((a, b) => b.activities - a.activities);
  }
  
  /**
   * تنظيف البيانات القديمة
   */
  cleanup() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);
    
    // تنظيف الأنشطة المشبوهة
    for (const [ip, activities] of this.suspiciousIPs) {
      const recentActivities = activities.filter(a => a.timestamp > oneHourAgo);
      if (recentActivities.length === 0) {
        this.suspiciousIPs.delete(ip);
      } else {
        this.suspiciousIPs.set(ip, recentActivities);
      }
    }
    
    // تنظيف محاولات تسجيل الدخول
    const fifteenMinutesAgo = now - (15 * 60 * 1000);
    for (const [key, attempts] of this.loginAttempts) {
      const recentAttempts = attempts.filter(a => a.timestamp > fifteenMinutesAgo);
      if (recentAttempts.length === 0) {
        this.loginAttempts.delete(key);
      } else {
        this.loginAttempts.set(key, recentAttempts);
      }
    }
  }
}

// إنشاء instance واحد للمراقبة
const securityMonitor = new SecurityMonitor();

// تنظيف دوري كل 10 دقائق
setInterval(() => {
  securityMonitor.cleanup();
}, 10 * 60 * 1000);

/**
 * middleware للفحص الأمني
 */
const securityCheck = (req, res, next) => {
  const ip = req.ip || req.connection?.remoteAddress || 'unknown';
  
  // فحص إذا كان IP محظور
  if (securityMonitor.isBlocked(ip)) {
    logSecurityEvent('BLOCKED_ACCESS', 'Blocked IP attempted access', ip);
    return res.status(403).json({ error: 'Access denied' });
  }
  
  next();
};

/**
 * middleware لتسجيل محاولات تسجيل الدخول
 */
const loginMonitor = (req, res, next) => {
  const originalSend = res.send;
  const ip = req.ip || req.connection?.remoteAddress || 'unknown';
  
  res.send = function(body) {
    // فحص إذا كان هذا طلب تسجيل دخول
    if (req.path.includes('/login') && req.method === 'POST') {
      const username = req.body?.loginName || req.body?.username || 'unknown';
      const success = res.statusCode === 200;
      
      securityMonitor.recordLoginAttempt(ip, username, success);
    }
    
    return originalSend.call(this, body);
  };
  
  next();
};

module.exports = {
  securityMonitor,
  logSecurityEvent,
  securityCheck,
  loginMonitor
};
