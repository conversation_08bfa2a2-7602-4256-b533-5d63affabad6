<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 نظام الوكلاء - التحقق من العملاء</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 20px auto;
            padding: 20px;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 28px;
        }
        
        .subtitle {
            margin: 10px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            border-right: 4px solid #ffc107;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #3498db;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        input, select, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 0;
            transition: all 0.3s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        }
        
        .result {
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
        }
        
        .success {
            background: #d4edda;
            color: #155724;
            border: 2px solid #c3e6cb;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 2px solid #f5c6cb;
        }
        
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 2px solid #bee5eb;
        }
        
        .loading {
            background: #fff3cd;
            color: #856404;
            border: 2px solid #ffeaa7;
        }
        
        .agent-card {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .agent-card:hover {
            border-color: #3498db;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .agent-card.selected {
            border-color: #e74c3c;
            background: #fdf2f2;
        }
        
        .agent-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .agent-card p {
            margin: 5px 0;
            font-size: 14px;
            color: #7f8c8d;
        }
        
        .client-tests {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .test-card {
            background: white;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .test-card:hover {
            border-color: #3498db;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .api-info {
            background: #2c3e50;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 نظام الوكلاء</h1>
            <p class="subtitle">التحقق من العملاء - الوكلاء فقط</p>
        </div>

        <div class="warning">
            ⚠️ تنبيه: الوصول للنظام مخصص للوكلاء فقط. العملاء لا يملكون حسابات للوصول المباشر.
        </div>

        <!-- اختيار الوكيل -->
        <div class="section">
            <h3>👤 اختيار الوكيل</h3>
            <p>اختر الوكيل الذي تريد استخدام حسابه للتحقق من العملاء:</p>
            
            <div class="agent-card" onclick="selectAgent('alghurasi')" id="agent-alghurasi">
                <h4>🏢 وكيل الغراسي</h4>
                <p><strong>اسم المستخدم:</strong> alghurasi</p>
                <p><strong>كلمة المرور:</strong> alghurasi123</p>
                <p><strong>الحالة:</strong> نشط ✅</p>
            </div>
            
            <div class="agent-card" onclick="selectAgent('almutarib')" id="agent-almutarib">
                <h4>🏢 وكيل المترب</h4>
                <p><strong>اسم المستخدم:</strong> almutarib</p>
                <p><strong>كلمة المرور:</strong> almutarib123</p>
                <p><strong>الحالة:</strong> نشط ✅</p>
            </div>
            
            <div class="form-group">
                <label>الوكيل المختار:</label>
                <input type="text" id="selectedAgent" value="لم يتم الاختيار" readonly>
            </div>
        </div>

        <!-- بيانات العميل للتحقق -->
        <div class="section">
            <h3>👥 بيانات العميل للتحقق</h3>
            <p>أدخل بيانات العميل الذي تريد التحقق من حالته:</p>
            
            <div class="form-group">
                <label>رمز العميل:</label>
                <input type="text" id="clientCode" value="1000" placeholder="أدخل رمز العميل">
            </div>
            <div class="form-group">
                <label>توكن العميل:</label>
                <input type="text" id="clientToken" value="ABC12345" placeholder="أدخل توكن العميل">
            </div>
        </div>

        <!-- اختبارات سريعة -->
        <div class="section">
            <h3>⚡ اختبارات سريعة للعملاء</h3>
            <div class="client-tests">
                <div class="test-card" onclick="quickTest('1000', 'ABC12345')">
                    <h4>عميل 1000</h4>
                    <p>توكن: ABC12345</p>
                    <p>متوقع: نشط</p>
                </div>
                <div class="test-card" onclick="quickTest('1001', 'XYZ67890')">
                    <h4>عميل 1001</h4>
                    <p>توكن: XYZ67890</p>
                    <p>متوقع: نشط</p>
                </div>
                <div class="test-card" onclick="quickTest('1002', 'DEF54321')">
                    <h4>عميل 1002</h4>
                    <p>توكن: DEF54321</p>
                    <p>متوقع: نشط</p>
                </div>
                <div class="test-card" onclick="quickTest('1000', 'WRONG')">
                    <h4>توكن خاطئ</h4>
                    <p>رمز: 1000</p>
                    <p>متوقع: خطأ</p>
                </div>
            </div>
        </div>

        <!-- زر التحقق -->
        <div class="section">
            <button onclick="verifyClient()" id="verifyBtn">
                🔍 التحقق من العميل
            </button>
        </div>

        <!-- النتيجة -->
        <div class="section">
            <h3>📊 نتيجة التحقق</h3>
            <div id="result" class="result info">
                اختر وكيل وأدخل بيانات العميل للبدء...
            </div>
        </div>

        <!-- معلومات الـ API -->
        <div class="section">
            <h3>💻 معلومات الـ API</h3>
            <div class="api-info" id="apiInfo">
عنوان الخادم: http://***********:8080/api/external/verify-direct
طريقة الطلب: POST
نوع المحتوى: application/json

النتائج المتوقعة:
✅ {"status":"success","client_status":1} = العميل نشط
✅ {"status":"success","client_status":0} = العميل غير نشط  
❌ {"status":"error"} = بيانات خاطئة أو عميل غير موجود
            </div>
        </div>
    </div>

    <script>
        let currentAgent = null;

        // اختيار الوكيل
        function selectAgent(agentType) {
            // إزالة التحديد من جميع الوكلاء
            document.querySelectorAll('.agent-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // تحديد الوكيل المختار
            document.getElementById(`agent-${agentType}`).classList.add('selected');
            
            currentAgent = agentType;
            
            if (agentType === 'alghurasi') {
                document.getElementById('selectedAgent').value = 'وكيل الغراسي (alghurasi)';
            } else if (agentType === 'almutarib') {
                document.getElementById('selectedAgent').value = 'وكيل المترب (almutarib)';
            }
            
            updateApiInfo();
        }

        // تحديث معلومات الـ API
        function updateApiInfo() {
            if (!currentAgent) return;
            
            const clientCode = document.getElementById('clientCode').value;
            const clientToken = document.getElementById('clientToken').value;
            
            const agentCredentials = {
                'alghurasi': { user: 'alghurasi', pass: 'alghurasi123' },
                'almutarib': { user: 'almutarib', pass: 'almutarib123' }
            };
            
            const creds = agentCredentials[currentAgent];
            
            document.getElementById('apiInfo').textContent = `عنوان الخادم: http://***********:8080/api/external/verify-direct
طريقة الطلب: POST
نوع المحتوى: application/json

مثال الطلب:
{
  "agent_login_name": "${creds.user}",
  "agent_login_password": "${creds.pass}",
  "client_code": "${clientCode}",
  "client_token": "${clientToken}"
}

النتائج المتوقعة:
✅ {"status":"success","client_status":1} = العميل نشط
✅ {"status":"success","client_status":0} = العميل غير نشط  
❌ {"status":"error"} = بيانات خاطئة أو عميل غير موجود`;
        }

        // عرض النتيجة
        function showResult(message, type = 'info') {
            const element = document.getElementById('result');
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // التحقق من العميل
        async function verifyClient() {
            if (!currentAgent) {
                showResult('❌ يرجى اختيار وكيل أولاً', 'error');
                return;
            }
            
            const clientCode = document.getElementById('clientCode').value;
            const clientToken = document.getElementById('clientToken').value;

            if (!clientCode || !clientToken) {
                showResult('❌ يرجى إدخال رمز العميل والتوكن', 'error');
                return;
            }

            showResult('⏳ جاري التحقق من العميل...', 'loading');
            
            try {
                const agentCredentials = {
                    'alghurasi': { user: 'alghurasi', pass: 'alghurasi123' },
                    'almutarib': { user: 'almutarib', pass: 'almutarib123' }
                };
                
                const creds = agentCredentials[currentAgent];

                const response = await fetch('http://***********:8080/api/external/verify-direct', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        agent_login_name: creds.user,
                        agent_login_password: creds.pass,
                        client_code: clientCode,
                        client_token: clientToken
                    })
                });

                const result = await response.json();
                console.log('النتيجة:', result);

                if (result.status === 'success') {
                    if (result.client_status === 1) {
                        showResult(`✅ العميل ${clientCode} نشط ومتاح\n\nالوكيل: ${currentAgent === 'alghurasi' ? 'الغراسي' : 'المترب'}\nالنتيجة: {"status":"success","client_status":1}`, 'success');
                    } else {
                        showResult(`⚠️ العميل ${clientCode} غير نشط\n\nالوكيل: ${currentAgent === 'alghurasi' ? 'الغراسي' : 'المترب'}\nالنتيجة: {"status":"success","client_status":0}`, 'success');
                    }
                } else {
                    showResult(`❌ فشل التحقق من العميل ${clientCode}\n\nالأسباب المحتملة:\n• بيانات الوكيل خاطئة\n• العميل غير موجود\n• التوكن غير مطابق\n\nالنتيجة: {"status":"error"}`, 'error');
                }

            } catch (error) {
                console.error('خطأ:', error);
                showResult('❌ خطأ في الاتصال\n\nتحقق من:\n• الاتصال بالإنترنت\n• عنوان الخادم\n• إعدادات جدار الحماية', 'error');
            }
        }

        // اختبار سريع
        function quickTest(code, token) {
            document.getElementById('clientCode').value = code;
            document.getElementById('clientToken').value = token;
            updateApiInfo();
            
            if (currentAgent) {
                verifyClient();
            } else {
                showResult('❌ يرجى اختيار وكيل أولاً', 'error');
            }
        }

        // تحديث معلومات الـ API عند تغيير البيانات
        document.getElementById('clientCode').addEventListener('input', updateApiInfo);
        document.getElementById('clientToken').addEventListener('input', updateApiInfo);

        // اختيار وكيل الغراسي افتراضياً
        window.onload = function() {
            selectAgent('alghurasi');
        };
    </script>
</body>
</html>
