const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function createAdminUser() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔧 إنشاء/تحديث مستخدم admin...');
    
    // حذف مستخدم admin السابق إن وجد
    await prisma.user.deleteMany({
      where: {
        OR: [
          { username: 'admin' },
          { loginName: 'admin' }
        ]
      }
    });
    
    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash('admin123456', 10);
    
    // إنشاء مستخدم admin جديد
    const adminUser = await prisma.user.create({
      data: {
        username: 'admin',
        loginName: 'admin',
        password: hashedPassword,
        isActive: true,
        deviceId: 'admin-device-001',
        permissions: {} // صلاحيات فارغة في البداية
      }
    });
    
    console.log('✅ تم إنشاء مستخدم admin بنجاح!');
    console.log('📋 بيانات الدخول:');
    console.log('   - اسم المستخدم: admin');
    console.log('   - كلمة المرور: admin123456');
    console.log(`   - ID: ${adminUser.id}`);
    console.log(`   - تاريخ الإنشاء: ${adminUser.createdAt}`);
    
    // إعادة تعيين كلمة مرور لـ hash8080 أيضاً
    console.log('\n🔧 إعادة تعيين كلمة مرور hash8080...');
    
    const updatedUser = await prisma.user.update({
      where: { loginName: 'hash8080' },
      data: {
        password: hashedPassword // نفس كلمة المرور admin123456
      }
    });
    
    console.log('✅ تم تحديث كلمة مرور hash8080 بنجاح!');
    console.log('📋 يمكنك الآن تسجيل الدخول بـ:');
    console.log('   - اسم المستخدم: hash8080');
    console.log('   - كلمة المرور: admin123456');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم:', error.message);
    console.error('   التفاصيل:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
