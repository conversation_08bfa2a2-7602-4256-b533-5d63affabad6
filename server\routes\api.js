const express = require('express')
const bcrypt = require('bcrypt')
const { PrismaClient } = require('@prisma/client')

const router = express.Router()
const prisma = new PrismaClient()
//const crypto = require('crypto');

/**
 * 🔐 مصادقة الوكيل
 * POST /api/external/agent/auth
 */
router.post('/external/agent/auth', async (req, res) => {
  try {
    const { login_name, login_password } = req.body

    // التحقق من وجود البيانات المطلوبة
    if (!login_name || !login_password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: login_name and login_password',
        error_code: 'VALIDATION_ERROR'
      })
    }

    // البحث عن الوكيل
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: login_name,
        isActive: true
      }
    })

    if (!agent) {
      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: null,
          deviceId: 'api_access',
          ipAddress: req.ip || 'unknown',
          success: false
        }
      })

      return res.status(401).json({
        status: 'agent_error'
      })
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(login_password, agent.loginPassword)

    if (!isPasswordValid) {
      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: agent.id,
          deviceId: 'api_access',
          ipAddress: req.ip || 'unknown',
          success: false
        }
      })

      return res.status(401).json({
        status: 'agent_error'
      })
    }

    // تسجيل محاولة دخول ناجحة
    await prisma.loginAttempt.create({
      data: {
        userType: 'agent',
        agentId: agent.id,
        deviceId: 'api_access',
        ipAddress: req.ip || 'unknown',
        success: true
      }
    })

    // إرجاع الرد الناجح
    res.json({
      status: 'success',
      message: 'Agent authenticated successfully',
      data: {
        agent_id: agent.id,
        agent_name: agent.agentName,
        agency_type: agent.agencyType,
        token: 'temp_token_' + Date.now()
      }
    })

  } catch (error) {
    console.error('Agent authentication error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Authentication service error',
      error_code: 'SERVER_ERROR'
    })
  }
})



/**
 * 🚀 التحقق المباشر من العميل (خطوة واحدة)
 * POST /api/external/verify-direct
 */
router.post('/external/verify-direct', async (req, res) => {
  try {
    const {
      agent_login_name,
      agent_login_password,
      client_code,
      client_token
    } = req.body

    // التحقق من وجود جميع البيانات المطلوبة
    if (!agent_login_name || !agent_login_password || !client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields',
        error_code: 'VALIDATION_ERROR'
      })
    }

    // الخطوة 1: التحقق من الوكيل
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: agent_login_name,
        isActive: true
      }
    })

    if (!agent) {
      return res.status(401).json({
        status: 'agent_error'
      })
    }

    // التحقق من كلمة مرور الوكيل
    const isPasswordValid = await bcrypt.compare(agent_login_password, agent.loginPassword)

    if (!isPasswordValid) {
      return res.status(401).json({
        status: 'agent_error'
      })
    }

    // الخطوة 2: التحقق من العميل
    const client = await prisma.client.findFirst({
      where: {
        clientCode: client_code,
        status: 1
      }
    })

    if (!client) {
      return res.status(404).json({
        status: 'client_error'
      })
    }

    // التحقق من token العميل
    if ( client.password  !== client_token) {
      return res.status(401).json({
        status: 'client_error'
      })
    }

    // تسجيل العملية الناجحة
    await prisma.dataRecord.create({
      data: {
        agentId: agent.id,
        clientId: client.id,
        clientCode: client_code,
        operationDate: new Date(),
        operationStatus: 'success',
        agentReference: 'API_' + Date.now(),
        clientIpAddress: req.ip || 'unknown'
      }
    })

    // إرجاع النجاح
    res.json({
      status: 'success'
    })

  } catch (error) {
    console.error('Direct verification error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR'
    })
  }
})

/**
 * ❤️ فحص حالة API
 * GET /api/external/health
 */
router.get('/external/health', async (req, res) => {
  try {
    // فحص الاتصال بقاعدة البيانات
    await prisma.$queryRaw`SELECT 1`

    res.json({
      status: 'success',
      message: 'API is healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        version: '1.0.0'
      }
    })

  } catch (error) {
    console.error('Health check error:', error)
    res.status(500).json({
      status: 'error',
      message: 'API health check failed',
      error_code: 'HEALTH_CHECK_FAILED'
    })
  }
})

// API للوكلاء للتحقق من بيانات العملاء
router.post('/verify-client', async (req, res) => {
  try {
    const { clientCode, agentId } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;

    if (!clientCode) {
      return res.status(400).json({
        error: 'Client code is required',
        message: 'رمز العميل مطلوب'
      });
    }

    // البحث عن العميل
    const client = await prisma.client.findUnique({
      where: { clientCode: parseInt(clientCode) },
      select: {
        id: true,
        clientCode: true,
        password: true,
        ipAddress: true,
        status: true,
        agentId: true
      }
    });

    // تسجيل محاولة الوصول
    await logLoginAttempt('agent', null, agentId, 'api-access', ipAddress, !!client);

    if (!client) {
      return res.json({
        success: false,
        message: 'رمز العميل هذا غير موجود',
        error: 'Client code not found'
      });
    }

    // التحقق من أن العميل مرتبط بالوكيل (اختياري)
    if (agentId && client.agentId !== parseInt(agentId)) {
      return res.json({
        success: false,
        message: 'غير مصرح لك بالوصول لبيانات هذا العميل',
        error: 'Unauthorized access to client data'
      });
    }

    // إرجاع بيانات العميل
    res.json({
      success: true,
      data: {
        clientCode: client.clientCode,
        password: client.password,
        ipAddress: client.ipAddress,
        status: client.status,
        statusText: client.status === 1 ? 'مفعل' : 'محظور'
      }
    });

  } catch (error) {
    console.error('Client verification error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'خطأ في الخادم'
    });
  }
});

// API للحصول على معلومات الوكيل
router.get('/agent-info/:agentId', async (req, res) => {
  try {
    const { agentId } = req.params;

    const agent = await prisma.agent.findUnique({
      where: { id: parseInt(agentId) },
      select: {
        id: true,
        agentName: true,
        agencyName: true,
        agencyType: true,
        isActive: true,
        _count: {
          select: { clients: true }
        }
      }
    });

    if (!agent) {
      return res.status(404).json({
        error: 'Agent not found',
        message: 'الوكيل غير موجود'
      });
    }

    res.json({
      success: true,
      data: {
        ...agent,
        clientsCount: agent._count.clients
      }
    });

  } catch (error) {
    console.error('Agent info error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'خطأ في الخادم'
    });
  }
});

// API لإحصائيات سريعة
router.get('/stats', async (req, res) => {
  try {
    const [totalClients, activeClients, totalAgents, activeAgents] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } })
    ]);

    res.json({
      success: true,
      data: {
        totalClients,
        activeClients,
        blockedClients: totalClients - activeClients,
        totalAgents,
        activeAgents,
        inactiveAgents: totalAgents - activeAgents
      }
    });

  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'خطأ في الخادم'
    });
  }
});

module.exports = router;
