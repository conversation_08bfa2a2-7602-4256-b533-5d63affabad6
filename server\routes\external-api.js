const express = require('express')
const bcrypt = require('bcrypt')
const { PrismaClient } = require('@prisma/client')
const TokenManager = require('../utils/token-manager')
const {
  authenticateAgent,
  logApiAccess,
  rateLimiter,
  securityHeaders,
  errorHandler
} = require('../middleware/agent-auth')

const router = express.Router()
const prisma = new PrismaClient()

// تطبيق middleware عام
router.use(securityHeaders)
router.use(logApiAccess)
router.use(rateLimiter)

/**
 * 🔐 مصادقة الوكيل
 * POST /api/external/agent/auth
 */
router.post('/agent/auth', async (req, res) => {
  try {
    const { login_name, login_password } = req.body

    // التحقق من وجود البيانات المطلوبة
    if (!login_name || !login_password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: login_name and login_password',
        error_code: 'VALIDATION_ERROR'
      })
    }

    // البحث عن الوكيل
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: login_name,
        isActive: true
      }
    })

    if (!agent) {
      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: null,
          deviceId: 'api_access',
          ipAddress: req.ip || 'unknown',
          success: false
        }
      })

      return res.status(401).json({
        status: 'error',
        message: 'Invalid login credentials',
        error_code: 'AUTH_FAILED'
      })
    }

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(login_password, agent.loginPassword)

    if (!isPasswordValid) {
      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: agent.id,
          deviceId: 'api_access',
          ipAddress: req.ip || 'unknown',
          success: false
        }
      })

      return res.status(401).json({
        status: 'error',
        message: 'Invalid login credentials',
        error_code: 'AUTH_FAILED'
      })
    }

    // إنشاء توكن جديد
    const tokenData = await TokenManager.createAgentToken(
      agent,
      req.ip || 'unknown',
      req.get('User-Agent')
    )

    // تسجيل محاولة دخول ناجحة
    await prisma.loginAttempt.create({
      data: {
        userType: 'agent',
        agentId: agent.id,
        deviceId: 'api_access',
        ipAddress: req.ip || 'unknown',
        success: true
      }
    })

    // إرجاع الرد الناجح
    res.json({
      status: 'success',
      message: 'Agent authenticated successfully',
      data: {
        agent_id: agent.id,
        agent_name: agent.agentName,
        agency_type: agent.agencyType,
        token: tokenData.token,
        expires_at: tokenData.expiresAt
      }
    })

  } catch (error) {
    console.error('Agent authentication error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Authentication service error',
      error_code: 'SERVER_ERROR'
    })
  }
})

/**
 * 👤 التحقق من بيانات العميل
 * POST /api/external/client/verify
 */
router.post('/client/verify', authenticateAgent, async (req, res) => {
  try {
    const { client_code, token } = req.body

    // التحقق من وجود البيانات المطلوبة
    if (!client_code || !token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: client_code and token',
        error_code: 'VALIDATION_ERROR'
      })
    }

    // البحث عن العميل
    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(client_code)
      },
      include: {
        user: {
          select: {
            id: true,
            loginName: true
          }
        }
      }
    })

    if (!client) {
      // تسجيل العملية الفاشلة
      await prisma.dataRecord.create({
        data: {
          agentId: req.agent.id,
          clientId: 0, // عميل غير موجود
          operationStatus: 0, // فاشلة
          agentReference: Math.floor(Math.random() * 1000000),
          clientCode: client_code.toString(),
          clientPassword: 'N/A',
          clientIpAddress: req.ip || 'unknown'
        }
      }).catch(console.error) // تجاهل أخطاء التسجيل

      return res.status(404).json({
        status: 'error',
        message: 'Client not found',
        error_code: 'CLIENT_NOT_FOUND'
      })
    }

    // التحقق من التوكن
    const isTokenValid = client.token === token

    if (!isTokenValid) {
      // تسجيل العملية الفاشلة
      await prisma.dataRecord.create({
        data: {
          agentId: req.agent.id,
          clientId: client.id,
          operationStatus: 0, // فاشلة
          agentReference: Math.floor(Math.random() * 1000000),
          clientCode: client.clientCode.toString(),
          clientPassword: client.password,
          clientIpAddress: client.ipAddress
        }
      }).catch(console.error)

      return res.status(401).json({
        status: 'error',
        message: 'Invalid client token',
        error_code: 'TOKEN_MISMATCH'
      })
    }

    // تسجيل العملية الناجحة
    await prisma.dataRecord.create({
      data: {
        agentId: req.agent.id,
        clientId: client.id,
        operationStatus: 1, // ناجحة
        agentReference: Math.floor(Math.random() * 1000000),
        clientCode: client.clientCode.toString(),
        clientPassword: client.password,
        clientIpAddress: client.ipAddress
      }
    }).catch(console.error)

    // إرجاع بيانات العميل
    res.json({
      status: 'success',
      message: 'Client verified successfully',
      data: {
        client_code: client.clientCode,
        client_name: client.clientName,
        app_name: client.appName,
        status: client.status,
        ip_address: client.ipAddress,
        created_date: client.createdAt.toISOString().split('T')[0],
        created_by_user: client.user ? client.user.loginName : 'Unknown'
      }
    })

  } catch (error) {
    console.error('Client verification error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Client verification service error',
      error_code: 'SERVER_ERROR'
    })
  }
})

/**
 * 📊 إحصائيات الوكيل
 * GET /api/external/agent/stats
 */
router.get('/agent/stats', authenticateAgent, async (req, res) => {
  try {
    const agentId = req.agent.id

    // حساب الإحصائيات
    const [totalOperations, successfulOperations, uniqueClients] = await Promise.all([
      prisma.dataRecord.count({
        where: { agentId }
      }),
      prisma.dataRecord.count({
        where: {
          agentId,
          operationStatus: 1
        }
      }),
      prisma.dataRecord.groupBy({
        by: ['clientId'],
        where: { agentId },
        _count: true
      })
    ])

    const failedOperations = totalOperations - successfulOperations
    const successRate = totalOperations > 0 ?
      ((successfulOperations / totalOperations) * 100).toFixed(1) : '0.0'

    res.json({
      status: 'success',
      data: {
        total_operations: totalOperations,
        successful_operations: successfulOperations,
        failed_operations: failedOperations,
        success_rate: `${successRate}%`,
        unique_clients: uniqueClients.length,
        agent_name: req.agent.agentName,
        agency_type: req.agent.agencyType
      }
    })

  } catch (error) {
    console.error('Agent stats error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Statistics service error',
      error_code: 'SERVER_ERROR'
    })
  }
})

/**
 * 📋 سجل عمليات الوكيل
 * GET /api/external/agent/operations
 */
router.get('/agent/operations', authenticateAgent, async (req, res) => {
  try {
    const agentId = req.agent.id
    const page = parseInt(req.query.page) || 1
    const limit = Math.min(parseInt(req.query.limit) || 10, 100) // حد أقصى 100
    const skip = (page - 1) * limit

    // جلب العمليات مع التصفح
    const [operations, totalCount] = await Promise.all([
      prisma.dataRecord.findMany({
        where: { agentId },
        orderBy: { operationDate: 'desc' },
        skip,
        take: limit,
        include: {
          client: {
            select: {
              clientName: true,
              appName: true
            }
          }
        }
      }),
      prisma.dataRecord.count({
        where: { agentId }
      })
    ])

    const totalPages = Math.ceil(totalCount / limit)

    res.json({
      status: 'success',
      data: {
        operations: operations.map(op => ({
          id: op.id,
          client_code: op.clientCode,
          client_name: op.client?.clientName || 'Unknown',
          app_name: op.client?.appName || 'Unknown',
          operation_status: op.operationStatus,
          operation_date: op.operationDate.toISOString(),
          agent_reference: op.agentReference,
          client_ip: op.clientIpAddress
        })),
        pagination: {
          page,
          limit,
          total: totalCount,
          total_pages: totalPages,
          has_next: page < totalPages,
          has_prev: page > 1
        }
      }
    })

  } catch (error) {
    console.error('Agent operations error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Operations service error',
      error_code: 'SERVER_ERROR'
    })
  }
})

/**
 * 🚪 تسجيل خروج الوكيل
 * POST /api/external/agent/logout
 */
router.post('/agent/logout', authenticateAgent, async (req, res) => {
  try {
    // إلغاء تفعيل التوكن الحالي
    await TokenManager.revokeAgentToken(req.agentSession.token)

    res.json({
      status: 'success',
      message: 'Agent logged out successfully'
    })

  } catch (error) {
    console.error('Agent logout error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Logout service error',
      error_code: 'SERVER_ERROR'
    })
  }
})

/**
 * 🚀 التحقق المباشر من العميل (خطوة واحدة)
 * POST /api/external/verify-direct
 */
router.post('/verify-direct', async (req, res) => {
  try {
    const {
      agent_login_name,
      agent_login_password,
      client_code,
      client_token
    } = req.body

    // التحقق من وجود جميع البيانات المطلوبة
    if (!agent_login_name || !agent_login_password || !client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: agent_login_name, agent_login_password, client_code, client_token',
        error_code: 'VALIDATION_ERROR'
      })
    }

    // الخطوة 1: التحقق من الوكيل
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: agent_login_name,
        isActive: true
      }
    })

    if (!agent) {
      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: null,
          deviceId: 'api_direct_access',
          ipAddress: req.ip || 'unknown',
          success: false
        }
      })

      return res.status(401).json({
        status: 'agent_error'
      })
    }

    // التحقق من كلمة مرور الوكيل
    const isPasswordValid = await bcrypt.compare(agent_login_password, agent.loginPassword)

    if (!isPasswordValid) {
      // تسجيل محاولة دخول فاشلة
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: agent.id,
          deviceId: 'api_direct_access',
          ipAddress: req.ip || 'unknown',
          success: false
        }
      })

      return res.status(401).json({
        status: 'agent_error'
      })
    }

    // الخطوة 2: البحث عن العميل
    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(client_code)
      },
      include: {
        user: {
          select: {
            id: true,
            loginName: true
          }
        }
      }
    })

    if (!client) {
      // تسجيل العملية الفاشلة
      await prisma.dataRecord.create({
        data: {
          agentId: agent.id,
          clientId: 0, // عميل غير موجود
          operationStatus: 0, // فاشلة
          agentReference: Math.floor(Math.random() * 1000000),
          clientCode: client_code.toString(),
          clientPassword: 'N/A',
          clientIpAddress: req.ip || 'unknown'
        }
      }).catch(console.error)

      // تسجيل محاولة دخول ناجحة للوكيل لكن فشل في العميل
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: agent.id,
          deviceId: 'api_direct_access',
          ipAddress: req.ip || 'unknown',
          success: true
        }
      })

      return res.status(404).json({
        status: 'client_error'
      })
    }

    // الخطوة 3: التحقق من توكن العميل
    const isTokenValid = client.token === client_token

    if (!isTokenValid) {
      // تسجيل العملية الفاشلة
      await prisma.dataRecord.create({
        data: {
          agentId: agent.id,
          clientId: client.id,
          operationStatus: 0, // فاشلة
          agentReference: Math.floor(Math.random() * 1000000),
          clientCode: client.clientCode.toString(),
          clientPassword: client.password,
          clientIpAddress: client.ipAddress
        }
      }).catch(console.error)

      // تسجيل محاولة دخول ناجحة للوكيل
      await prisma.loginAttempt.create({
        data: {
          userType: 'agent',
          agentId: agent.id,
          deviceId: 'api_direct_access',
          ipAddress: req.ip || 'unknown',
          success: true
        }
      })

      return res.status(401).json({
        status: 'client_error'
      })
    }

    // الخطوة 4: تسجيل العملية الناجحة
    await prisma.dataRecord.create({
      data: {
        agentId: agent.id,
        clientId: client.id,
        operationStatus: 1, // ناجحة
        agentReference: Math.floor(Math.random() * 1000000),
        clientCode: client.clientCode.toString(),
        clientPassword: client.password,
        clientIpAddress: client.ipAddress
      }
    }).catch(console.error)

    // تسجيل محاولة دخول ناجحة للوكيل
    await prisma.loginAttempt.create({
      data: {
        userType: 'agent',
        agentId: agent.id,
        deviceId: 'api_direct_access',
        ipAddress: req.ip || 'unknown',
        success: true
      }
    })

    // إرجاع النتيجة المبسطة
    res.json({
      status: 'success',
      client_status: client.status
    })

  } catch (error) {
    console.error('Direct verification error:', error)
    res.status(500).json({
      status: 'error',
      message: 'Direct verification service error',
      error_code: 'SERVER_ERROR'
    })
  }
})

/**
 * ❤️ فحص حالة API
 * GET /api/external/health
 */
router.get('/health', async (req, res) => {
  try {
    // فحص الاتصال بقاعدة البيانات
    await prisma.$queryRaw`SELECT 1`

    // إحصائيات سريعة
    const sessionStats = await TokenManager.getSessionStats()

    res.json({
      status: 'success',
      message: 'API is healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        active_sessions: sessionStats.activeSessions,
        version: '1.0.0'
      }
    })

  } catch (error) {
    console.error('Health check error:', error)
    res.status(500).json({
      status: 'error',
      message: 'API health check failed',
      error_code: 'HEALTH_CHECK_FAILED'
    })
  }
})

// معالج الأخطاء
router.use(errorHandler)

module.exports = router
