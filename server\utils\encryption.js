const crypto = require('crypto');
const bcrypt = require('bcryptjs');

/**
 * توليد توكن عشوائي مشفر
 * @param {number} length - طول التوكن (افتراضي 16)
 * @returns {string} توكن مشفر يحتوي على أحرف وأرقام
 */
function generateSecureToken(length = 16) {
  // التأكد من أن الطول لا يقل عن 8
  if (length < 8) {
    length = 8;
  }
  
  // أحرف وأرقام مسموحة
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let token = '';
  
  // توليد التوكن
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, chars.length);
    token += chars[randomIndex];
  }
  
  // التأكد من وجود رقم واحد على الأقل
  if (!/\d/.test(token)) {
    const randomPos = crypto.randomInt(0, token.length);
    const randomDigit = crypto.randomInt(0, 10);
    token = token.substring(0, randomPos) + randomDigit + token.substring(randomPos + 1);
  }
  
  // التأكد من وجود حرف واحد على الأقل
  if (!/[a-zA-Z]/.test(token)) {
    const randomPos = crypto.randomInt(0, token.length);
    const randomChar = chars[crypto.randomInt(26, 52)]; // حروف فقط
    token = token.substring(0, randomPos) + randomChar + token.substring(randomPos + 1);
  }
  
  return token;
}

/**
 * توليد كلمة مرور عشوائية آمنة
 * @param {number} length - طول كلمة المرور (افتراضي 12)
 * @returns {string} كلمة مرور تحتوي على أحرف وأرقام ورموز
 */
function generateSecurePassword(length = 12) {
  // التأكد من أن الطول لا يقل عن 8
  if (length < 8) {
    length = 8;
  }
  
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*';
  
  let password = '';
  
  // ضمان وجود حرف كبير واحد على الأقل
  password += uppercase[crypto.randomInt(0, uppercase.length)];
  
  // ضمان وجود حرف صغير واحد على الأقل
  password += lowercase[crypto.randomInt(0, lowercase.length)];
  
  // ضمان وجود رقم واحد على الأقل
  password += numbers[crypto.randomInt(0, numbers.length)];
  
  // ضمان وجود رمز واحد على الأقل
  password += symbols[crypto.randomInt(0, symbols.length)];
  
  // ملء باقي الطول
  const allChars = uppercase + lowercase + numbers + symbols;
  for (let i = 4; i < length; i++) {
    password += allChars[crypto.randomInt(0, allChars.length)];
  }
  
  // خلط الأحرف
  return password.split('').sort(() => crypto.randomInt(-1, 2)).join('');
}

/**
 * تشفير كلمة المرور باستخدام bcrypt
 * @param {string} password - كلمة المرور الخام
 * @returns {Promise<string>} كلمة المرور المشفرة
 */
async function hashPassword(password) {
  const saltRounds = 12; // مستوى تشفير عالي
  return await bcrypt.hash(password, saltRounds);
}

/**
 * التحقق من كلمة المرور
 * @param {string} password - كلمة المرور الخام
 * @param {string} hashedPassword - كلمة المرور المشفرة
 * @returns {Promise<boolean>} true إذا كانت كلمة المرور صحيحة
 */
async function verifyPassword(password, hashedPassword) {
  return await bcrypt.compare(password, hashedPassword);
}

/**
 * التحقق من قوة كلمة المرور
 * @param {string} password - كلمة المرور للفحص
 * @returns {object} نتيجة الفحص مع التفاصيل
 */
function validatePasswordStrength(password) {
  const result = {
    isValid: true,
    errors: [],
    score: 0
  };
  
  // الطول الأدنى
  if (password.length < 8) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
  } else {
    result.score += 1;
  }
  
  // وجود أحرف كبيرة
  if (!/[A-Z]/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  } else {
    result.score += 1;
  }
  
  // وجود أحرف صغيرة
  if (!/[a-z]/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  } else {
    result.score += 1;
  }
  
  // وجود أرقام
  if (!/\d/.test(password)) {
    result.isValid = false;
    result.errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  } else {
    result.score += 1;
  }
  
  // وجود رموز خاصة
  if (!/[!@#$%^&*]/.test(password)) {
    result.errors.push('يُنصح بإضافة رمز خاص (!@#$%^&*) لزيادة الأمان');
  } else {
    result.score += 1;
  }
  
  return result;
}

/**
 * التحقق من صحة التوكن
 * @param {string} token - التوكن للفحص
 * @returns {object} نتيجة الفحص
 */
function validateToken(token) {
  const result = {
    isValid: true,
    errors: []
  };
  
  // الطول الأدنى
  if (token.length < 8) {
    result.isValid = false;
    result.errors.push('التوكن يجب أن يكون 8 أحرف على الأقل');
  }
  
  // وجود أحرف وأرقام
  if (!/[a-zA-Z]/.test(token)) {
    result.isValid = false;
    result.errors.push('التوكن يجب أن يحتوي على أحرف');
  }
  
  if (!/\d/.test(token)) {
    result.isValid = false;
    result.errors.push('التوكن يجب أن يحتوي على أرقام');
  }
  
  // أحرف مسموحة فقط
  if (!/^[a-zA-Z0-9]+$/.test(token)) {
    result.isValid = false;
    result.errors.push('التوكن يجب أن يحتوي على أحرف وأرقام فقط');
  }
  
  return result;
}

/**
 * توليد توكن فريد للعميل
 * @param {object} prisma - Prisma client
 * @returns {Promise<string>} توكن فريد
 */
async function generateUniqueClientToken(prisma) {
  let token;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;
  
  while (!isUnique && attempts < maxAttempts) {
    token = generateSecureToken(16);
    
    // التحقق من عدم وجود التوكن في قاعدة البيانات
    const existingClient = await prisma.client.findFirst({
      where: { token: token }
    });
    
    if (!existingClient) {
      isUnique = true;
    }
    
    attempts++;
  }
  
  if (!isUnique) {
    throw new Error('فشل في توليد توكن فريد بعد عدة محاولات');
  }
  
  return token;
}

module.exports = {
  generateSecureToken,
  generateSecurePassword,
  hashPassword,
  verifyPassword,
  validatePasswordStrength,
  validateToken,
  generateUniqueClientToken
};
