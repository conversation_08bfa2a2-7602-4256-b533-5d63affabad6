const { PrismaClient } = require('./server/node_modules/@prisma/client')

const prisma = new PrismaClient()

async function generateComprehensiveReport() {
  try {
    console.log('📊 تقرير شامل عن أرقام المعرفات والعمليات')
    console.log('=' .repeat(80))
    
    // 1. تحليل الوكلاء
    console.log('\n🤝 تحليل الوكلاء:')
    console.log('=' .repeat(40))
    
    const agents = await prisma.agent.findMany({
      orderBy: { id: 'asc' },
      select: {
        id: true,
        agentName: true,
        agencyName: true,
        loginName: true,
        isActive: true,
        createdAt: true,
        _count: {
          select: {
            dataRecords: true,
            loginAttempts: true
          }
        }
      }
    })

    console.log(`📊 إجمالي الوكلاء: ${agents.length}`)
    console.log('')

    agents.forEach((agent, index) => {
      console.log(`الوكيل ${index + 1}:`)
      console.log(`  🆔 رقم المعرف: ${agent.id}`)
      console.log(`  👤 الاسم: ${agent.agentName}`)
      console.log(`  🏢 الوكالة: ${agent.agencyName}`)
      console.log(`  🔑 اسم الدخول: ${agent.loginName || 'غير محدد'}`)
      console.log(`  ✅ الحالة: ${agent.isActive ? 'نشط' : 'غير نشط'}`)
      console.log(`  📊 العمليات: ${agent._count.dataRecords}`)
      console.log(`  🔐 محاولات الدخول: ${agent._count.loginAttempts}`)
      console.log(`  📅 تاريخ الإنشاء: ${agent.createdAt.toLocaleString('ar-SA')}`)
      console.log('')
    })

    // تحليل الفجوات في أرقام الوكلاء
    const agentIds = agents.map(a => a.id).sort((a, b) => a - b)
    const missingAgentIds = []
    
    for (let i = 1; i <= Math.max(...agentIds); i++) {
      if (!agentIds.includes(i)) {
        missingAgentIds.push(i)
      }
    }

    console.log('🔍 تحليل أرقام الوكلاء:')
    console.log(`  📊 الأرقام الموجودة: ${agentIds.join(', ')}`)
    console.log(`  ❌ الأرقام المفقودة: ${missingAgentIds.length > 0 ? missingAgentIds.join(', ') : 'لا توجد'}`)
    console.log(`  📈 المدى: ${Math.min(...agentIds)} - ${Math.max(...agentIds)}`)
    console.log('')

    // 2. تحليل العمليات
    console.log('📊 تحليل العمليات:')
    console.log('=' .repeat(40))
    
    const operations = await prisma.dataRecord.findMany({
      orderBy: { id: 'desc' },
      take: 10,
      include: {
        agent: {
          select: {
            agentName: true
          }
        }
      }
    })

    console.log(`📊 آخر ${operations.length} عمليات:`)
    console.log('')

    operations.forEach((op, index) => {
      console.log(`العملية ${index + 1}:`)
      console.log(`  🆔 رقم المعرف: ${op.id}`)
      console.log(`  🤝 الوكيل: ${op.agent?.agentName || 'غير محدد'} (ID: ${op.agentId})`)
      console.log(`  👤 رمز العميل: ${op.clientCode}`)
      console.log(`  📊 الحالة: ${op.operationStatus === 1 ? 'ناجحة ✅' : 'فاشلة ❌'}`)
      console.log(`  🎲 رقم المرجع: ${op.agentReference}`)
      console.log(`  📅 التاريخ: ${op.operationDate.toLocaleString('ar-SA')}`)
      console.log('')
    })

    // إحصائيات العمليات
    const operationStats = await prisma.dataRecord.aggregate({
      _count: { id: true },
      _min: { id: true },
      _max: { id: true }
    })

    const successfulOps = await prisma.dataRecord.count({
      where: { operationStatus: 1 }
    })

    const failedOps = await prisma.dataRecord.count({
      where: { operationStatus: 0 }
    })

    console.log('📈 إحصائيات العمليات:')
    console.log(`  📊 إجمالي العمليات: ${operationStats._count.id}`)
    console.log(`  ✅ العمليات الناجحة: ${successfulOps}`)
    console.log(`  ❌ العمليات الفاشلة: ${failedOps}`)
    console.log(`  🔢 أصغر رقم معرف: ${operationStats._min.id}`)
    console.log(`  🔢 أكبر رقم معرف: ${operationStats._max.id}`)
    console.log('')

    // 3. تحليل العملاء
    console.log('👥 تحليل العملاء:')
    console.log('=' .repeat(40))
    
    const clientStats = await prisma.client.aggregate({
      _count: { id: true },
      _min: { clientCode: true },
      _max: { clientCode: true }
    })

    const activeClients = await prisma.client.count({
      where: { status: 1 }
    })

    const inactiveClients = await prisma.client.count({
      where: { status: 0 }
    })

    console.log(`📊 إجمالي العملاء: ${clientStats._count.id}`)
    console.log(`✅ العملاء النشطون: ${activeClients}`)
    console.log(`❌ العملاء غير النشطين: ${inactiveClients}`)
    console.log(`🔢 أصغر رمز عميل: ${clientStats._min.clientCode}`)
    console.log(`🔢 أكبر رمز عميل: ${clientStats._max.clientCode}`)
    console.log('')

    // 4. تحليل المستخدمين
    console.log('👤 تحليل المستخدمين:')
    console.log('=' .repeat(40))
    
    const userStats = await prisma.user.aggregate({
      _count: { id: true }
    })

    const activeUsers = await prisma.user.count({
      where: { isActive: true }
    })

    console.log(`📊 إجمالي المستخدمين: ${userStats._count.id}`)
    console.log(`✅ المستخدمين النشطين: ${activeUsers}`)
    console.log('')

    // 5. الخلاصة والتوصيات
    console.log('🎯 الخلاصة والتوصيات:')
    console.log('=' .repeat(50))
    
    console.log('📋 الوضع الحالي:')
    console.log(`  • رقم وكيل الغراسي: ${agents.find(a => a.agentName === 'الغراسي')?.id || 'غير موجود'}`)
    console.log(`  • رقم وكيل المترب: ${agents.find(a => a.agentName === 'المترب')?.id || 'غير موجود'}`)
    console.log(`  • رقم وكيل الاختبار: ${agents.find(a => a.agentName === 'وكيل اختبار')?.id || 'غير موجود'}`)
    console.log('')

    console.log('💡 التوصيات:')
    if (missingAgentIds.length > 0) {
      console.log('  ⚠️  هناك فجوات في أرقام الوكلاء')
      console.log('  💡 يمكن إضافة حقل agentNumber منفصل للترقيم التسلسلي')
    }
    
    console.log('  ✅ الأرقام الحالية ثابتة ويمكن الاعتماد عليها')
    console.log('  ✅ رقم مرجع الوكيل عشوائي وهذا طبيعي')
    console.log('  ✅ النظام يعمل بشكل صحيح')
    console.log('')

    console.log('🔧 للاستخدام في API:')
    console.log('  • استخدم رقم المعرف (ID) كما هو')
    console.log('  • لا تعتمد على التسلسل في الأرقام')
    console.log('  • رقم مرجع الوكيل للتتبع فقط')

  } catch (error) {
    console.error('❌ خطأ في إنشاء التقرير:', error)
  } finally {
    await prisma.$disconnect()
  }
}

generateComprehensiveReport()
