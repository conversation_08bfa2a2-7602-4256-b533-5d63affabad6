/**
 * إنشاء مستخدم admin جديد
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function createAdminUser() {
  console.log('🔧 إنشاء مستخدم admin جديد...\n');

  try {
    await prisma.$connect();
    console.log('✅ الاتصال بقاعدة البيانات نجح\n');

    // التحقق من وجود مستخدم admin
    const existingAdmin = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { loginName: 'admin' }
        ]
      }
    });

    if (existingAdmin) {
      console.log('⚠️ مستخدم admin موجود بالفعل:');
      console.log(`   👤 الاسم: ${existingAdmin.username}`);
      console.log(`   🔑 اسم الدخول: ${existingAdmin.loginName}`);
      console.log(`   ✅ نشط: ${existingAdmin.isActive ? 'نعم' : 'لا'}`);
      
      // تحديث كلمة المرور للمستخدم الموجود
      console.log('\n🔄 تحديث كلمة المرور إلى admin123...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      await prisma.user.update({
        where: { id: existingAdmin.id },
        data: {
          password: hashedPassword,
          isActive: true,
          permissions: {
            isAdmin: true,
            clients: { read: true, create: true, update: true, delete: true },
            agents: { read: true, create: true, update: true, delete: true },
            users: { read: true, create: true, update: true, delete: true },
            dataRecords: { read: true, create: true, update: true, delete: true },
            security: { read: true, create: true, update: true, delete: true }
          }
        }
      });
      
      console.log('✅ تم تحديث المستخدم admin بنجاح!');
      
    } else {
      // إنشاء مستخدم admin جديد
      console.log('📝 إنشاء مستخدم admin جديد...');
      
      const hashedPassword = await bcrypt.hash('admin123', 10);
      
      const newAdmin = await prisma.user.create({
        data: {
          username: 'admin',
          loginName: 'admin',
          password: hashedPassword,
          isActive: true,
          permissions: {
            isAdmin: true,
            clients: { read: true, create: true, update: true, delete: true },
            agents: { read: true, create: true, update: true, delete: true },
            users: { read: true, create: true, update: true, delete: true },
            dataRecords: { read: true, create: true, update: true, delete: true },
            security: { read: true, create: true, update: true, delete: true }
          }
        }
      });
      
      console.log('✅ تم إنشاء مستخدم admin جديد!');
      console.log(`   👤 ID: ${newAdmin.id}`);
      console.log(`   👤 الاسم: ${newAdmin.username}`);
      console.log(`   🔑 اسم الدخول: ${newAdmin.loginName}`);
    }

    // اختبار تسجيل الدخول
    console.log('\n🧪 اختبار تسجيل الدخول...');
    
    const testUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { loginName: 'admin' }
        ]
      }
    });

    if (testUser) {
      const isPasswordValid = await bcrypt.compare('admin123', testUser.password);
      console.log(`🔑 اختبار كلمة المرور admin123: ${isPasswordValid ? '✅ صحيحة' : '❌ خاطئة'}`);
      
      if (isPasswordValid) {
        console.log('\n🎉 يمكنك الآن تسجيل الدخول بـ:');
        console.log('   👤 اسم المستخدم: admin');
        console.log('   🔑 كلمة المرور: admin123');
      }
    }

    // عرض جميع المستخدمين
    console.log('\n📋 جميع المستخدمين في النظام:');
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        isActive: true
      }
    });

    allUsers.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.username} (${user.loginName}) - ${user.isActive ? 'نشط' : 'غير نشط'}`);
    });

    console.log('\n✅ انتهى إنشاء المستخدم!');

  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser().catch(console.error);
