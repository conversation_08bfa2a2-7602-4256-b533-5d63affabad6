const express = require('express');
const bcrypt = require('bcryptjs');
const { body, validationResult } = require('express-validator');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken, checkPermission } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// الحصول على جميع المستخدمين
router.get('/', authenticateToken, checkPermission('users', 'read'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const skip = (page - 1) * limit;

    const where = search ? {
      OR: [
        { username: { contains: search, mode: 'insensitive' } },
        { loginName: { contains: search, mode: 'insensitive' } }
      ]
    } : {};

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          loginName: true,
          deviceId: true,
          permissions: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: { clients: true }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    res.json({
      users,
      pagination: {
        total,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// الحصول على مستخدم واحد
router.get('/:id', authenticateToken, checkPermission('users', 'read'), async (req, res) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: parseInt(req.params.id) },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        permissions: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        clients: {
          select: {
            id: true,
            clientName: true,
            appName: true,
            status: true
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// إضافة مستخدم جديد
router.post('/', [
  authenticateToken,
  checkPermission('users', 'create'),
  body('username').notEmpty().withMessage('Username is required'),
  body('loginName').isLength({ min: 8 }).withMessage('Login name must be at least 8 characters'),
  body('password').isLength({ min: 8 }).withMessage('Password must be at least 8 characters'),
  body('permissions').isObject().withMessage('Permissions must be an object')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { username, loginName, password, permissions, deviceId } = req.body;

    // التحقق من عدم تكرار اسم المستخدم
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { loginName }
        ]
      }
    });

    if (existingUser) {
      return res.status(400).json({ error: 'Username or login name already exists' });
    }

    // تشفير كلمة المرور
    const hashedPassword = await bcrypt.hash(password, 12);

    const user = await prisma.user.create({
      data: {
        username,
        loginName,
        password: hashedPassword,
        permissions,
        deviceId
      },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        permissions: true,
        isActive: true,
        createdAt: true
      }
    });

    res.status(201).json(user);
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// تحديث مستخدم
router.put('/:id', [
  authenticateToken,
  checkPermission('users', 'update'),
  body('username').optional().notEmpty(),
  body('loginName').optional().isLength({ min: 8 }),
  body('password').optional().isLength({ min: 8 }),
  body('permissions').optional().isObject()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const userId = parseInt(req.params.id);
    const updateData = { ...req.body };

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // تشفير كلمة المرور إذا تم تحديثها
    if (updateData.password) {
      updateData.password = await bcrypt.hash(updateData.password, 12);
    }

    // التحقق من عدم تكرار اسم المستخدم
    if (updateData.username || updateData.loginName) {
      const duplicateUser = await prisma.user.findFirst({
        where: {
          OR: [
            updateData.username ? { username: updateData.username } : {},
            updateData.loginName ? { loginName: updateData.loginName } : {}
          ].filter(obj => Object.keys(obj).length > 0),
          id: { not: userId }
        }
      });

      if (duplicateUser) {
        return res.status(400).json({ error: 'Username or login name already exists' });
      }
    }

    const user = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        permissions: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    });

    res.json(user);
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// حذف مستخدم
router.delete('/:id', authenticateToken, checkPermission('users', 'delete'), async (req, res) => {
  try {
    const userId = parseInt(req.params.id);

    // منع حذف المستخدم الحالي
    if (userId === req.user.id) {
      return res.status(400).json({ error: 'Cannot delete your own account' });
    }

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    await prisma.user.delete({
      where: { id: userId }
    });

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
