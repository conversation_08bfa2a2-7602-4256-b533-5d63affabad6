// إصلاح سريع لمشكلة تسجيل الدخول
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 8084;

// Middleware
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(express.json());

console.log('🚀 Starting quick fix server...');

// Serve static files
const clientDistPath = path.join(__dirname, '../client/dist');
console.log('Looking for client dist at:', clientDistPath);

if (require('fs').existsSync(clientDistPath)) {
  app.use(express.static(clientDistPath));
  console.log('✅ Serving static files from:', clientDistPath);
} else {
  console.log('❌ Client dist not found');
}

// Health check
app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    message: 'Quick fix server is running'
  });
});

// Mock login endpoint for testing
app.post('/api/auth/login', async (req, res) => {
  console.log('🔥 Login request received:', {
    body: req.body,
    ip: req.ip,
    timestamp: new Date().toISOString()
  });

  try {
    const { loginName, password, userType = 'auto' } = req.body;
    
    console.log('📋 Processing login:', {
      loginName,
      hasPassword: !!password,
      userType
    });

    // Mock authentication - for testing only
    let isValid = false;
    let responseData = null;

    // Test users
    if (loginName === 'admin' && password === 'admin123') {
      isValid = true;
      responseData = {
        id: 1,
        username: 'admin',
        loginName: 'admin',
        accountType: 'user',
        permissions: { users: { read: true, write: true } },
        deviceId: 'test-device-' + Date.now()
      };
    } else if (loginName === 'testadmin' && password === 'admin123') {
      isValid = true;
      responseData = {
        id: 2,
        username: 'Test Admin',
        loginName: 'testadmin',
        accountType: 'user',
        permissions: { users: { read: true, write: true } },
        deviceId: 'test-device-' + Date.now()
      };
    } else if (loginName === '1001' && password === '123456') {
      isValid = true;
      responseData = {
        id: 3,
        username: 'عميل تجريبي',
        loginName: '1001',
        clientCode: 1001,
        accountType: 'client',
        appName: 'YemClient Test',
        deviceId: 'test-device-' + Date.now()
      };
    } else if (loginName === '9999' && password === '123456') {
      isValid = true;
      responseData = {
        id: 4,
        username: 'عميل تجريبي للاختبار',
        loginName: '9999',
        clientCode: 9999,
        accountType: 'client',
        appName: 'YemClient Test',
        deviceId: 'test-device-' + Date.now()
      };
    }

    if (isValid) {
      console.log('✅ Login successful for:', loginName);
      
      // Mock JWT token
      const token = 'mock-jwt-token-' + Date.now();
      
      res.json({
        success: true,
        message: 'تم تسجيل الدخول بنجاح',
        user: responseData,
        token: token,
        sessionId: 'mock-session-' + Date.now()
      });
    } else {
      console.log('❌ Login failed for:', loginName);
      res.status(401).json({
        success: false,
        message: 'بيانات الدخول غير صحيحة'
      });
    }

  } catch (error) {
    console.error('💥 Login error:', error);
    res.status(500).json({
      success: false,
      message: 'خطأ في الخادم'
    });
  }
});

// Catch all handler for SPA
app.get('*', (req, res) => {
  const indexPath = path.join(clientDistPath, 'index.html');
  if (require('fs').existsSync(indexPath)) {
    res.sendFile(indexPath);
  } else {
    res.status(404).send('Client app not found');
  }
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🚀 Quick fix server running on http://localhost:${PORT}`);
  console.log(`🌐 External: http://***********:${PORT}`);
  console.log(`💚 Health: http://localhost:${PORT}/health`);
  console.log(`🔑 Login: http://localhost:${PORT}/api/auth/login`);
  console.log('');
  console.log('📋 Test Credentials:');
  console.log('👤 Users:');
  console.log('   admin / admin123');
  console.log('   testadmin / admin123');
  console.log('🏢 Clients:');
  console.log('   1001 / 123456');
  console.log('   9999 / 123456');
});

// Handle shutdown
process.on('SIGINT', () => {
  console.log('🛑 Shutting down quick fix server...');
  process.exit(0);
});
