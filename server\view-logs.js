const fs = require('fs');
const path = require('path');

// مجلد اللوقات
const logsDir = path.join(__dirname, 'logs');

// دالة لقراءة وعرض اللوقات
function viewLogs(logType = 'all', lines = 50) {
  console.log('📝 عارض اللوقات - نظام إدارة العملاء والوكلاء');
  console.log('=' .repeat(60));
  
  if (!fs.existsSync(logsDir)) {
    console.log('❌ مجلد اللوقات غير موجود');
    return;
  }
  
  const today = new Date().toISOString().split('T')[0];
  const files = [];
  
  // تحديد الملفات المطلوبة
  if (logType === 'all' || logType === 'proxy') {
    const proxyFile = path.join(logsDir, `proxy-${today}.log`);
    if (fs.existsSync(proxyFile)) {
      files.push({ type: 'PROXY', file: proxyFile });
    }
  }
  
  if (logType === 'all' || logType === 'main') {
    const mainFile = path.join(logsDir, `main-${today}.log`);
    if (fs.existsSync(mainFile)) {
      files.push({ type: 'MAIN', file: mainFile });
    }
  }
  
  if (files.length === 0) {
    console.log('❌ لا توجد ملفات لوق لليوم الحالي');
    return;
  }
  
  // قراءة وعرض اللوقات
  files.forEach(({ type, file }) => {
    console.log(`\n🔍 ${type} SERVER LOGS:`);
    console.log('-'.repeat(40));
    
    try {
      const content = fs.readFileSync(file, 'utf8');
      const logLines = content.trim().split('\n');
      
      // عرض آخر N سطر
      const recentLines = logLines.slice(-lines);
      
      recentLines.forEach(line => {
        if (line.trim()) {
          // تلوين اللوقات حسب النوع
          if (line.includes('ERROR')) {
            console.log(`🔴 ${line}`);
          } else if (line.includes('INCOMING REQUEST')) {
            console.log(`🔵 ${line}`);
          } else if (line.includes('PROXY REQUEST')) {
            console.log(`🟡 ${line}`);
          } else if (line.includes('PROXY RESPONSE')) {
            console.log(`🟢 ${line}`);
          } else if (line.includes('STATIC FILE')) {
            console.log(`🟣 ${line}`);
          } else {
            console.log(`⚪ ${line}`);
          }
        }
      });
      
      console.log(`\n📊 إجمالي السطور: ${logLines.length}, معروض: ${recentLines.length}`);
      
    } catch (error) {
      console.log(`❌ خطأ في قراءة الملف: ${error.message}`);
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log('🔍 لعرض لوقات محددة:');
  console.log('  node view-logs.js proxy    - لوقات البروكسي فقط');
  console.log('  node view-logs.js main     - لوقات الخادم الرئيسي فقط');
  console.log('  node view-logs.js all 100  - آخر 100 سطر من جميع اللوقات');
}

// دالة لمراقبة اللوقات في الوقت الفعلي
function watchLogs() {
  console.log('👁️ مراقبة اللوقات في الوقت الفعلي...');
  console.log('اضغط Ctrl+C للخروج\n');
  
  const today = new Date().toISOString().split('T')[0];
  const proxyFile = path.join(logsDir, `proxy-${today}.log`);
  const mainFile = path.join(logsDir, `main-${today}.log`);
  
  // مراقبة ملف البروكسي
  if (fs.existsSync(proxyFile)) {
    fs.watchFile(proxyFile, (curr, prev) => {
      if (curr.mtime > prev.mtime) {
        const content = fs.readFileSync(proxyFile, 'utf8');
        const lines = content.trim().split('\n');
        const lastLine = lines[lines.length - 1];
        if (lastLine) {
          console.log(`🔄 PROXY: ${lastLine}`);
        }
      }
    });
  }
  
  // مراقبة ملف الخادم الرئيسي
  if (fs.existsSync(mainFile)) {
    fs.watchFile(mainFile, (curr, prev) => {
      if (curr.mtime > prev.mtime) {
        const content = fs.readFileSync(mainFile, 'utf8');
        const lines = content.trim().split('\n');
        const lastLine = lines[lines.length - 1];
        if (lastLine) {
          console.log(`🚀 MAIN: ${lastLine}`);
        }
      }
    });
  }
}

// دالة لتحليل اللوقات
function analyzeLogs() {
  console.log('📊 تحليل اللوقات...\n');
  
  const today = new Date().toISOString().split('T')[0];
  const proxyFile = path.join(logsDir, `proxy-${today}.log`);
  const mainFile = path.join(logsDir, `main-${today}.log`);
  
  const stats = {
    proxyRequests: 0,
    mainRequests: 0,
    errors: 0,
    staticFiles: 0,
    indexRequests: 0
  };
  
  // تحليل لوقات البروكسي
  if (fs.existsSync(proxyFile)) {
    const content = fs.readFileSync(proxyFile, 'utf8');
    const lines = content.trim().split('\n');
    
    lines.forEach(line => {
      if (line.includes('INCOMING REQUEST')) stats.proxyRequests++;
      if (line.includes('ERROR')) stats.errors++;
    });
  }
  
  // تحليل لوقات الخادم الرئيسي
  if (fs.existsSync(mainFile)) {
    const content = fs.readFileSync(mainFile, 'utf8');
    const lines = content.trim().split('\n');
    
    lines.forEach(line => {
      if (line.includes('INCOMING REQUEST')) stats.mainRequests++;
      if (line.includes('STATIC FILE')) stats.staticFiles++;
      if (line.includes('INDEX PAGE REQUEST')) stats.indexRequests++;
      if (line.includes('ERROR')) stats.errors++;
    });
  }
  
  console.log('📈 إحصائيات اليوم:');
  console.log(`  🔄 طلبات البروكسي: ${stats.proxyRequests}`);
  console.log(`  🚀 طلبات الخادم الرئيسي: ${stats.mainRequests}`);
  console.log(`  🏠 طلبات الصفحة الرئيسية: ${stats.indexRequests}`);
  console.log(`  📁 طلبات الملفات الثابتة: ${stats.staticFiles}`);
  console.log(`  ❌ الأخطاء: ${stats.errors}`);
  
  // تحليل التوجيه
  if (stats.proxyRequests > 0 && stats.mainRequests === 0) {
    console.log('\n⚠️ تحذير: البروكسي يستقبل طلبات لكن الخادم الرئيسي لا يستقبل أي طلبات!');
    console.log('   هذا يشير إلى مشكلة في التوجيه بين البروكسي والخادم الرئيسي.');
  } else if (stats.proxyRequests === stats.mainRequests) {
    console.log('\n✅ التوجيه يعمل بشكل صحيح - جميع طلبات البروكسي تصل للخادم الرئيسي.');
  }
}

// معالجة المعاملات
const args = process.argv.slice(2);
const command = args[0] || 'all';
const lines = parseInt(args[1]) || 50;

if (command === 'watch') {
  watchLogs();
} else if (command === 'analyze') {
  analyzeLogs();
} else {
  viewLogs(command, lines);
}
