import React, { createContext, useContext, useState, useEffect } from 'react'
import debugLogger from '../utils/debugLogger'

// إنشاء Context لحالة المصادقة
const AuthContext = createContext({})

debugLogger.info('AuthContext initialized', {
  message: 'النظام يعمل مع قاعدة البيانات الحقيقية',
  timestamp: new Date().toISOString()
})

// دالة لتوليد رقم جهاز فريد
const generateDeviceId = () => {
  const stored = localStorage.getItem('deviceId')
  if (stored) return stored

  const deviceId = `device_${Math.random().toString(36).substring(2, 11)}_${Date.now()}`
  localStorage.setItem('deviceId', deviceId)
  return deviceId
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const validateSession = async () => {
      debugLogger.auth('Starting session validation', {
        hasToken: !!localStorage.getItem('token'),
        hasUser: !!localStorage.getItem('user'),
        hasClientData: !!localStorage.getItem('clientData')
      })

      const token = localStorage.getItem('token')
      const savedUser = localStorage.getItem('user')
      const clientData = localStorage.getItem('clientData')

      // إذا كان هناك مستخدم عادي
      if (token && savedUser) {
        try {
          debugLogger.auth('Validating user session', { token: token.substring(0, 20) + '...' })

          // التحقق من صحة الجلسة مع الخادم
          const response = await fetch('/api/auth/validate', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            credentials: 'include'
          })

          debugLogger.network('Session validation response',
            { url: '/api/auth/validate', method: 'GET' },
            { status: response.status, ok: response.ok }
          )

          if (response.ok) {
            const result = await response.json()
            debugLogger.auth('Session validation result', result)

            if (result.success && result.user) {
              setUser(result.user)
              // تحديث البيانات المحلية
              localStorage.setItem('user', JSON.stringify(result.user))
              debugLogger.success('User session validated successfully', result.user)
            } else {
              throw new Error('Invalid session response: ' + JSON.stringify(result))
            }
          } else {
            const errorText = await response.text()
            throw new Error(`Session validation failed: ${response.status} - ${errorText}`)
          }
        } catch (error) {
          debugLogger.error('Session validation error', error, {
            token: token ? token.substring(0, 20) + '...' : 'none',
            savedUser: savedUser ? 'exists' : 'none'
          })
          // مسح البيانات المحلية إذا فشل التحقق
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
        }
      }
      // إذا كان هناك عميل مسجل دخول
      else if (clientData) {
        try {
          const client = JSON.parse(clientData)
          debugLogger.auth('Validating client session', {
            clientId: client.id,
            clientCode: client.clientCode,
            hasToken: !!client.token
          })

          // التحقق من صحة بيانات العميل
          if (client.token) {
            const response = await fetch('/api/auth/validate', {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${client.token}`,
                'Content-Type': 'application/json'
              },
              credentials: 'include'
            })

            debugLogger.network('Client session validation response',
              { url: '/api/auth/validate', method: 'GET' },
              { status: response.status, ok: response.ok }
            )

            if (response.ok) {
              const result = await response.json()
              if (result.success && result.user) {
                const clientUser = {
                  ...result.user,
                  userType: 'client',
                  isClient: true,
                  clientData: client
                }
                setUser(clientUser)
                debugLogger.success('Client session validated successfully', clientUser)
              } else {
                throw new Error('Invalid client session response: ' + JSON.stringify(result))
              }
            } else {
              const errorText = await response.text()
              throw new Error(`Client session validation failed: ${response.status} - ${errorText}`)
            }
          } else {
            // إنشاء كائن مستخدم وهمي للعميل (للتوافق مع النظام القديم)
            const clientUser = {
              id: client.id,
              username: client.clientName,
              loginName: client.clientCode.toString(),
              userType: 'client',
              isClient: true,
              clientData: client
            }
            setUser(clientUser)
            debugLogger.info('Client session created without server validation', clientUser)
          }
        } catch (error) {
          debugLogger.error('Error validating client session', error, { clientData })
          localStorage.removeItem('clientData')
          setUser(null)
        }
      } else {
        debugLogger.info('No existing session found')
      }

      setLoading(false)
      debugLogger.auth('Session validation completed', {
        hasUser: !!user,
        loading: false
      })
    }

    validateSession()
  }, [])

  const login = async (loginName, password, deviceId) => {
    // إنشاء deviceId تلقائي إذا لم يتم توفيره
    const finalDeviceId = deviceId || `web-${loginName}-${Date.now()}`;

    debugLogger.auth('Starting user login', {
      loginName,
      hasPassword: !!password,
      deviceId: finalDeviceId,
      userType: 'user'
    })

    try {
      const requestBody = {
        loginName,
        password,
        deviceId: finalDeviceId,
        userType: 'user' // تحديد نوع المستخدم صراحة
      }

      debugLogger.network('Sending login request', {
        url: '/api/auth/login',
        method: 'POST',
        body: { ...requestBody, password: '***' }
      })

      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session management
        body: JSON.stringify(requestBody)
      })

      const result = await response.json()

      debugLogger.network('Login response received',
        { url: '/api/auth/login', method: 'POST' },
        {
          status: response.status,
          ok: response.ok,
          data: { ...result, token: result.token ? 'present' : 'missing' }
        }
      )

      // تبسيط المنطق - إذا كان الخادم يرد بـ 200 فهذا يعني نجاح
      if (response.ok && result.success) {
        debugLogger.auth('Login successful, saving data', {
          hasToken: !!result.token,
          hasUser: !!result.user,
          sessionId: result.sessionId
        })

        // حفظ البيانات محلياً
        if (result.token) {
          localStorage.setItem('token', result.token)
          debugLogger.info('Token saved to localStorage')
        }
        if (result.user) {
          localStorage.setItem('user', JSON.stringify(result.user))
          setUser(result.user)
          debugLogger.info('User data saved', result.user)
        }

        debugLogger.success('User login completed successfully', {
          userId: result.user?.id,
          username: result.user?.username,
          accountType: result.user?.accountType,
          sessionId: result.sessionId
        })

        return {
          success: true,
          user: result.user,
          token: result.token,
          sessionId: result.sessionId
        }
      } else {
        debugLogger.warning('Login failed', {
          status: response.status,
          error: result.message || result.error,
          details: result.details
        })

        return {
          success: false,
          error: result.message || result.error || `خطأ HTTP ${response.status}`,
          details: result.details
        }
      }
    } catch (error) {
      debugLogger.error('Login request failed', error, {
        loginName,
        deviceId
      })

      return {
        success: false,
        error: 'حدث خطأ في الاتصال بالخادم'
      }
    }
  }

  const clientLogin = async (clientCode, password) => {
    debugLogger.auth('Starting client login', {
      clientCode,
      hasPassword: !!password,
      userType: 'client'
    })

    try {
      const requestBody = {
        loginName: clientCode.toString(),
        password,
        userType: 'client', // تحديد نوع المستخدم صراحة
        deviceId: generateDeviceId() // إضافة deviceId للعملاء أيضاً
      }

      debugLogger.network('Sending client login request', {
        url: '/api/auth/login',
        method: 'POST',
        body: { ...requestBody, password: '***' }
      })

      // استخدم مسار المصادقة الموحد مع تحديد نوع المستخدم "client"
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      })

      const result = await response.json()

      debugLogger.network('Client login response received',
        { url: '/api/auth/login', method: 'POST' },
        {
          status: response.status,
          ok: response.ok,
          data: { ...result, token: result.token ? 'present' : 'missing' }
        }
      )

      if (response.ok && result.success) {
        debugLogger.auth('Client login successful, processing data', {
          hasUser: !!result.user,
          hasToken: !!result.token
        })

        // هيكل البيانات الموحد يعيد الكائن في result.user
        const client = result.user

        // حفظ بيانات العميل محلياً للتعامل مع وضع عدم الاتصال أو التحديثات السريعة
        const clientData = {
          id: client.id,
          clientCode: client.clientCode || clientCode,
          clientName: client.username,
          token: result.token || null,
          appName: client.appName,
          loginTime: new Date().toISOString()
        }

        localStorage.setItem('clientData', JSON.stringify(clientData))
        if (result.token) {
          localStorage.setItem('token', result.token)
        }

        // تحديث حالة المستخدم في AuthContext
        const clientUser = {
          ...client,
          userType: 'client',
          isClient: true,
          clientData: clientData
        }
        setUser(clientUser)

        debugLogger.success('Client login completed successfully', {
          clientId: client.id,
          clientCode: client.clientCode || clientCode,
          clientName: client.username,
          sessionId: result.sessionId
        })

        return { success: true, client: clientUser }
      } else {
        debugLogger.warning('Client login failed', {
          status: response.status,
          error: result.message || result.error,
          details: result.details
        })

        return {
          success: false,
          error: result.message || result.error || `خطأ HTTP ${response.status}`
        }
      }
    } catch (error) {
      debugLogger.error('Client login request failed', error, {
        clientCode
      })

      return {
        success: false,
        error: 'حدث خطأ في الاتصال بالخادم'
      }
    }
  }

  const logout = async () => {
    try {
      console.log('🚪 تسجيل خروج...')

      // إرسال طلب تسجيل الخروج للخادم
      const token = localStorage.getItem('token')
      if (token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        })
      }
    } catch (error) {
      console.error('Logout request error:', error)
      // نكمل عملية تسجيل الخروج حتى لو فشل الطلب
    } finally {
      // مسح البيانات المحلية
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('clientData')
      setUser(null)
      console.log('✅ تم تسجيل الخروج محلياً')
    }
  }

  // دالة لإضافة التوكن للطلبات تلقائياً
  const makeAuthenticatedRequest = async (url, options = {}) => {
    const token = localStorage.getItem('token')

    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      credentials: 'include',
      ...options
    }

    // دمج headers إذا كانت موجودة في options
    if (options.headers) {
      defaultOptions.headers = {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    try {
      const response = await fetch(url, defaultOptions)

      // إذا كان الرد 401 (غير مصرح) فهذا يعني انتهاء صلاحية التوكن
      if (response.status === 401) {
        console.warn('Token expired or invalid, logging out...')
        await logout()
        throw new Error('Session expired')
      }

      return response
    } catch (error) {
      console.error('Authenticated request error:', error)
      throw error
    }
  }

  const hasPermission = (resource, action) => {
    // التحقق من وجود المستخدم
    if (!user || !user.permissions) return false

    // الأدمن له كل الصلاحيات
    if (user.permissions.isAdmin) return true

    // التحقق من الصلاحية المحددة
    return user.permissions[resource]?.[action] || false
  }

  // إنشاء كائن API مع التوكن والـ device ID
  const api = {
    get: async (url, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      // معالجة query parameters
      let finalUrl = url
      if (config.params) {
        const searchParams = new URLSearchParams()
        Object.keys(config.params).forEach(key => {
          if (config.params[key] !== undefined && config.params[key] !== null && config.params[key] !== '') {
            searchParams.append(key, config.params[key])
          }
        })
        const queryString = searchParams.toString()
        if (queryString) {
          finalUrl += (url.includes('?') ? '&' : '?') + queryString
        }
      }

      const response = await fetch(finalUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        }
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // إذا كان التوكن غير صالح، قم بتسجيل الخروج
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    post: async (url, data, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    put: async (url, data, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    delete: async (url, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        }
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    }
  }

  const value = {
    user,
    loading,
    login,
    clientLogin,
    logout,
    hasPermission,
    makeAuthenticatedRequest,
    api
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// خطأ useAuth إذا لم توجد
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
