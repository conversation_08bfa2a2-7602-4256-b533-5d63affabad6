import React, { createContext, useContext, useState, useEffect } from 'react'

// إنشاء Context لحالة المصادقة
const AuthContext = createContext({})

console.log('🎯 النظام يعمل مع قاعدة البيانات الحقيقية')

// دالة لتوليد رقم جهاز فريد
const generateDeviceId = () => {
  const stored = localStorage.getItem('deviceId')
  if (stored) return stored

  const deviceId = `device_${Math.random().toString(36).substring(2, 11)}_${Date.now()}`
  localStorage.setItem('deviceId', deviceId)
  return deviceId
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const validateSession = async () => {
      const token = localStorage.getItem('token')
      const savedUser = localStorage.getItem('user')
      const clientData = localStorage.getItem('clientData')

      // إذا كان هناك مستخدم عادي
      if (token && savedUser) {
        try {
          // التحقق من صحة الجلسة مع الخادم
          const response = await fetch('/api/auth/validate', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            credentials: 'include'
          })

          if (response.ok) {
            const result = await response.json()
            if (result.success && result.user) {
              setUser(result.user)
              // تحديث البيانات المحلية
              localStorage.setItem('user', JSON.stringify(result.user))
              console.log('✅ Session validated successfully')
            } else {
              throw new Error('Invalid session response')
            }
          } else {
            throw new Error('Session validation failed')
          }
        } catch (error) {
          console.error('Session validation error:', error)
          // مسح البيانات المحلية إذا فشل التحقق
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
        }
      }
      // إذا كان هناك عميل مسجل دخول
      else if (clientData) {
        try {
          const client = JSON.parse(clientData)
          // التحقق من صحة بيانات العميل
          if (client.token) {
            const response = await fetch('/api/auth/validate', {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${client.token}`,
                'Content-Type': 'application/json'
              },
              credentials: 'include'
            })

            if (response.ok) {
              const result = await response.json()
              if (result.success && result.user) {
                setUser({
                  ...result.user,
                  userType: 'client',
                  isClient: true,
                  clientData: client
                })
                console.log('✅ Client session validated successfully')
              } else {
                throw new Error('Invalid client session')
              }
            } else {
              throw new Error('Client session validation failed')
            }
          } else {
            // إنشاء كائن مستخدم وهمي للعميل (للتوافق مع النظام القديم)
            setUser({
              id: client.id,
              username: client.clientName,
              loginName: client.clientCode.toString(),
              userType: 'client',
              isClient: true,
              clientData: client
            })
          }
        } catch (error) {
          console.error('Error validating client session:', error)
          localStorage.removeItem('clientData')
          setUser(null)
        }
      }

      setLoading(false)
    }

    validateSession()
  }, [])

  const login = async (loginName, password, deviceId) => {
    console.log('🚀 تسجيل دخول عبر الخادم - من قاعدة البيانات')
    console.log('🚀 Parameters received:', { loginName, password: password ? '***' : 'undefined', deviceId })

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for session management
        body: JSON.stringify({
          loginName,
          password,
          deviceId
        })
      })

      const result = await response.json()

      console.log('🔍 Response status:', response.status)
      console.log('🔍 Response ok:', response.ok)
      console.log('🔍 Response data:', result)

      // تبسيط المنطق - إذا كان الخادم يرد بـ 200 فهذا يعني نجاح
      if (response.ok && result.success) {
        // حفظ البيانات محلياً
        if (result.token) {
          localStorage.setItem('token', result.token)
        }
        if (result.user) {
          localStorage.setItem('user', JSON.stringify(result.user))
          setUser(result.user)
        }

        console.log('✅ تم تسجيل الدخول بنجاح (HTTP 200)')
        console.log('✅ User data saved:', result.user)
        console.log('✅ Token saved:', result.token ? 'Yes' : 'No')
        console.log('✅ Session ID:', result.sessionId)

        return {
          success: true,
          user: result.user,
          token: result.token,
          sessionId: result.sessionId
        }
      } else {
        console.log('❌ فشل تسجيل الدخول - HTTP Status:', response.status)
        console.log('❌ Error details:', result)
        return {
          success: false,
          error: result.message || result.error || `خطأ HTTP ${response.status}`,
          details: result.details
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'حدث خطأ في الاتصال بالخادم'
      }
    }
  }

  const clientLogin = async (clientCode, password) => {
    console.log('🏢 تسجيل دخول العميل عبر الخادم - من قاعدة البيانات')
    console.log('🏢 Parameters received:', { clientCode, password: password ? '***' : 'undefined' })

    try {
      // استخدم مسار المصادقة الموحد مع تحديد نوع المستخدم "client"
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          loginName: clientCode.toString(),
          password,
          userType: 'client'
        })
      })

      const result = await response.json()

      console.log('🔍 Client Response status:', response.status)
      console.log('🔍 Client Response ok:', response.ok)
      console.log('🔍 Client Response data:', result)

      if (response.ok && result.success) {
        // هيكل البيانات الموحد يعيد الكائن في result.user
        const client = result.user

        // حفظ بيانات العميل محلياً للتعامل مع وضع عدم الاتصال أو التحديثات السريعة
        const clientData = {
          id: client.id,
          clientCode: client.clientCode,
          clientName: client.username,
          token: result.token || null,
          appName: client.appName,
          loginTime: new Date().toISOString()
        }

        localStorage.setItem('clientData', JSON.stringify(clientData))
        if (result.token) {
          localStorage.setItem('token', result.token)
        }

        // تحديث حالة المستخدم في AuthContext
        setUser({
          ...client,
          userType: 'client',
          isClient: true,
          clientData: clientData
        })

        console.log('✅ تم تسجيل دخول العميل بنجاح (HTTP 200)')
        return { success: true, client }
      } else {
        console.log('❌ فشل تسجيل دخول العميل - HTTP Status:', response.status)
        return {
          success: false,
          error: result.message || `خطأ HTTP ${response.status}`
        }
      }
    } catch (error) {
      console.error('Client login error:', error)
      return {
        success: false,
        error: 'حدث خطأ في الاتصال بالخادم'
      }
    }
  }

  const logout = async () => {
    try {
      console.log('🚪 تسجيل خروج...')

      // إرسال طلب تسجيل الخروج للخادم
      const token = localStorage.getItem('token')
      if (token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          credentials: 'include'
        })
      }
    } catch (error) {
      console.error('Logout request error:', error)
      // نكمل عملية تسجيل الخروج حتى لو فشل الطلب
    } finally {
      // مسح البيانات المحلية
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('clientData')
      setUser(null)
      console.log('✅ تم تسجيل الخروج محلياً')
    }
  }

  // دالة لإضافة التوكن للطلبات تلقائياً
  const makeAuthenticatedRequest = async (url, options = {}) => {
    const token = localStorage.getItem('token')

    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` })
      },
      credentials: 'include',
      ...options
    }

    // دمج headers إذا كانت موجودة في options
    if (options.headers) {
      defaultOptions.headers = {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    try {
      const response = await fetch(url, defaultOptions)

      // إذا كان الرد 401 (غير مصرح) فهذا يعني انتهاء صلاحية التوكن
      if (response.status === 401) {
        console.warn('Token expired or invalid, logging out...')
        await logout()
        throw new Error('Session expired')
      }

      return response
    } catch (error) {
      console.error('Authenticated request error:', error)
      throw error
    }
  }

  const hasPermission = (resource, action) => {
    // التحقق من وجود المستخدم
    if (!user || !user.permissions) return false

    // الأدمن له كل الصلاحيات
    if (user.permissions.isAdmin) return true

    // التحقق من الصلاحية المحددة
    return user.permissions[resource]?.[action] || false
  }

  // إنشاء كائن API مع التوكن والـ device ID
  const api = {
    get: async (url, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      // معالجة query parameters
      let finalUrl = url
      if (config.params) {
        const searchParams = new URLSearchParams()
        Object.keys(config.params).forEach(key => {
          if (config.params[key] !== undefined && config.params[key] !== null && config.params[key] !== '') {
            searchParams.append(key, config.params[key])
          }
        })
        const queryString = searchParams.toString()
        if (queryString) {
          finalUrl += (url.includes('?') ? '&' : '?') + queryString
        }
      }

      const response = await fetch(finalUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        }
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          // إذا كان التوكن غير صالح، قم بتسجيل الخروج
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    post: async (url, data, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    put: async (url, data, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    },

    delete: async (url, config = {}) => {
      const token = localStorage.getItem('token')
      const deviceId = generateDeviceId()

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
          'X-Device-ID': deviceId,
          ...config.headers
        }
      })

      if (!response.ok) {
        if (response.status === 401 || response.status === 403) {
          localStorage.removeItem('token')
          localStorage.removeItem('user')
          setUser(null)
          window.location.href = '/login'
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return {
        data: await response.json()
      }
    }
  }

  const value = {
    user,
    loading,
    login,
    clientLogin,
    logout,
    hasPermission,
    makeAuthenticatedRequest,
    api
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// خطأ useAuth إذا لم توجد
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
