@echo off
echo ========================================
echo    تشغيل خادم نظام إدارة العملاء اليمني
echo ========================================
echo.

echo 1. إيقاف أي خوادم سابقة...
taskkill /F /IM node.exe /T >nul 2>&1

echo 2. التحقق من المجلد...
if not exist "C:\yemclinet\server" (
    echo خطأ: مجلد الخادم غير موجود
    echo المسار المطلوب: C:\yemclinet\server
    pause
    exit /b 1
)

echo 3. الانتقال لمجلد الخادم...
cd /d "C:\yemclinet\server"

echo 4. التحقق من ملف الخادم...
if not exist "working-server.js" (
    echo خطأ: ملف الخادم غير موجود
    echo الملف المطلوب: working-server.js
    pause
    exit /b 1
)

echo 5. بدء تشغيل الخادم...
echo.
echo ========================================
echo الخادم يعمل الآن على:
echo - محلي: http://localhost:8080
echo - خارجي: http://***********:8080
echo ========================================
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

node working-server.js

echo.
echo تم إيقاف الخادم
pause
