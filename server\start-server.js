#!/usr/bin/env node

/**
 * YemClient Production Server Starter
 * Enhanced startup script with better error handling and logging
 */

const path = require('path');
const fs = require('fs');

// Set environment variables
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
process.env.PORT = process.env.PORT || '8080';

// Ensure logs directory exists
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
  console.log('📁 Created logs directory');
}

// Load environment variables
require('dotenv').config();

console.log('🚀 Starting YemClient Server...');
console.log('📊 Environment:', process.env.NODE_ENV);
console.log('🌐 Port:', process.env.PORT);
console.log('🔧 Node Version:', process.version);
console.log('💻 Platform:', process.platform);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
  
  // Give the server time to finish current requests
  setTimeout(() => {
    console.log('✅ Graceful shutdown completed');
    process.exit(0);
  }, 5000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Start the server
try {
  require('./production-server.js');
  console.log('✅ Server module loaded successfully');
} catch (error) {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
}
