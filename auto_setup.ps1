# نص PowerShell لإعداد نظام إدارة العملاء والوكلاء تلقائياً
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    إعداد نظام إدارة العملاء والوكلاء" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# التحقق من وجود Node.js
Write-Host "`n1. التحقق من Node.js..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js موجود: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js غير مثبت!" -ForegroundColor Red
    Write-Host "يرجى تحميل وتثبيت Node.js من: https://nodejs.org" -ForegroundColor Yellow
    Read-Host "اضغط Enter للمتابعة بعد تثبيت Node.js"
    exit 1
}

# التحقق من وجود PostgreSQL
Write-Host "`n2. التحقق من PostgreSQL..." -ForegroundColor Yellow
try {
    $psqlVersion = psql --version
    Write-Host "✓ PostgreSQL موجود: $psqlVersion" -ForegroundColor Green
    $hasPostgres = $true
} catch {
    Write-Host "✗ PostgreSQL غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    Write-Host "سيتم استخدام Docker كبديل..." -ForegroundColor Yellow
    $hasPostgres = $false
}

# التحقق من وجود Docker
if (-not $hasPostgres) {
    Write-Host "`n3. التحقق من Docker..." -ForegroundColor Yellow
    try {
        $dockerVersion = docker --version
        Write-Host "✓ Docker موجود: $dockerVersion" -ForegroundColor Green
        $useDocker = $true
    } catch {
        Write-Host "✗ Docker غير مثبت!" -ForegroundColor Red
        Write-Host "يرجى تثبيت PostgreSQL أو Docker للمتابعة" -ForegroundColor Yellow
        Read-Host "اضغط Enter للخروج"
        exit 1
    }
}

if ($hasPostgres) {
    # الطريقة المحلية مع PostgreSQL
    Write-Host "`n=== استخدام PostgreSQL المحلي ===" -ForegroundColor Cyan
    
    # إنشاء قاعدة البيانات
    Write-Host "`n4. إنشاء قاعدة البيانات..." -ForegroundColor Yellow
    $env:PGPASSWORD = "yemen123"
    try {
        createdb -U postgres yemclient_db 2>$null
        Write-Host "✓ تم إنشاء قاعدة البيانات yemclient_db" -ForegroundColor Green
    } catch {
        Write-Host "⚠ قاعدة البيانات موجودة مسبقاً أو حدث خطأ" -ForegroundColor Yellow
    }
    
    # تثبيت متطلبات الخادم
    Write-Host "`n5. تثبيت متطلبات الخادم..." -ForegroundColor Yellow
    Set-Location server
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم تثبيت متطلبات الخادم" -ForegroundColor Green
    } else {
        Write-Host "✗ فشل في تثبيت متطلبات الخادم" -ForegroundColor Red
        exit 1
    }
    
    # إنشاء الجداول
    Write-Host "`n6. إنشاء الجداول..." -ForegroundColor Yellow
    npx prisma migrate dev --name init
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم إنشاء الجداول" -ForegroundColor Green
    } else {
        Write-Host "✗ فشل في إنشاء الجداول" -ForegroundColor Red
    }
    
    # إدخال البيانات التجريبية
    Write-Host "`n7. إدخال البيانات التجريبية..." -ForegroundColor Yellow
    npm run db:seed
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم إدخال البيانات التجريبية" -ForegroundColor Green
    } else {
        Write-Host "✗ فشل في إدخال البيانات التجريبية" -ForegroundColor Red
    }
    
    # تثبيت متطلبات العميل
    Write-Host "`n8. تثبيت متطلبات العميل..." -ForegroundColor Yellow
    Set-Location ..\client
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم تثبيت متطلبات العميل" -ForegroundColor Green
    } else {
        Write-Host "✗ فشل في تثبيت متطلبات العميل" -ForegroundColor Red
        exit 1
    }
    
    Set-Location ..
    
} else {
    # الطريقة باستخدام Docker
    Write-Host "`n=== استخدام Docker ===" -ForegroundColor Cyan
    
    Write-Host "`n4. إيقاف الحاويات السابقة..." -ForegroundColor Yellow
    docker-compose down 2>$null
    
    Write-Host "`n5. تشغيل النظام بـ Docker..." -ForegroundColor Yellow
    docker-compose up -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ تم تشغيل النظام بـ Docker" -ForegroundColor Green
        Write-Host "انتظار تشغيل الخدمات..." -ForegroundColor Yellow
        Start-Sleep -Seconds 30
    } else {
        Write-Host "✗ فشل في تشغيل Docker" -ForegroundColor Red
        exit 1
    }
}

# عرض معلومات الوصول
Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "       تم إعداد النظام بنجاح!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "`n📊 معلومات الوصول:" -ForegroundColor Yellow
Write-Host "لوحة التحكم: http://localhost:5173" -ForegroundColor White
Write-Host "API الخادم: http://localhost:3000" -ForegroundColor White

if ($useDocker) {
    Write-Host "pgAdmin: http://localhost:5050" -ForegroundColor White
    Write-Host "`n🔑 بيانات دخول pgAdmin:" -ForegroundColor Yellow
    Write-Host "Email: <EMAIL>" -ForegroundColor White
    Write-Host "Password: admin123456" -ForegroundColor White
    Write-Host "`n📋 معلومات قاعدة البيانات في pgAdmin:" -ForegroundColor Yellow
    Write-Host "Host: postgres" -ForegroundColor White
    Write-Host "Port: 5432" -ForegroundColor White
    Write-Host "Database: yemclient_db" -ForegroundColor White
    Write-Host "Username: yemclient_user" -ForegroundColor White
    Write-Host "Password: yemclient_password" -ForegroundColor White
} else {
    Write-Host "`n📋 معلومات قاعدة البيانات في pgAdmin:" -ForegroundColor Yellow
    Write-Host "Host: localhost" -ForegroundColor White
    Write-Host "Port: 5432" -ForegroundColor White
    Write-Host "Database: yemclient_db" -ForegroundColor White
    Write-Host "Username: postgres" -ForegroundColor White
    Write-Host "Password: yemen123" -ForegroundColor White
}

Write-Host "`n🔑 بيانات دخول النظام:" -ForegroundColor Yellow
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: admin123456" -ForegroundColor White

Write-Host "`n🚀 لتشغيل النظام:" -ForegroundColor Yellow
if ($hasPostgres) {
    Write-Host "npm run dev" -ForegroundColor White
} else {
    Write-Host "النظام يعمل بالفعل مع Docker" -ForegroundColor Green
}

Write-Host "`n📚 ملفات مفيدة:" -ForegroundColor Yellow
Write-Host "README.md - دليل شامل" -ForegroundColor White
Write-Host "API_DOCUMENTATION.md - توثيق API" -ForegroundColor White
Write-Host "START_HERE.md - دليل البداية السريعة" -ForegroundColor White

Write-Host "`n✨ النظام جاهز للاستخدام!" -ForegroundColor Green
Read-Host "`nاضغط Enter للخروج"
