import React from 'react';
import { Box } from '@mui/material';

// خريطة الأيقونات البديلة
const iconFallbacks = {
  Add: '➕',
  Edit: '✏️',
  Delete: '🗑️',
  Visibility: '👁️',
  Search: '🔍',
  Person: '👤',
  Group: '👥',
  Business: '🏢',
  Dashboard: '📊',
  Security: '🔒',
  Settings: '⚙️',
  Home: '🏠',
  AccountCircle: '👤',
  ExitToApp: '🚪',
  FilterList: '🔽',
  MoreVert: '⋮',
  Check: '✅',
  Close: '❌',
  Warning: '⚠️',
  Info: 'ℹ️',
  Error: '❌',
  Success: '✅'
};

const IconWithFallback = ({ 
  icon: IconComponent, 
  fallbackName, 
  sx = {}, 
  fontSize = 'medium',
  color = 'inherit',
  ...props 
}) => {
  const fallbackIcon = iconFallbacks[fallbackName] || '⚙️';
  
  // محاولة عرض الأيقونة الأصلية
  if (IconComponent) {
    try {
      return (
        <IconComponent 
          sx={{ 
            fontSize: fontSize === 'small' ? '1.2rem' : fontSize === 'large' ? '2rem' : '1.5rem',
            color,
            ...sx 
          }} 
          {...props} 
        />
      );
    } catch (error) {
      console.warn(`Failed to render icon ${fallbackName}:`, error);
    }
  }
  
  // عرض الأيقونة البديلة
  return (
    <Box
      component="span"
      sx={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: fontSize === 'small' ? '1rem' : fontSize === 'large' ? '1.5rem' : '1.2rem',
        lineHeight: 1,
        color,
        ...sx
      }}
      {...props}
    >
      {fallbackIcon}
    </Box>
  );
};

export default IconWithFallback;
