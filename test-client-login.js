/**
 * اختبار دخول العميل
 */

async function testClientLogin() {
  console.log('🏢 اختبار دخول العميل...\n');

  try {
    // اختبار 1: دخول العميل 1001
    console.log('1️⃣ اختبار دخول العميل 1001:');
    
    const response1 = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 1001,
        password: 'Hash2020@'
      })
    });

    console.log(`   📡 Status: ${response1.status}`);
    
    if (response1.ok) {
      const data1 = await response1.json();
      console.log('   ✅ نجح دخول العميل 1001!');
      console.log(`   🏢 اسم العميل: ${data1.client?.clientName}`);
      console.log(`   🔢 رمز العميل: ${data1.client?.clientCode}`);
      console.log(`   📱 التطبيق: ${data1.client?.appName}`);
      console.log(`   🎫 التوكن: ${data1.client?.token || 'غير محدد'}`);
      console.log(`   📊 الحالة: ${data1.client?.status}`);
    } else {
      const error1 = await response1.json();
      console.log('   ❌ فشل دخول العميل 1001');
      console.log(`   📝 الخطأ: ${error1.message}`);
    }
    console.log('');

    // اختبار 2: دخول العميل 1000
    console.log('2️⃣ اختبار دخول العميل 1000:');
    
    const response2 = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 1000,
        password: '112223333'
      })
    });

    console.log(`   📡 Status: ${response2.status}`);
    
    if (response2.ok) {
      const data2 = await response2.json();
      console.log('   ✅ نجح دخول العميل 1000!');
      console.log(`   🏢 اسم العميل: ${data2.client?.clientName}`);
      console.log(`   🔢 رمز العميل: ${data2.client?.clientCode}`);
      console.log(`   📱 التطبيق: ${data2.client?.appName}`);
      console.log(`   🎫 التوكن: ${data2.client?.token || 'غير محدد'}`);
      console.log(`   📊 الحالة: ${data2.client?.status}`);
    } else {
      const error2 = await response2.json();
      console.log('   ❌ فشل دخول العميل 1000');
      console.log(`   📝 الخطأ: ${error2.message}`);
    }
    console.log('');

    // اختبار 3: دخول بكلمة مرور خاطئة
    console.log('3️⃣ اختبار دخول بكلمة مرور خاطئة:');
    
    const response3 = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 1001,
        password: 'wrongpassword'
      })
    });

    console.log(`   📡 Status: ${response3.status}`);
    
    if (!response3.ok) {
      const error3 = await response3.json();
      console.log('   ✅ تم رفض كلمة المرور الخاطئة بنجاح');
      console.log(`   📝 رسالة الخطأ: ${error3.message}`);
    } else {
      console.log('   ❌ تم قبول كلمة مرور خاطئة (مشكلة أمنية!)');
    }
    console.log('');

    // اختبار 4: دخول برمز عميل غير موجود
    console.log('4️⃣ اختبار دخول برمز عميل غير موجود:');
    
    const response4 = await fetch('http://localhost:8080/api/client/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        clientCode: 9999,
        password: 'anypassword'
      })
    });

    console.log(`   📡 Status: ${response4.status}`);
    
    if (!response4.ok) {
      const error4 = await response4.json();
      console.log('   ✅ تم رفض رمز العميل غير الموجود بنجاح');
      console.log(`   📝 رسالة الخطأ: ${error4.message}`);
    } else {
      console.log('   ❌ تم قبول رمز عميل غير موجود (مشكلة أمنية!)');
    }

    console.log('\n' + '='.repeat(70));
    console.log('📋 ملخص اختبار دخول العميل:');
    console.log('');
    console.log('✅ الاختبارات المطلوبة:');
    console.log('   🔍 التحقق من رمز العميل في عمود client_code');
    console.log('   🔐 التحقق من كلمة المرور في عمود password');
    console.log('   📊 التحقق من حالة العميل (status = 1)');
    console.log('   🔒 رفض كلمات المرور الخاطئة');
    console.log('   🚫 رفض أرقام العملاء غير الموجودة');
    console.log('');
    console.log('💡 بيانات الاختبار الصحيحة:');
    console.log('   🏢 العميل 1001: كلمة المرور Hash2020@');
    console.log('   🏢 العميل 1000: كلمة المرور 112223333');
    console.log('');
    console.log('🌐 للاختبار من المتصفح:');
    console.log('   📍 افتح: http://localhost:8080');
    console.log('   🔄 اختر: دخول عميل');
    console.log('   📝 أدخل: رمز العميل وكلمة المرور');
    console.log('   ➡️ ستنتقل لصفحة: /client-dashboard');

  } catch (error) {
    console.error('❌ خطأ في اختبار دخول العميل:', error.message);
  }
}

testClientLogin().catch(console.error);
