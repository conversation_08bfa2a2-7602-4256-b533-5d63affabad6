const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function debugDevice1Issue() {
  try {
    console.log('🔍 تشخيص مشكلة العمود device1')
    console.log('==============================')
    
    // اختبار 1: قراءة البيانات باستخدام SQL مباشر
    console.log('📝 اختبار 1: قراءة البيانات باستخدام SQL مباشر...')
    
    const sqlResults = await prisma.$queryRaw`
      SELECT user_id, username, login_name, device_id, device1, is_active
      FROM users 
      WHERE login_name = 'hash8080'
    `
    
    if (sqlResults.length > 0) {
      const user = sqlResults[0]
      console.log(`👤 المستخدم: ${user.username}`)
      console.log(`📱 device_id: ${user.device_id || 'فارغ'}`)
      console.log(`📱 device1: ${user.device1 || 'فارغ'}`)
    } else {
      console.log('❌ لم يتم العثور على المستخدم')
      return
    }
    
    // اختبار 2: محاكاة منطق التحقق من الجهاز
    console.log('')
    console.log('📝 اختبار 2: محاكاة منطق التحقق من الجهاز...')
    
    const user = sqlResults[0]
    const testDeviceId = 'b78dex9jv_1751070014503'
    
    let isDeviceAllowed = false;
    let allowedDevices = [];
    
    // التحقق من العمود الجديد device1 أولاً
    console.log(`🔍 التحقق من device1: ${user.device1}`)
    if (user.device1) {
      allowedDevices.push(user.device1);
      if (user.device1 === testDeviceId) {
        isDeviceAllowed = true;
        console.log('✅ الجهاز موجود في device1')
      } else {
        console.log('❌ الجهاز غير موجود في device1')
      }
    } else {
      console.log('⚠️ device1 فارغ')
    }
    
    // التحقق من العمود القديم deviceId
    console.log(`🔍 التحقق من device_id: ${user.device_id}`)
    if (user.device_id && !isDeviceAllowed) {
      const oldDevices = user.device_id.includes(',')
        ? user.device_id.split(',').map(id => id.trim())
        : [user.device_id];
      
      allowedDevices = [...allowedDevices, ...oldDevices];
      
      if (oldDevices.includes(testDeviceId)) {
        isDeviceAllowed = true;
        console.log('✅ الجهاز موجود في device_id')
      } else {
        console.log('❌ الجهاز غير موجود في device_id')
      }
    }
    
    // إزالة التكرارات
    allowedDevices = [...new Set(allowedDevices)];
    
    console.log(`📱 الأجهزة المسموحة: ${allowedDevices.join(', ')}`)
    console.log(`🔐 هل الجهاز مسموح: ${isDeviceAllowed ? 'نعم ✅' : 'لا ❌'}`)
    
    // اختبار 3: محاكاة استعلام Prisma الحالي
    console.log('')
    console.log('📝 اختبار 3: محاكاة استعلام Prisma الحالي...')
    
    try {
      const prismaUser = await prisma.user.findUnique({
        where: { loginName: 'hash8080' }
      })
      
      if (prismaUser) {
        console.log('📊 بيانات Prisma:')
        console.log(`   - deviceId: ${prismaUser.deviceId || 'فارغ'}`)
        console.log(`   - device1: ${prismaUser.device1 || 'غير موجود في النموذج'}`)
        console.log(`   - الخصائص المتاحة: ${Object.keys(prismaUser).join(', ')}`)
      }
    } catch (error) {
      console.log(`❌ خطأ في استعلام Prisma: ${error.message}`)
    }
    
    // اختبار 4: اختبار الاستعلام الجديد بـ SQL مباشر
    console.log('')
    console.log('📝 اختبار 4: اختبار الاستعلام الجديد بـ SQL مباشر...')
    
    try {
      const newQueryResults = await prisma.$queryRaw`
        SELECT user_id as id, username, login_name as "loginName", password, device_id as "deviceId", device1, is_active as "isActive", permissions
        FROM users 
        WHERE login_name = 'hash8080'
        LIMIT 1
      `;
      
      if (newQueryResults.length > 0) {
        const newUser = newQueryResults[0]
        console.log('📊 بيانات الاستعلام الجديد:')
        console.log(`   - deviceId: ${newUser.deviceId || 'فارغ'}`)
        console.log(`   - device1: ${newUser.device1 || 'فارغ'}`)
        console.log(`   - الخصائص المتاحة: ${Object.keys(newUser).join(', ')}`)
        
        // اختبار منطق التحقق مع البيانات الجديدة
        let newIsDeviceAllowed = false;
        let newAllowedDevices = [];
        
        if (newUser.device1) {
          newAllowedDevices.push(newUser.device1);
          if (newUser.device1 === testDeviceId) {
            newIsDeviceAllowed = true;
          }
        }
        
        if (newUser.deviceId && !newIsDeviceAllowed) {
          const oldDevices = newUser.deviceId.includes(',')
            ? newUser.deviceId.split(',').map(id => id.trim())
            : [newUser.deviceId];
          
          newAllowedDevices = [...newAllowedDevices, ...oldDevices];
          
          if (oldDevices.includes(testDeviceId)) {
            newIsDeviceAllowed = true;
          }
        }
        
        newAllowedDevices = [...new Set(newAllowedDevices)];
        
        console.log(`📱 الأجهزة المسموحة (جديد): ${newAllowedDevices.join(', ')}`)
        console.log(`🔐 هل الجهاز مسموح (جديد): ${newIsDeviceAllowed ? 'نعم ✅' : 'لا ❌'}`)
      }
    } catch (error) {
      console.log(`❌ خطأ في الاستعلام الجديد: ${error.message}`)
    }
    
    console.log('')
    console.log('🎯 الخلاصة:')
    console.log('============')
    console.log('المشكلة تكمن في أن Prisma لا يقرأ العمود device1 الجديد')
    console.log('الحل: استخدام SQL مباشر في auth.js')
    
  } catch (error) {
    console.error('❌ خطأ في التشخيص:', error)
  } finally {
    await prisma.$disconnect()
  }
}

debugDevice1Issue()
