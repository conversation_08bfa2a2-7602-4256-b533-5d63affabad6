const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const fs = require('fs');
const path = require('path');

const app = express();
const PORT = 3000;
const TARGET_PORT = 8080;

// إنشاء مجلد logs إذا لم يكن موجوداً
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// دالة للكتابة في ملف اللوق
function writeLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  // طباعة في الكونسول
  console.log(`🔄 PROXY: ${message}`);

  // كتابة في ملف
  const logFile = path.join(logsDir, `proxy-${new Date().toISOString().split('T')[0]}.log`);
  fs.appendFileSync(logFile, logMessage);
}

// middleware للتسجيل قبل البروكسي
app.use((req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
  const userAgent = req.headers['user-agent'] || 'Unknown';
  const referer = req.headers['referer'] || 'Direct';

  writeLog(`INCOMING REQUEST: ${req.method} ${req.url}`);
  writeLog(`  Client IP: ${clientIP}`);
  writeLog(`  User-Agent: ${userAgent}`);
  writeLog(`  Referer: ${referer}`);
  writeLog(`  Headers: ${JSON.stringify(req.headers, null, 2)}`);

  next();
});

// إعداد البروكسي لتوجيه جميع الطلبات إلى المنفذ 8080 المحلي
const proxyOptions = {
  target: `http://127.0.0.1:${TARGET_PORT}`,
  changeOrigin: true,
  ws: true, // دعم WebSocket
  logLevel: 'debug',
  onError: (err, req, res) => {
    const errorMsg = `PROXY ERROR: ${err.message} for ${req.method} ${req.url}`;
    writeLog(errorMsg);
    writeLog(`  Error Details: ${err.stack}`);

    if (!res.headersSent) {
      res.status(500).json({
        error: 'Proxy Error',
        message: 'Unable to connect to internal server',
        details: err.message
      });
    }
  },
  onProxyReq: (proxyReq, req, res) => {
    writeLog(`PROXY REQUEST: ${req.method} ${req.url} -> http://127.0.0.1:${TARGET_PORT}${req.url}`);
    writeLog(`  Proxy Headers: ${JSON.stringify(proxyReq.getHeaders(), null, 2)}`);
  },
  onProxyRes: (proxyRes, req, res) => {
    writeLog(`PROXY RESPONSE: ${proxyRes.statusCode} for ${req.method} ${req.url}`);
    writeLog(`  Response Headers: ${JSON.stringify(proxyRes.headers, null, 2)}`);
    writeLog(`  Content-Type: ${proxyRes.headers['content-type'] || 'Unknown'}`);
    writeLog(`  Content-Length: ${proxyRes.headers['content-length'] || 'Unknown'}`);
  }
};

// استخدام البروكسي لجميع الطلبات
app.use('/', createProxyMiddleware(proxyOptions));

// بدء الخادم
app.listen(PORT, '0.0.0.0', () => {
  writeLog(`PROXY SERVER STARTED on port ${PORT}`);
  writeLog(`Target: http://127.0.0.1:${TARGET_PORT}`);
  writeLog(`External access: http://185.11.8.26:${PORT}`);
  writeLog(`Logs directory: ${logsDir}`);

  console.log(`🔄 External Proxy Server running on port ${PORT}`);
  console.log(`📡 Forwarding all requests to http://127.0.0.1:${TARGET_PORT}`);
  console.log(`🌐 External access: http://185.11.8.26:${PORT}`);
  console.log(`🔒 Internal target: http://127.0.0.1:${TARGET_PORT}`);
  console.log(`🎯 Listening on all interfaces (0.0.0.0:${PORT})`);
  console.log(`📝 Logs saved to: ${logsDir}`);
});

// معالجة الأخطاء
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
