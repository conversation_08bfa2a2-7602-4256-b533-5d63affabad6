# 🛠️ دليل أدوات التشخيص - YemClient

## 📥 الأدوات المطلوبة للتحميل

### 1. أدوات أساسية (مطلوبة)

#### **Node.js و npm**
```bash
# تحميل من: https://nodejs.org/
# التحقق من التثبيت:
node --version
npm --version
```

#### **cURL** (لاختبار API)
```bash
# Windows 10+ (مدمج)
curl --version

# إذا لم يكن متوفر:
# تحميل من: https://curl.se/windows/
```

#### **Git** (لإدارة الكود)
```bash
# تحميل من: https://git-scm.com/
git --version
```

### 2. أدوات اختيارية (مفيدة)

#### **Postman** (لاختبار API بواجهة رسومية)
```
تحميل من: https://www.postman.com/downloads/
- سهل الاستخدام
- حفظ الطلبات
- اختبار متقدم
```

#### **VS Code** (محرر متقدم)
```
تحميل من: https://code.visualstudio.com/
الإضافات المفيدة:
- REST Client
- Thunder Client
- JSON Viewer
```

#### **jq** (لمعالجة JSON في سطر الأوامر)
```bash
# Windows (باستخدام Chocolatey)
choco install jq

# أو تحميل من: https://stedolan.github.io/jq/download/
```

### 3. أدوات مراقبة الشبكة (متقدمة)

#### **Wireshark** (مراقبة حركة الشبكة)
```
تحميل من: https://www.wireshark.org/download.html
- مراقبة جميع الحزم
- تحليل البروتوكولات
- تشخيص مشاكل الشبكة
```

#### **Fiddler** (مراقبة HTTP/HTTPS)
```
تحميل من: https://www.telerik.com/fiddler
- مراقبة طلبات HTTP
- تعديل الطلبات والاستجابات
- تشخيص مشاكل CORS
```

## 🚀 كيفية استخدام أدوات التشخيص

### 1. سكريبت التشخيص الشامل

#### **Windows (PowerShell)**
```powershell
# تشغيل السكريبت
powershell -ExecutionPolicy Bypass -File diagnostic-script.ps1

# مع معاملات مخصصة
powershell -ExecutionPolicy Bypass -File diagnostic-script.ps1 -ServerUrl "http://***********:8080" -TestUser "admin" -TestPassword "admin123"
```

#### **Linux/Mac (Bash)**
```bash
# إعطاء صلاحية التشغيل
chmod +x diagnostic-script.sh

# تشغيل السكريبت
./diagnostic-script.sh

# مع معاملات مخصصة
./diagnostic-script.sh "http://***********:8080" "admin" "admin123" "1001" "123456"
```

### 2. اختبار تسجيل الدخول المباشر

#### **فتح ملف الاختبار**
```
1. افتح test-login.html في المتصفح
2. اختبر جميع أنواع تسجيل الدخول
3. راقب النتائج والأخطاء
4. استخدم أزرار التشخيص المختلفة
```

### 3. استخدام أدوات المطور

#### **فتح أدوات المطور**
```
الطرق:
- F12
- Ctrl+Shift+I
- النقر بالزر الأيمن > Inspect
```

#### **التبويبات المهمة:**
```
Console: رسائل JavaScript والأخطاء
Network: طلبات HTTP والاستجابات
Application: localStorage والكوكيز
Sources: تتبع الكود وإضافة breakpoints
```

## 📊 فهم ردود الأخطاء

### 1. أخطاء HTTP الشائعة

#### **400 Bad Request**
```json
{
  "success": false,
  "message": "اسم المستخدم وكلمة المرور مطلوبان"
}
```
**السبب:** بيانات ناقصة أو غير صحيحة
**الحل:** تحقق من البيانات المرسلة

#### **401 Unauthorized**
```json
{
  "success": false,
  "message": "بيانات الدخول غير صحيحة"
}
```
**السبب:** اسم مستخدم أو كلمة مرور خاطئة
**الحل:** تحقق من البيانات في قاعدة البيانات

#### **403 Forbidden**
```json
{
  "success": false,
  "message": "الجهاز غير مصرح"
}
```
**السبب:** الجهاز غير مسجل أو محظور
**الحل:** تسجيل الجهاز أو إزالة القيود

#### **429 Too Many Requests**
```json
{
  "error": "Too many requests from this IP, please try again later.",
  "retryAfter": "15 minutes"
}
```
**السبب:** تجاوز حد الطلبات المسموح
**الحل:** انتظار أو تغيير IP

#### **500 Internal Server Error**
```json
{
  "success": false,
  "message": "خطأ في الخادم"
}
```
**السبب:** خطأ في الخادم أو قاعدة البيانات
**الحل:** فحص سجلات الخادم

### 2. أخطاء الشبكة

#### **CORS Error**
```
Access to fetch at 'http://***********:8080/api/auth/login' from origin 'http://localhost:3000' has been blocked by CORS policy
```
**السبب:** إعدادات CORS غير صحيحة
**الحل:** تحديث إعدادات CORS في الخادم

#### **Network Error**
```
TypeError: Failed to fetch
```
**السبب:** الخادم غير متاح أو مشكلة في الشبكة
**الحل:** فحص حالة الخادم والاتصال

## 🔍 خطوات التشخيص المنهجي

### المرحلة 1: فحص أساسي (5 دقائق)

```bash
# 1. فحص الاتصال
ping ***********

# 2. فحص المنفذ
telnet *********** 8080

# 3. فحص صحة الخادم
curl http://***********:8080/health

# 4. تشغيل السكريبت السريع
./diagnostic-script.sh
```

### المرحلة 2: فحص تفصيلي (15 دقيقة)

```bash
# 1. فحص معلومات النظام
curl http://***********:8080/api/debug/system-info

# 2. اختبار تسجيل دخول المستخدم
curl -X POST http://***********:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"loginName":"admin","password":"admin123","deviceId":"test","userType":"user"}'

# 3. اختبار تسجيل دخول العميل
curl -X POST http://***********:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"loginName":"1001","password":"123456","deviceId":"test","userType":"client"}'

# 4. فحص محاولات الدخول
curl http://***********:8080/api/debug/login-attempts?limit=10
```

### المرحلة 3: فحص متقدم (30 دقيقة)

```bash
# 1. فحص سجلات الخادم
pm2 logs yemclient-server --lines 50

# 2. فحص قاعدة البيانات
npm run db:studio

# 3. اختبار CORS
curl -I -X OPTIONS \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  http://***********:8080/api/auth/login

# 4. مراقبة الأداء
pm2 monit
```

## 📋 قوائم التحقق السريع

### للمطورين ✅

- [ ] الخادم يعمل على المنفذ 8080
- [ ] قاعدة البيانات متصلة ومتاحة
- [ ] إعدادات CORS صحيحة
- [ ] JWT_SECRET و SESSION_SECRET محددان
- [ ] سجلات الخادم لا تظهر أخطاء
- [ ] PM2 يعرض حالة "online"

### للمستخدمين ✅

- [ ] المتصفح محدث (Chrome/Firefox/Edge)
- [ ] JavaScript مفعل
- [ ] الكوكيز مفعلة
- [ ] لا يوجد برامج حجب إعلانات تتداخل
- [ ] اتصال الإنترنت مستقر
- [ ] لا يوجد VPN يتداخل

### لمدراء النظام ✅

- [ ] المنفذ 8080 مفتوح في الجدار الناري
- [ ] الخادم يستمع على 0.0.0.0:8080
- [ ] DNS يحل *********** بشكل صحيح
- [ ] SSL/TLS مكون بشكل صحيح (إن وجد)
- [ ] موارد النظام كافية (RAM/CPU)
- [ ] مساحة القرص الصلب كافية

## 🆘 طلب المساعدة

### معلومات مطلوبة عند طلب الدعم:

1. **نتائج السكريبت التشخيصي**
2. **لقطات شاشة للأخطاء**
3. **سجلات من Debug Panel**
4. **معلومات النظام:**
   - نوع المتصفح والإصدار
   - نظام التشغيل
   - إصدار Node.js
5. **خطوات إعادة إنتاج المشكلة**

### طرق الحصول على المعلومات:

```bash
# تصدير سجلات التشخيص
./diagnostic-script.sh > diagnostic-report.txt

# تصدير سجلات الخادم
pm2 logs yemclient-server --lines 100 > server-logs.txt

# معلومات النظام
node --version > system-info.txt
npm --version >> system-info.txt
pm2 status >> system-info.txt
```

## 🔗 روابط مفيدة

- **الخادم المحلي:** http://localhost:8080
- **الخادم الخارجي:** http://***********:8080
- **فحص الصحة:** http://***********:8080/health
- **معلومات النظام:** http://***********:8080/api/debug/system-info
- **اختبار تسجيل الدخول:** افتح test-login.html
- **Debug Panel:** متاح في التطبيق (زر Debug)
