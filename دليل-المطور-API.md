# 📚 دليل المطور - نظام إدارة العملاء اليمني
## Yemen Client Management System - Developer Guide

---

## 🎯 **نظرة عامة**

هذا الدليل يوضح كيفية استخدام مكتبات الربط للوصول إلى نظام إدارة العملاء اليمني من التطبيقات الخارجية.

### **الملفات المتوفرة:**
- 📄 `YemenClientAPI-Agent.js` - مكتبة الوكلاء
- 📄 `YemenClientAPI-Client.js` - مكتبة العملاء
- 📄 `API-Documentation-EN.md` - وثائق API الإنجليزية
- 📄 `API-System-Design.md` - تصميم النظام العربي

---

## 🤝 **مكتبة الوكلاء (Agent SDK)**

### **التثبيت:**
```bash
# في Node.js
npm install node-fetch

# نسخ الملف
cp YemenClientAPI-Agent.js ./your-project/
```

### **الاستخدام الأساسي:**
```javascript
const YemenClientAgentAPI = require('./YemenClientAPI-Agent.js')

// إنشاء مثيل جديد
const agent = new YemenClientAgentAPI(
  'http://***********:8080',  // عنوان الخادم
  'agent001',                 // اسم تسجيل الدخول
  'agent123'                  // كلمة المرور
)

async function main() {
  try {
    // 1. تسجيل الدخول
    const authResult = await agent.authenticate()
    console.log('Agent:', authResult.agent_name)

    // 2. التحقق من عميل
    const clientResult = await agent.verifyClient('1000', 'ABC12345')
    if (clientResult.error) {
      console.log('Error:', clientResult.error)
    } else {
      console.log('Client:', clientResult.client_name)
    }

    // 3. الحصول على الإحصائيات
    const stats = await agent.getStats()
    console.log('Success Rate:', stats.success_rate)

    // 4. تسجيل الخروج
    await agent.logout()

  } catch (error) {
    console.error('Error:', error.message)
  }
}

main()
```

### **الوظائف المتاحة:**

#### **authenticate()**
```javascript
const result = await agent.authenticate()
// Returns: { agent_id, agent_name, agency_type, token, expires_at }
```

#### **verifyClient(clientCode, token)**
```javascript
const result = await agent.verifyClient('1000', 'ABC12345')
// Returns: { client_code, client_name, app_name, status, ... }
```

#### **getStats()**
```javascript
const stats = await agent.getStats()
// Returns: { total_operations, success_rate, unique_clients, ... }
```

#### **getOperations(page, limit)**
```javascript
const ops = await agent.getOperations(1, 10)
// Returns: { operations: [...], pagination: {...} }
```

---

## 👤 **مكتبة العملاء (Client SDK)**

### **التثبيت:**
```bash
# في Node.js
npm install node-fetch

# نسخ الملف
cp YemenClientAPI-Client.js ./your-project/
```

### **الاستخدام الأساسي:**
```javascript
const YemenClientAPI = require('./YemenClientAPI-Client.js')

// إنشاء مثيل جديد
const client = new YemenClientAPI(
  'http://***********:8080',  // عنوان الخادم
  '1000',                     // رمز العميل
  'ABC12345'                  // توكن العميل
)

async function main() {
  try {
    // التحقق من العميل عبر وكيل
    const result = await client.verifyThroughAgent(
      'agent001',  // اسم الوكيل
      'agent123'   // كلمة مرور الوكيل
    )

    if (result.success) {
      console.log('Client verified:', result.client.client_name)
      console.log('Through agent:', result.agent.name)
      console.log('Status:', result.client.status === 1 ? 'Active' : 'Inactive')
    } else {
      console.log('Verification failed:', result.error)
    }

  } catch (error) {
    console.error('Error:', error.message)
  }
}

main()
```

### **الوظائف المتاحة:**

#### **verifyThroughAgent(agentLogin, agentPassword)**
```javascript
const result = await client.verifyThroughAgent('agent001', 'agent123')
// Returns: { success, client: {...}, agent: {...} }
```

#### **checkSystemHealth()**
```javascript
const health = await client.checkSystemHealth()
// Returns: { success, health: {...}, message }
```

#### **validateCredentials()**
```javascript
const isValid = client.validateCredentials()
// Returns: boolean
```

---

## 🌐 **استخدام في المتصفح**

### **HTML:**
```html
<!DOCTYPE html>
<html>
<head>
    <title>Yemen Client API Test</title>
</head>
<body>
    <script src="YemenClientAPI-Agent.js"></script>
    <script>
        async function testAgent() {
            const agent = new YemenClientAgentAPI(
                'http://***********:8080',
                'agent001',
                'agent123'
            )

            try {
                await agent.authenticate()
                const result = await agent.verifyClient('1000', 'ABC12345')
                console.log('Result:', result)
                await agent.logout()
            } catch (error) {
                console.error('Error:', error)
            }
        }

        testAgent()
    </script>
</body>
</html>
```

---

## 🔧 **أمثلة متقدمة**

### **مثال 1: نظام تحقق متكامل**
```javascript
class ClientVerificationSystem {
  constructor(serverUrl, agentCredentials) {
    this.agent = new YemenClientAgentAPI(
      serverUrl,
      agentCredentials.login,
      agentCredentials.password
    )
    this.isAuthenticated = false
  }

  async initialize() {
    try {
      await this.agent.authenticate()
      this.isAuthenticated = true
      console.log('✅ System initialized')
    } catch (error) {
      console.error('❌ Initialization failed:', error.message)
      throw error
    }
  }

  async verifyMultipleClients(clients) {
    if (!this.isAuthenticated) {
      await this.initialize()
    }

    const results = []
    for (const client of clients) {
      try {
        const result = await this.agent.verifyClient(
          client.code,
          client.token
        )
        results.push({
          code: client.code,
          success: !result.error,
          data: result
        })
      } catch (error) {
        results.push({
          code: client.code,
          success: false,
          error: error.message
        })
      }
    }

    return results
  }

  async getDetailedStats() {
    const [stats, operations] = await Promise.all([
      this.agent.getStats(),
      this.agent.getOperations(1, 5)
    ])

    return {
      statistics: stats,
      recentOperations: operations.operations,
      agentInfo: this.agent.getAgentInfo()
    }
  }

  async shutdown() {
    if (this.isAuthenticated) {
      await this.agent.logout()
      this.isAuthenticated = false
      console.log('✅ System shutdown')
    }
  }
}

// الاستخدام
const system = new ClientVerificationSystem(
  'http://***********:8080',
  { login: 'agent001', password: 'agent123' }
)

await system.initialize()

const clients = [
  { code: '1000', token: 'ABC12345' },
  { code: '1001', token: 'XYZ67890' }
]

const results = await system.verifyMultipleClients(clients)
console.log('Verification results:', results)

const stats = await system.getDetailedStats()
console.log('Detailed stats:', stats)

await system.shutdown()
```

### **مثال 2: نظام مراقبة العملاء**
```javascript
class ClientMonitor {
  constructor(serverUrl, clientCredentials, agentCredentials) {
    this.client = new YemenClientAPI(
      serverUrl,
      clientCredentials.code,
      clientCredentials.token
    )
    this.agentCredentials = agentCredentials
    this.monitoringInterval = null
  }

  async startMonitoring(intervalMinutes = 5) {
    console.log('🔍 Starting client monitoring...')
    
    // فحص أولي
    await this.checkStatus()

    // فحص دوري
    this.monitoringInterval = setInterval(async () => {
      await this.checkStatus()
    }, intervalMinutes * 60 * 1000)
  }

  async checkStatus() {
    try {
      const timestamp = new Date().toISOString()
      
      // فحص حالة النظام
      const health = await this.client.checkSystemHealth()
      
      if (!health.success) {
        console.log(`❌ [${timestamp}] System health check failed`)
        return
      }

      // التحقق من العميل
      const verification = await this.client.verifyThroughAgent(
        this.agentCredentials.login,
        this.agentCredentials.password
      )

      if (verification.success) {
        console.log(`✅ [${timestamp}] Client status: Active`)
        console.log(`   Client: ${verification.client.client_name}`)
        console.log(`   Agent: ${verification.agent.name}`)
      } else {
        console.log(`❌ [${timestamp}] Client verification failed: ${verification.error}`)
      }

    } catch (error) {
      console.error(`💥 [${new Date().toISOString()}] Monitoring error:`, error.message)
    }
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
      console.log('⏹️ Monitoring stopped')
    }
  }
}

// الاستخدام
const monitor = new ClientMonitor(
  'http://***********:8080',
  { code: '1000', token: 'ABC12345' },
  { login: 'agent001', password: 'agent123' }
)

// بدء المراقبة كل 5 دقائق
await monitor.startMonitoring(5)

// إيقاف المراقبة بعد ساعة
setTimeout(() => {
  monitor.stopMonitoring()
}, 60 * 60 * 1000)
```

---

## 🛡️ **أفضل الممارسات الأمنية**

### **1. حماية بيانات الاعتماد:**
```javascript
// ❌ خطأ - تخزين كلمات المرور في الكود
const agent = new YemenClientAgentAPI(
  'http://***********:8080',
  'agent001',
  'hardcoded_password'  // خطر أمني!
)

// ✅ صحيح - استخدام متغيرات البيئة
const agent = new YemenClientAgentAPI(
  process.env.API_SERVER_URL,
  process.env.AGENT_LOGIN,
  process.env.AGENT_PASSWORD
)
```

### **2. معالجة الأخطاء:**
```javascript
async function safeVerifyClient(agent, clientCode, token) {
  try {
    const result = await agent.verifyClient(clientCode, token)
    
    if (result.error) {
      // تسجيل الخطأ بدون كشف معلومات حساسة
      console.log('Verification failed:', result.error)
      return { success: false, message: 'Verification failed' }
    }
    
    return { success: true, data: result }
    
  } catch (error) {
    // عدم كشف تفاصيل الخطأ للمستخدم النهائي
    console.error('Internal error:', error.message)
    return { success: false, message: 'Service temporarily unavailable' }
  }
}
```

### **3. إدارة الجلسات:**
```javascript
class SecureAgentSession {
  constructor(serverUrl, credentials) {
    this.agent = new YemenClientAgentAPI(serverUrl, credentials.login, credentials.password)
    this.sessionTimeout = 30 * 60 * 1000 // 30 دقيقة
    this.lastActivity = null
    this.timeoutHandler = null
  }

  async authenticate() {
    await this.agent.authenticate()
    this.updateActivity()
  }

  async verifyClient(code, token) {
    this.checkSession()
    const result = await this.agent.verifyClient(code, token)
    this.updateActivity()
    return result
  }

  updateActivity() {
    this.lastActivity = Date.now()
    
    // إعادة تعيين مؤقت انتهاء الجلسة
    if (this.timeoutHandler) {
      clearTimeout(this.timeoutHandler)
    }
    
    this.timeoutHandler = setTimeout(async () => {
      console.log('Session timeout - logging out')
      await this.agent.logout()
    }, this.sessionTimeout)
  }

  checkSession() {
    if (!this.agent.isAuthenticated()) {
      throw new Error('Session expired - please re-authenticate')
    }
  }
}
```

---

## 📊 **مراقبة الأداء**

### **قياس أوقات الاستجابة:**
```javascript
class PerformanceMonitor {
  static async measureOperation(operation, ...args) {
    const start = Date.now()
    try {
      const result = await operation(...args)
      const duration = Date.now() - start
      console.log(`⏱️ Operation completed in ${duration}ms`)
      return { success: true, result, duration }
    } catch (error) {
      const duration = Date.now() - start
      console.log(`❌ Operation failed after ${duration}ms:`, error.message)
      return { success: false, error: error.message, duration }
    }
  }
}

// الاستخدام
const agent = new YemenClientAgentAPI(/* ... */)

const authResult = await PerformanceMonitor.measureOperation(
  agent.authenticate.bind(agent)
)

const verifyResult = await PerformanceMonitor.measureOperation(
  agent.verifyClient.bind(agent),
  '1000',
  'ABC12345'
)
```

---

## 🔗 **روابط مفيدة**

- 📖 **وثائق API الكاملة:** `API-Documentation-EN.md`
- 🏗️ **تصميم النظام:** `API-System-Design.md`
- 🧪 **ملفات الاختبار:** `test-sdk.js`
- 🌐 **عنوان الخادم:** `http://***********:8080`
- 📞 **الدعم الفني:** <EMAIL>

---

## ❓ **الأسئلة الشائعة**

### **س: كيف أحصل على بيانات اعتماد الوكيل؟**
ج: تواصل مع إدارة النظام للحصول على اسم المستخدم وكلمة المرور.

### **س: ما هو معدل الطلبات المسموح؟**
ج: 100 طلب/دقيقة، 1000 طلب/ساعة لكل وكيل.

### **س: كيف أتعامل مع انتهاء صلاحية التوكن؟**
ج: المكتبة تتعامل مع هذا تلقائياً، أو يمكنك استخدام `authenticate()` مرة أخرى.

### **س: هل يمكن استخدام المكتبة في React/Vue؟**
ج: نعم، المكتبة تعمل في المتصفح والخادم.

---

**🎉 مبروك! أنت الآن جاهز لاستخدام نظام إدارة العملاء اليمني في تطبيقاتك.**
