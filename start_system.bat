@echo off
chcp 65001 >nul
echo ========================================
echo      تشغيل نظام إدارة العملاء والوكلاء
echo ========================================

echo.
echo اختر طريقة التشغيل:
echo 1. Docker (يتطلب Docker Desktop)
echo 2. تشغيل محلي (يتطلب PostgreSQL)
echo 3. إعداد قاعدة البيانات فقط
echo 4. خروج
echo.

set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto docker_setup
if "%choice%"=="2" goto local_setup
if "%choice%"=="3" goto database_only
if "%choice%"=="4" goto exit
goto invalid_choice

:docker_setup
echo.
echo === تشغيل Docker ===
echo إيقاف الحاويات السابقة...
docker-compose down

echo تشغيل النظام...
docker-compose up -d

echo انتظار تشغيل الخدمات...
timeout /t 30 /nobreak >nul

echo.
echo === معلومات الوصول ===
echo لوحة التحكم: http://localhost:5173
echo pgAdmin: http://localhost:5050
echo API: http://localhost:3000
echo.
echo بيانات دخول pgAdmin:
echo Email: <EMAIL>
echo Password: admin123456
echo.
echo بيانات دخول النظام:
echo Username: admin
echo Password: admin123456
echo.
goto end

:local_setup
echo.
echo === التشغيل المحلي ===
echo تحقق من وجود PostgreSQL...

psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: PostgreSQL غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PostgreSQL أولاً
    pause
    goto end
)

echo.
set /p db_password="أدخل كلمة مرور PostgreSQL: "

echo تحديث ملف التكوين...
cd server
echo DATABASE_URL="postgresql://postgres:%db_password%@localhost:5432/yemclient_db?schema=public" > .env.temp
echo JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" >> .env.temp
echo PORT=3000 >> .env.temp
echo NODE_ENV=development >> .env.temp
echo EXTERNAL_IP=*********** >> .env.temp
echo EXTERNAL_PORT=3000 >> .env.temp
echo ADMIN_USERNAME=admin >> .env.temp
echo ADMIN_PASSWORD=admin123456 >> .env.temp
echo ADMIN_LOGIN_NAME=admin >> .env.temp
move .env.temp .env

echo إنشاء قاعدة البيانات...
createdb -U postgres yemclient_db
if %errorlevel% neq 0 (
    echo تحذير: قد تكون قاعدة البيانات موجودة مسبقاً
)

echo تثبيت المتطلبات...
call npm install

echo إنشاء الجداول...
call npx prisma migrate dev --name init

echo إدخال البيانات التجريبية...
call npm run db:seed

echo تثبيت متطلبات العميل...
cd ..\client
call npm install

echo.
echo === تشغيل النظام ===
echo تشغيل الخادم...
cd ..\server
start "YemClient Server" cmd /k "npm start"

echo انتظار تشغيل الخادم...
timeout /t 5 /nobreak >nul

echo تشغيل العميل...
cd ..\client
start "YemClient Frontend" cmd /k "npm run dev"

echo.
echo === معلومات الوصول ===
echo لوحة التحكم: http://localhost:5173
echo API: http://localhost:3000
echo.
echo بيانات دخول النظام:
echo Username: admin
echo Password: admin123456
echo.
echo استخدم pgAdmin المثبت محلياً للوصول لقاعدة البيانات
echo Host: localhost, Port: 5432, Database: yemclient_db
echo.
goto end

:database_only
echo.
echo === إعداد قاعدة البيانات فقط ===
set /p db_password="أدخل كلمة مرور PostgreSQL: "

echo إنشاء قاعدة البيانات...
createdb -U postgres yemclient_db

echo تحديث ملف التكوين...
cd server
echo DATABASE_URL="postgresql://postgres:%db_password%@localhost:5432/yemclient_db?schema=public" > .env.temp
echo JWT_SECRET="your-super-secret-jwt-key-change-this-in-production" >> .env.temp
echo PORT=3000 >> .env.temp
echo NODE_ENV=development >> .env.temp
echo EXTERNAL_IP=*********** >> .env.temp
echo EXTERNAL_PORT=3000 >> .env.temp
echo ADMIN_USERNAME=admin >> .env.temp
echo ADMIN_PASSWORD=admin123456 >> .env.temp
echo ADMIN_LOGIN_NAME=admin >> .env.temp
move .env.temp .env

call npm install
call npx prisma migrate dev --name init
call npm run db:seed

echo.
echo تم إعداد قاعدة البيانات بنجاح!
echo يمكنك الآن رؤيتها في pgAdmin
echo.
goto end

:invalid_choice
echo اختيار غير صحيح!
pause
goto start

:exit
echo خروج...
goto end

:end
echo.
pause
