/**
 * Debug Logger for YemClient
 * Comprehensive logging and monitoring utility
 */

class DebugLogger {
  constructor() {
    this.logs = [];
    this.maxLogs = 1000;
    this.isEnabled = true;
    this.sessionId = this.generateSessionId();
    
    // Initialize with session info
    this.log('SYSTEM', 'Debug Logger initialized', {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });
  }

  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  log(category, message, data = {}) {
    if (!this.isEnabled) return;

    const logEntry = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      category: category.toUpperCase(),
      message,
      data,
      sessionId: this.sessionId,
      url: window.location.href,
      stack: new Error().stack
    };

    this.logs.push(logEntry);
    
    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with colors
    const colors = {
      AUTH: '#2196F3',
      ERROR: '#F44336',
      SUCCESS: '#4CAF50',
      WARNING: '#FF9800',
      INFO: '#9C27B0',
      NETWORK: '#00BCD4',
      SYSTEM: '#607D8B'
    };

    const color = colors[category] || '#000000';
    
    console.group(`%c[${category}] ${message}`, `color: ${color}; font-weight: bold;`);
    console.log('📅 Time:', logEntry.timestamp);
    console.log('🔗 URL:', logEntry.url);
    if (Object.keys(data).length > 0) {
      console.log('📊 Data:', data);
    }
    console.groupEnd();

    // Store in localStorage for persistence
    this.saveToStorage();
  }

  saveToStorage() {
    try {
      const recentLogs = this.logs.slice(-100); // Keep last 100 logs
      localStorage.setItem('yemclient_debug_logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.warn('Failed to save logs to localStorage:', error);
    }
  }

  loadFromStorage() {
    try {
      const stored = localStorage.getItem('yemclient_debug_logs');
      if (stored) {
        const storedLogs = JSON.parse(stored);
        this.logs = [...storedLogs, ...this.logs];
      }
    } catch (error) {
      console.warn('Failed to load logs from localStorage:', error);
    }
  }

  // Specific logging methods
  auth(message, data = {}) {
    this.log('AUTH', message, data);
  }

  error(message, error = null, data = {}) {
    const errorData = {
      ...data,
      ...(error && {
        errorMessage: error.message,
        errorStack: error.stack,
        errorName: error.name
      })
    };
    this.log('ERROR', message, errorData);
  }

  success(message, data = {}) {
    this.log('SUCCESS', message, data);
  }

  warning(message, data = {}) {
    this.log('WARNING', message, data);
  }

  info(message, data = {}) {
    this.log('INFO', message, data);
  }

  network(message, request = {}, response = {}) {
    this.log('NETWORK', message, {
      request: {
        url: request.url,
        method: request.method,
        headers: request.headers,
        body: request.body
      },
      response: {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data
      }
    });
  }

  // Get logs for debugging
  getLogs(category = null, limit = 50) {
    let filteredLogs = this.logs;
    
    if (category) {
      filteredLogs = this.logs.filter(log => log.category === category.toUpperCase());
    }
    
    return filteredLogs.slice(-limit);
  }

  // Export logs for support
  exportLogs() {
    const exportData = {
      sessionId: this.sessionId,
      exportTime: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      logs: this.logs
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `yemclient-debug-${this.sessionId}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  // Clear logs
  clear() {
    this.logs = [];
    localStorage.removeItem('yemclient_debug_logs');
    this.log('SYSTEM', 'Debug logs cleared');
  }

  // Enable/disable logging
  enable() {
    this.isEnabled = true;
    this.log('SYSTEM', 'Debug logging enabled');
  }

  disable() {
    this.isEnabled = false;
  }

  // Get system info
  getSystemInfo() {
    return {
      sessionId: this.sessionId,
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString(),
      localStorage: {
        token: !!localStorage.getItem('token'),
        user: !!localStorage.getItem('user'),
        clientData: !!localStorage.getItem('clientData'),
        deviceId: localStorage.getItem('deviceId')
      },
      totalLogs: this.logs.length,
      categories: [...new Set(this.logs.map(log => log.category))]
    };
  }
}

// Create global instance
const debugLogger = new DebugLogger();

// Load existing logs
debugLogger.loadFromStorage();

// Make it available globally for debugging
window.debugLogger = debugLogger;

export default debugLogger;
