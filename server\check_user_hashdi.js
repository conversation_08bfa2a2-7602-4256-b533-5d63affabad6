const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')
const prisma = new PrismaClient()

async function checkUserHashdi() {
  try {
    console.log('🔍 فحص بيانات المستخدم محمد الحاشدي')
    console.log('=====================================')
    
    // البحث عن المستخدم محمد الحاشدي
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        password: true,
        isActive: true,
        permissions: true,
        createdAt: true
      }
    })

    if (!user) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }
    
    console.log(`👤 المستخدم: ${user.username}`)
    console.log(`🆔 معرف المستخدم: ${user.id}`)
    console.log(`🔑 اسم تسجيل الدخول: ${user.loginName}`)
    console.log(`📱 الأجهزة: ${user.deviceId || 'غير محدد'}`)
    console.log(`✅ نشط: ${user.isActive ? 'نعم' : 'لا'}`)
    console.log(`📅 تاريخ الإنشاء: ${user.createdAt}`)
    
    // عرض الأجهزة المسجلة
    if (user.deviceId) {
      const devices = user.deviceId.split(',').map(id => id.trim())
      console.log(`📱 عدد الأجهزة: ${devices.length}`)
      console.log('📱 قائمة الأجهزة:')
      devices.forEach((device, index) => {
        console.log(`   ${index + 1}. ${device}`)
      })
    }
    
    // اختبار كلمات المرور المحتملة
    const possiblePasswords = [
      'Hash8080',
      'hash8080',
      'admin123456',
      'yemen123',
      '12345678'
    ]
    
    console.log('')
    console.log('🔐 اختبار كلمات المرور:')
    
    for (const password of possiblePasswords) {
      const isValid = await bcrypt.compare(password, user.password)
      console.log(`   ${password}: ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`)
      
      if (isValid) {
        console.log(`🎉 كلمة المرور الصحيحة هي: ${password}`)
        break
      }
    }
    
  } catch (error) {
    console.error('❌ خطأ في فحص المستخدم:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUserHashdi()
