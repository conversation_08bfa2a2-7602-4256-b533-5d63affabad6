<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وكيل الغراسي - اختبار النظام</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        input, button {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 0;
            transition: all 0.3s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            border: 2px solid #dee2e6;
            min-height: 50px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        
        .step {
            background: #fff3cd;
            border: 2px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .step-number {
            background: #007bff;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: bold;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #007bff;
            font-weight: bold;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏢 وكيل الغراسي - اختبار النظام</h1>
        
        <!-- شرح الخطوات -->
        <div class="section">
            <h3>📋 خطوات العملية:</h3>
            <div class="step">
                <span class="step-number">1</span>
                <strong>تسجيل دخول الوكيل:</strong> سيتم تسجيل دخولك كوكيل الغراسي للحصول على توكن الأمان
            </div>
            <div class="step">
                <span class="step-number">2</span>
                <strong>إرسال بيانات العميل:</strong> سيتم إرسال رمز العميل والتوكن للتحقق من صحتها
            </div>
            <div class="step">
                <span class="step-number">3</span>
                <strong>استلام النتيجة:</strong> سيرد النظام بحالة العميل ومعلوماته
            </div>
        </div>

        <!-- بيانات الوكيل -->
        <div class="section">
            <h3>🔐 بيانات الوكيل (الغراسي)</h3>
            <div class="form-group">
                <label>اسم تسجيل الدخول:</label>
                <input type="text" id="agentLogin" value="agent001" readonly>
                <small>هذا هو اسم المستخدم الخاص بوكيل الغراسي</small>
            </div>
            <div class="form-group">
                <label>كلمة المرور:</label>
                <input type="password" id="agentPassword" value="agent123" readonly>
                <small>كلمة المرور الخاصة بالوكيل</small>
            </div>
        </div>

        <!-- بيانات العميل -->
        <div class="section">
            <h3>👤 بيانات العميل للتحقق</h3>
            <div class="form-group">
                <label>رمز العميل:</label>
                <input type="text" id="clientCode" value="1000" placeholder="أدخل رمز العميل">
            </div>
            <div class="form-group">
                <label>توكن العميل:</label>
                <input type="text" id="clientToken" value="ABC12345" placeholder="أدخل توكن العميل">
            </div>
        </div>

        <!-- أزرار العمليات -->
        <div class="section">
            <h3>⚡ تنفيذ العمليات</h3>
            <button onclick="authenticateAgent()" id="authBtn">
                🔐 الخطوة 1: تسجيل دخول الوكيل
            </button>
            <button onclick="verifyClient()" id="verifyBtn" disabled>
                👤 الخطوة 2: التحقق من العميل
            </button>
            <button onclick="runFullProcess()" id="fullBtn">
                🚀 تنفيذ العملية الكاملة
            </button>
        </div>

        <!-- منطقة التحميل -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            جاري المعالجة...
        </div>

        <!-- نتائج العمليات -->
        <div class="section">
            <h3>📊 نتائج العمليات</h3>
            <div id="authResult" class="result">
                في انتظار تسجيل دخول الوكيل...
            </div>
            <div id="verifyResult" class="result">
                في انتظار التحقق من العميل...
            </div>
        </div>

        <!-- معلومات تقنية -->
        <div class="section">
            <h3>🔧 المعلومات التقنية</h3>
            <div id="technicalInfo" class="result info">
                عنوان الخادم: http://localhost:8080/api/external/
                حالة الاتصال: في انتظار الاختبار...
            </div>
        </div>
    </div>

    <script>
        // متغيرات عامة
        let agentToken = null;
        const API_BASE_URL = 'http://localhost:8080/api/external';

        // عرض حالة التحميل
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // عرض النتيجة في منطقة محددة
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // تحديث المعلومات التقنية
        function updateTechnicalInfo(info) {
            const element = document.getElementById('technicalInfo');
            element.textContent = `عنوان الخادم: ${API_BASE_URL}\n${info}`;
        }

        // الخطوة 1: تسجيل دخول الوكيل
        async function authenticateAgent() {
            showLoading(true);
            showResult('authResult', 'جاري تسجيل دخول الوكيل...', 'info');
            
            try {
                const agentLogin = document.getElementById('agentLogin').value;
                const agentPassword = document.getElementById('agentPassword').value;

                console.log('🔐 بدء تسجيل دخول الوكيل...');
                console.log('اسم المستخدم:', agentLogin);

                const response = await fetch(`${API_BASE_URL}/agent/auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        login_name: agentLogin,
                        login_password: agentPassword
                    })
                });

                const data = await response.json();
                console.log('📥 استجابة تسجيل الدخول:', data);

                if (data.status === 'success') {
                    agentToken = data.data.token;
                    
                    const successMessage = `✅ تم تسجيل الدخول بنجاح!
الوكيل: ${data.data.agent_name}
نوع الوكالة: ${data.data.agency_type}
رقم الوكيل: ${data.data.agent_id}
التوكن: ${agentToken.substring(0, 20)}...
انتهاء الصلاحية: ${new Date(data.data.expires_at).toLocaleString('ar-SA')}`;

                    showResult('authResult', successMessage, 'success');
                    
                    // تفعيل زر التحقق من العميل
                    document.getElementById('verifyBtn').disabled = false;
                    
                    updateTechnicalInfo(`حالة الاتصال: متصل ✅\nتوكن الوكيل: نشط\nآخر عملية: تسجيل دخول ناجح`);
                    
                } else {
                    showResult('authResult', `❌ فشل تسجيل الدخول: ${data.message}`, 'error');
                    updateTechnicalInfo(`حالة الاتصال: فشل في المصادقة ❌\nالخطأ: ${data.error_code || 'غير محدد'}`);
                }

            } catch (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error);
                showResult('authResult', `❌ خطأ في الاتصال: ${error.message}`, 'error');
                updateTechnicalInfo(`حالة الاتصال: خطأ في الشبكة ❌\nالتفاصيل: ${error.message}`);
            }

            showLoading(false);
        }

        // الخطوة 2: التحقق من العميل
        async function verifyClient() {
            if (!agentToken) {
                alert('يجب تسجيل دخول الوكيل أولاً!');
                return;
            }

            showLoading(true);
            showResult('verifyResult', 'جاري التحقق من بيانات العميل...', 'info');

            try {
                const clientCode = document.getElementById('clientCode').value;
                const clientToken = document.getElementById('clientToken').value;

                console.log('👤 بدء التحقق من العميل...');
                console.log('رمز العميل:', clientCode);
                console.log('توكن العميل:', clientToken);

                const response = await fetch(`${API_BASE_URL}/client/verify`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${agentToken}`
                    },
                    body: JSON.stringify({
                        client_code: clientCode,
                        token: clientToken
                    })
                });

                const data = await response.json();
                console.log('📥 استجابة التحقق من العميل:', data);

                if (data.status === 'success') {
                    const clientStatus = data.data.status === 1 ? 'نشط ✅' : 'غير نشط ❌';
                    
                    const successMessage = `✅ تم العثور على العميل!
اسم العميل: ${data.data.client_name}
اسم التطبيق: ${data.data.app_name}
رمز العميل: ${data.data.client_code}
حالة العميل: ${clientStatus}
عنوان IP: ${data.data.ip_address}
تاريخ الإنشاء: ${data.data.created_date}
المستخدم المنشئ: ${data.data.created_by_user}

🔐 التوكن: مطابق ✅`;

                    showResult('verifyResult', successMessage, 'success');
                    updateTechnicalInfo(`حالة الاتصال: متصل ✅\nآخر عملية: تحقق من عميل ناجح\nرمز العميل: ${clientCode}\nحالة العميل: ${clientStatus}`);

                } else {
                    let errorMessage = `❌ فشل التحقق من العميل: ${data.message}\n`;
                    
                    if (data.error_code === 'CLIENT_NOT_FOUND') {
                        errorMessage += `\n🔍 العميل برمز ${clientCode} غير موجود في النظام`;
                    } else if (data.error_code === 'TOKEN_MISMATCH') {
                        errorMessage += `\n🔐 التوكن ${clientToken} غير مطابق للتوكن المحفوظ`;
                    }

                    showResult('verifyResult', errorMessage, 'error');
                    updateTechnicalInfo(`حالة الاتصال: متصل ✅\nآخر عملية: فشل التحقق من العميل\nكود الخطأ: ${data.error_code}`);
                }

            } catch (error) {
                console.error('❌ خطأ في التحقق من العميل:', error);
                showResult('verifyResult', `❌ خطأ في الاتصال: ${error.message}`, 'error');
                updateTechnicalInfo(`حالة الاتصال: خطأ في الشبكة ❌\nالتفاصيل: ${error.message}`);
            }

            showLoading(false);
        }

        // تنفيذ العملية الكاملة
        async function runFullProcess() {
            console.log('🚀 بدء العملية الكاملة...');
            
            // الخطوة 1: تسجيل دخول الوكيل
            await authenticateAgent();
            
            // انتظار قصير
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // الخطوة 2: التحقق من العميل (إذا نجح تسجيل الدخول)
            if (agentToken) {
                await verifyClient();
            }
        }

        // فحص حالة النظام عند تحميل الصفحة
        window.onload = async function() {
            try {
                console.log('🔍 فحص حالة النظام...');
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                if (data.status === 'success') {
                    updateTechnicalInfo(`حالة النظام: يعمل بشكل طبيعي ✅\nقاعدة البيانات: متصلة\nالجلسات النشطة: ${data.data.active_sessions}\nالإصدار: ${data.data.version}`);
                } else {
                    updateTechnicalInfo('حالة النظام: مشكلة في الخادم ❌');
                }
            } catch (error) {
                updateTechnicalInfo(`حالة النظام: غير متاح ❌\nالخطأ: ${error.message}`);
            }
        };
    </script>
</body>
</html>
