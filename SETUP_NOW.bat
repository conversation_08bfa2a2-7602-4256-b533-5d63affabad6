@echo off
chcp 65001 >nul
title إعداد نظام إدارة العملاء والوكلاء - الحل النهائي

color 0A
echo ========================================
echo    نظام إدارة العملاء والوكلاء
echo         الإعداد التلقائي
echo ========================================

echo.
echo 🚀 اختر طريقة الإعداد:
echo.
echo 1. Docker (الأسهل - مُوصى به)
echo 2. PostgreSQL المحلي (يدوي)
echo 3. عرض تعليمات pgAdmin
echo 4. خروج
echo.

set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto docker_setup
if "%choice%"=="2" goto postgres_setup  
if "%choice%"=="3" goto pgadmin_help
if "%choice%"=="4" goto exit
goto invalid_choice

:docker_setup
echo.
echo ========================================
echo           إعداد Docker
echo ========================================

echo.
echo التحقق من Docker...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker غير مثبت!
    echo.
    echo يرجى تحميل وتثبيت Docker Desktop من:
    echo https://www.docker.com/products/docker-desktop
    echo.
    pause
    goto start
)

echo ✅ Docker متاح

echo.
echo 🛑 إيقاف الحاويات السابقة...
docker-compose down >nul 2>&1

echo.
echo 🚀 تشغيل النظام...
docker-compose up -d

if %errorlevel% neq 0 (
    echo ❌ فشل في تشغيل Docker
    echo.
    echo تأكد من:
    echo - تشغيل Docker Desktop
    echo - عدم استخدام المنفذ 5432
    echo.
    pause
    goto start
)

echo ✅ تم تشغيل النظام بنجاح!

echo.
echo ⏳ انتظار تشغيل الخدمات (30 ثانية)...
timeout /t 30 /nobreak >nul

echo.
echo ========================================
echo         النظام جاهز! 🎉
echo ========================================

echo.
echo 🌐 الوصول للنظام:
echo ├─ لوحة التحكم: http://localhost:5173
echo ├─ pgAdmin: http://localhost:5050  
echo └─ API: http://localhost:3000

echo.
echo 🔑 بيانات دخول لوحة التحكم:
echo ├─ Username: admin
echo └─ Password: admin123456

echo.
echo 🔑 بيانات دخول pgAdmin:
echo ├─ Email: <EMAIL>
echo └─ Password: admin123456

echo.
echo 📊 معلومات قاعدة البيانات في pgAdmin:
echo ├─ Host: postgres
echo ├─ Port: 5432
echo ├─ Database: yemclient_db
echo ├─ Username: yemclient_user
echo └─ Password: yemclient_password

echo.
echo 🎯 الخطوات التالية:
echo 1. اذهب إلى: http://localhost:5050
echo 2. سجل دخول بالبيانات أعلاه
echo 3. أضف خادم جديد بمعلومات قاعدة البيانات
echo 4. ستجد قاعدة البيانات yemclient_db مع جميع الجداول!

goto end

:postgres_setup
echo.
echo ========================================
echo        PostgreSQL المحلي
echo ========================================

echo.
echo 📋 الخطوات المطلوبة:

echo.
echo 1️⃣ افتح pgAdmin 4
echo 2️⃣ اتصل بالخادم:
echo    ├─ Host: localhost
echo    ├─ Port: 5432  
echo    ├─ Username: postgres
echo    └─ Password: yemen123

echo.
echo 3️⃣ انقر بزر الماوس الأيمن على "Databases"
echo 4️⃣ اختر "Create" → "Database"
echo 5️⃣ اكتب: yemclient_db
echo 6️⃣ انقر "Save"

echo.
echo 7️⃣ انقر بزر الماوس الأيمن على قاعدة البيانات الجديدة
echo 8️⃣ اختر "Query Tool"
echo 9️⃣ انسخ محتوى ملف create_database.sql
echo 🔟 الصق المحتوى واضغط F5

echo.
echo 📁 فتح ملف SQL...
start notepad create_database.sql

echo.
echo ✅ تم فتح الملف في Notepad
echo انسخ المحتوى كاملاً والصقه في pgAdmin

goto end

:pgadmin_help
echo.
echo ========================================
echo        تعليمات pgAdmin
echo ========================================

echo.
echo 🔧 إضافة خادم في pgAdmin:

echo.
echo 1️⃣ افتح pgAdmin 4
echo 2️⃣ انقر بزر الماوس الأيمن على "Servers"
echo 3️⃣ اختر "Create" → "Server"

echo.
echo 📝 في تبويب "General":
echo └─ Name: YemClient Database

echo.
echo 📝 في تبويب "Connection":

if exist "docker-compose.yml" (
    echo.
    echo 🐳 للـ Docker:
    echo ├─ Host: postgres
    echo ├─ Port: 5432
    echo ├─ Database: yemclient_db  
    echo ├─ Username: yemclient_user
    echo └─ Password: yemclient_password
)

echo.
echo 🖥️ للـ PostgreSQL المحلي:
echo ├─ Host: localhost
echo ├─ Port: 5432
echo ├─ Database: yemclient_db
echo ├─ Username: postgres  
echo └─ Password: yemen123

echo.
echo 4️⃣ انقر "Save"
echo 5️⃣ ستجد قاعدة البيانات yemclient_db تحت الخادم

goto end

:invalid_choice
echo ❌ اختيار غير صحيح!
timeout /t 2 /nobreak >nul
goto start

:exit
echo 👋 خروج...
goto end

:start
goto docker_setup

:end
echo.
echo ========================================
echo.
pause
