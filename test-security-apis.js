/**
 * اختبار APIs الأمان المُصلحة
 */

async function testSecurityAPIs() {
  console.log('🔒 اختبار APIs الأمان المُصلحة...\n');

  try {
    // اختبار 1: API إحصائيات الأمان
    console.log('1️⃣ اختبار API إحصائيات الأمان:');
    const statsResponse = await fetch('http://localhost:8080/api/security/stats');
    console.log(`   📡 Status: ${statsResponse.status}`);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('   ✅ API إحصائيات الأمان يعمل!');
      console.log(`   📊 إجمالي المحاولات: ${statsData.totalAttempts}`);
      console.log(`   ✅ المحاولات الناجحة: ${statsData.successfulAttempts}`);
      console.log(`   ❌ المحاولات الفاشلة: ${statsData.failedAttempts}`);
      console.log(`   📅 المحاولات الحديثة: ${statsData.recentAttempts}`);
      console.log(`   📈 معدل النجاح: ${statsData.successRate}%`);
    } else {
      const errorText = await statsResponse.text();
      console.log('   ❌ API إحصائيات الأمان فشل!');
      console.log(`   📝 Error: ${errorText}`);
    }
    console.log('');

    // اختبار 2: API سجلات تسجيل الدخول
    console.log('2️⃣ اختبار API سجلات تسجيل الدخول:');
    const attemptsResponse = await fetch('http://localhost:8080/api/security/login-attempts?page=1&limit=20');
    console.log(`   📡 Status: ${attemptsResponse.status}`);
    
    if (attemptsResponse.ok) {
      const attemptsData = await attemptsResponse.json();
      console.log('   ✅ API سجلات تسجيل الدخول يعمل!');
      console.log(`   📊 إجمالي السجلات: ${attemptsData.total}`);
      console.log(`   📋 السجلات في هذه الصفحة: ${attemptsData.data?.length}`);
      console.log(`   📄 إجمالي الصفحات: ${attemptsData.totalPages}`);
      
      if (attemptsData.data && attemptsData.data.length > 0) {
        console.log(`   📋 أول سجل: ${attemptsData.data[0].username} - ${attemptsData.data[0].status}`);
      } else {
        console.log('   ℹ️ لا توجد سجلات تسجيل دخول');
      }
    } else {
      const errorText = await attemptsResponse.text();
      console.log('   ❌ API سجلات تسجيل الدخول فشل!');
      console.log(`   📝 Error: ${errorText}`);
    }
    console.log('');

    // اختبار 3: اختبار جميع APIs المطلوبة للنظام الأساسي
    console.log('3️⃣ اختبار جميع APIs المطلوبة:');
    
    const requiredAPIs = [
      { name: 'Dashboard Stats', url: '/api/dashboard/stats' },
      { name: 'Data Records', url: '/api/data-records?page=1&limit=5' },
      { name: 'Clients', url: '/api/clients?page=1&limit=5' },
      { name: 'Agents', url: '/api/agents?page=1&limit=5' },
      { name: 'Users', url: '/api/users?page=1&limit=5' },
      { name: 'Recent Activity', url: '/api/dashboard/recent-activity' },
      { name: 'Security Stats', url: '/api/security/stats' },
      { name: 'Login Attempts', url: '/api/security/login-attempts?page=1&limit=5' }
    ];

    let allAPIsWorking = true;
    
    for (const api of requiredAPIs) {
      const response = await fetch(`http://localhost:8080${api.url}`);
      console.log(`   📡 ${api.name}: ${response.status}`);
      
      if (!response.ok) {
        allAPIsWorking = false;
        console.log(`   ❌ ${api.name} فشل`);
      } else {
        console.log(`   ✅ ${api.name} نجح`);
      }
    }
    
    console.log('');
    
    if (allAPIsWorking) {
      console.log('🎉 جميع APIs المطلوبة تعمل بشكل مثالي!');
    } else {
      console.log('⚠️ بعض APIs لا تعمل');
    }

    console.log('\n' + '='.repeat(60));
    console.log('📋 ملخص اختبار APIs الأمان:');
    console.log('✅ API إحصائيات الأمان: مُصلح');
    console.log('✅ API سجلات تسجيل الدخول: مُضاف');
    console.log('✅ جميع APIs المطلوبة: متاحة');
    console.log('');
    console.log('🎯 النتيجة:');
    
    if (allAPIsWorking) {
      console.log('🎉 النظام الأساسي سيعمل الآن بدون أخطاء!');
      console.log('🏠 جميع صفحات النظام ستعرض البيانات بشكل صحيح');
      console.log('🔒 صفحة الأمان ستعمل مع إحصائيات وسجلات حقيقية');
    } else {
      console.log('⚠️ لا تزال هناك مشاكل في بعض APIs');
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار APIs الأمان:', error.message);
  }
}

testSecurityAPIs().catch(console.error);
