const { PrismaClient } = require('./server/node_modules/@prisma/client')

const prisma = new PrismaClient()

async function fixClientData() {
  try {
    console.log('🔧 Fixing client data for API testing...')
    
    // البحث عن العميل برقم 1000
    const client = await prisma.client.findFirst({
      where: { clientCode: 1000 }
    })

    if (client) {
      console.log('✅ Found client 1000:', client.clientName)
      
      // تحديث التوكن
      await prisma.client.update({
        where: { id: client.id },
        data: { token: 'ABC12345' }
      })
      
      console.log('✅ Updated client token to: ABC12345')
    } else {
      console.log('⚠️ Client 1000 not found, creating test client...')
      
      // إنشاء عميل اختبار
      const newClient = await prisma.client.create({
        data: {
          clientName: 'عميل اختبار API',
          appName: 'تطبيق اختبار',
          cardNumber: '12345678',
          clientCode: 1000,
          password: 'encrypted_password_123',
          token: 'ABC12345',
          ipAddress: '*************',
          status: 1,
          userId: 1 // ربط بالمستخدم الأول
        }
      })
      
      console.log('✅ Created test client:', newClient)
    }

    // إضافة المزيد من العملاء الاختباريين
    const testClients = [
      {
        clientCode: 1001,
        clientName: 'عميل يمن موبايل',
        appName: 'تطبيق يمن موبايل',
        token: 'XYZ67890'
      },
      {
        clientCode: 1002,
        clientName: 'عميل سبافون',
        appName: 'تطبيق سبافون',
        token: 'DEF54321'
      }
    ]

    for (const testClient of testClients) {
      const existing = await prisma.client.findFirst({
        where: { clientCode: testClient.clientCode }
      })

      if (!existing) {
        await prisma.client.create({
          data: {
            clientName: testClient.clientName,
            appName: testClient.appName,
            cardNumber: '87654321',
            clientCode: testClient.clientCode,
            password: 'encrypted_password_123',
            token: testClient.token,
            ipAddress: '*************',
            status: 1,
            userId: 1
          }
        })
        console.log(`✅ Created client ${testClient.clientCode}: ${testClient.clientName}`)
      } else {
        await prisma.client.update({
          where: { id: existing.id },
          data: { token: testClient.token }
        })
        console.log(`✅ Updated token for client ${testClient.clientCode}`)
      }
    }

    console.log('🎉 Client data fixed successfully!')

  } catch (error) {
    console.error('❌ Error fixing client data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixClientData()
