/**
 * فحص طارئ لمشكلة الأمان - Device Validation
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function emergencySecurityCheck() {
  console.log('🚨 فحص طارئ لمشكلة الأمان...\n');

  try {
    await prisma.$connect();
    console.log('✅ الاتصال بقاعدة البيانات نجح\n');

    // 1. فحص جدول المستخدمين
    console.log('1️⃣ فحص جدول المستخدمين:');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        device1: true,
        deviceId: true,
        isActive: true,
        createdAt: true
      },
      orderBy: { id: 'asc' }
    });
    
    console.log(`   📊 عدد المستخدمين: ${users.length}\n`);
    
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ID: ${user.id}`);
      console.log(`      👤 اسم المستخدم: ${user.username}`);
      console.log(`      🔑 اسم الدخول: ${user.loginName}`);
      console.log(`      🔒 كلمة المرور: ${user.password ? 'موجودة' : '❌ مفقودة'}`);
      console.log(`      📱 device1: ${user.device1 || '❌ فارغ'}`);
      console.log(`      📱 deviceId: ${user.deviceId || '❌ فارغ'}`);
      console.log(`      ✅ نشط: ${user.isActive ? 'نعم' : 'لا'}`);
      console.log(`      📅 تاريخ الإنشاء: ${user.createdAt}`);
      console.log('');
    });

    // 2. فحص محاولات الدخول الأخيرة
    console.log('2️⃣ فحص محاولات الدخول الأخيرة:');
    const recentAttempts = await prisma.loginAttempt.findMany({
      take: 10,
      orderBy: { timestamp: 'desc' },
      include: {
        user: {
          select: {
            username: true,
            loginName: true
          }
        }
      }
    });
    
    console.log(`   📊 آخر ${recentAttempts.length} محاولات دخول:\n`);
    
    recentAttempts.forEach((attempt, index) => {
      console.log(`   ${index + 1}. ${attempt.success ? '✅' : '❌'} ${attempt.timestamp}`);
      console.log(`      👤 المستخدم: ${attempt.user?.username || 'غير محدد'} (ID: ${attempt.userId || 'N/A'})`);
      console.log(`      🌐 IP: ${attempt.ipAddress}`);
      console.log(`      📱 Device ID: ${attempt.deviceId || 'غير محدد'}`);
      console.log(`      🔍 النوع: ${attempt.userType}`);
      if (!attempt.success && attempt.failureReason) {
        console.log(`      ❌ سبب الفشل: ${attempt.failureReason}`);
      }
      console.log('');
    });

    // 3. البحث عن المستخدم admin تحديداً
    console.log('3️⃣ فحص المستخدم admin تحديداً:');
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: 'admin' },
          { loginName: 'admin' }
        ]
      }
    });

    if (adminUser) {
      console.log('   👤 تم العثور على المستخدم admin:');
      console.log(`      ID: ${adminUser.id}`);
      console.log(`      اسم المستخدم: ${adminUser.username}`);
      console.log(`      اسم الدخول: ${adminUser.loginName}`);
      console.log(`      📱 device1: ${adminUser.device1 || '❌ فارغ'}`);
      console.log(`      📱 deviceId: ${adminUser.deviceId || '❌ فارغ'}`);
      console.log(`      ✅ نشط: ${adminUser.isActive ? 'نعم' : 'لا'}`);
      
      // فحص محاولات دخول admin
      const adminAttempts = await prisma.loginAttempt.findMany({
        where: { userId: adminUser.id },
        orderBy: { timestamp: 'desc' },
        take: 5
      });
      
      console.log(`\n   📊 آخر ${adminAttempts.length} محاولات دخول لـ admin:`);
      adminAttempts.forEach((attempt, index) => {
        console.log(`      ${index + 1}. ${attempt.success ? '✅' : '❌'} ${attempt.timestamp}`);
        console.log(`         🌐 IP: ${attempt.ipAddress}`);
        console.log(`         📱 Device: ${attempt.deviceId || 'غير محدد'}`);
        if (!attempt.success && attempt.failureReason) {
          console.log(`         ❌ السبب: ${attempt.failureReason}`);
        }
      });
    } else {
      console.log('   ❌ لم يتم العثور على المستخدم admin!');
    }

    // 4. اختبار كلمة مرور admin
    if (adminUser) {
      console.log('\n4️⃣ اختبار كلمة مرور admin:');
      const passwords = ['admin123', 'admin', 'hash8080', 'yemen123'];
      
      for (const pwd of passwords) {
        try {
          const isValid = await bcrypt.compare(pwd, adminUser.password);
          console.log(`   🔑 "${pwd}": ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
          if (isValid) {
            console.log(`   🎯 كلمة المرور الصحيحة هي: ${pwd}`);
            break;
          }
        } catch (error) {
          console.log(`   ❌ خطأ في اختبار "${pwd}": ${error.message}`);
        }
      }
    }

    // 5. فحص الخادم الحالي
    console.log('\n5️⃣ فحص الخادم الحالي:');
    try {
      const response = await fetch('http://localhost:8080/health');
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ الخادم يستجيب');
        console.log(`   📊 حالة قاعدة البيانات: ${data.database}`);
        console.log(`   👥 عدد المستخدمين: ${data.userCount}`);
      } else {
        console.log(`   ⚠️ الخادم يستجيب بحالة: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ الخادم لا يستجيب: ${error.message}`);
    }

    // 6. اختبار API تسجيل الدخول
    console.log('\n6️⃣ اختبار API تسجيل الدخول:');
    const testDeviceId = 'test_emergency_' + Date.now();
    
    try {
      const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          loginName: 'admin',
          password: 'admin123',
          deviceId: testDeviceId
        })
      });
      
      console.log(`   📡 استجابة الخادم: ${loginResponse.status}`);
      
      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        console.log('   ✅ تسجيل الدخول نجح!');
        console.log(`   👤 المستخدم: ${loginData.user?.username}`);
        console.log(`   📱 Device ID المرسل: ${testDeviceId}`);
        console.log(`   🔑 Token: ${loginData.token ? 'تم إنشاؤه' : 'لم يتم إنشاؤه'}`);
        
        // فحص إذا تم تسجيل الجهاز
        const updatedUser = await prisma.user.findUnique({
          where: { id: adminUser.id },
          select: { device1: true, deviceId: true }
        });
        
        console.log('\n   📱 حالة الأجهزة بعد تسجيل الدخول:');
        console.log(`      device1: ${updatedUser.device1 || 'فارغ'}`);
        console.log(`      deviceId: ${updatedUser.deviceId || 'فارغ'}`);
        
        if (updatedUser.device1 === testDeviceId) {
          console.log('   ✅ تم تسجيل الجهاز في device1 بنجاح');
        } else {
          console.log('   ❌ لم يتم تسجيل الجهاز في device1!');
        }
        
      } else {
        const errorData = await loginResponse.json();
        console.log('   ❌ تسجيل الدخول فشل!');
        console.log(`   📝 الخطأ: ${errorData.error || errorData.message}`);
        if (errorData.authorizedDevices) {
          console.log(`   📱 الأجهزة المصرحة: ${errorData.authorizedDevices.join(', ')}`);
        }
      }
    } catch (error) {
      console.log(`   ❌ خطأ في اختبار API: ${error.message}`);
    }

    console.log('\n🚨 انتهى الفحص الطارئ!');
    
    // تقييم الوضع الأمني
    console.log('\n📋 تقييم الوضع الأمني:');
    if (adminUser && (!adminUser.device1 && !adminUser.deviceId)) {
      console.log('🚨 خطر أمني: المستخدم admin ليس لديه أجهزة مسجلة!');
      console.log('🔧 يجب تطبيق Device Validation بشكل صارم');
    } else if (adminUser && (adminUser.device1 || adminUser.deviceId)) {
      console.log('✅ المستخدم admin لديه أجهزة مسجلة');
      console.log(`📱 device1: ${adminUser.device1 || 'فارغ'}`);
      console.log(`📱 deviceId: ${adminUser.deviceId || 'فارغ'}`);
    }

  } catch (error) {
    console.error('❌ خطأ في الفحص الطارئ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

emergencySecurityCheck().catch(console.error);
