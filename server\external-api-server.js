/**
 * خادم API خارجي للمطورين - منفذ 8081
 * External API Server for Developers - Port 8081
 */

const express = require('express');
const cors = require('cors');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const app = express();
const PORT = 8081; // منفذ خاص للـ APIs الخارجية
const prisma = new PrismaClient();

// Middleware
app.use(cors({
  origin: '*',
  credentials: true
}));

app.use(express.json());

// Logging function
function writeLog(message) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
}

// Health Check للـ API الخارجي
app.get('/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      status: 'OK',
      service: 'External API Server',
      port: PORT,
      database: 'connected',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      service: 'External API Server',
      port: PORT,
      database: 'disconnected',
      error: error.message
    });
  }
});

// External API: Health Check
app.get('/api/external/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      status: 'success',
      message: 'External API is healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        version: '1.0.0',
        port: PORT,
        service: 'External API Server'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'External API health check failed',
      error_code: 'HEALTH_CHECK_FAILED'
    });
  }
});

// External API: Agent Authentication
app.post('/api/external/agent/auth', async (req, res) => {
  try {
    const { login_name, login_password } = req.body;

    writeLog(`🔐 Agent auth: ${login_name}`);

    if (!login_name || !login_password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: login_name and login_password',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // البحث عن الوكيل
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: login_name,
        isActive: true
      }
    });

    if (!agent) {
      writeLog(`❌ Agent not found: ${login_name}`);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    const isPasswordValid = await bcrypt.compare(login_password, agent.loginPassword);

    if (!isPasswordValid) {
      writeLog(`❌ Invalid agent password: ${login_name}`);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    writeLog(`✅ Agent authenticated: ${login_name}`);

    res.json({
      status: 'success',
      agent_id: agent.id,
      agent_name: agent.agentName
    });

  } catch (error) {
    writeLog(`❌ Agent auth error: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Agent authentication service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// External API: Client Verification
app.post('/api/external/client/verify', async (req, res) => {
  try {
    const { client_code, client_token } = req.body;

    writeLog(`🔍 Client verify: ${client_code}`);

    if (!client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: client_code and client_token',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // البحث عن العميل
    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(client_code)
      }
    });

    if (!client) {
      writeLog(`❌ Client not found: ${client_code}`);
      return res.status(404).json({
        status: 'client_error'
      });
    }

    // التحقق من الـ token
    let isTokenValid = false;

    if (client.token === client_token) {
      isTokenValid = true;
    } else if (client.password && client.password.startsWith('$2b$')) {
      try {
        isTokenValid = await bcrypt.compare(client_token, client.password);
      } catch (error) {
        console.error('Bcrypt comparison error:', error);
      }
    } else if (client.password === client_token) {
      isTokenValid = true;
    }

    if (!isTokenValid) {
      writeLog(`❌ Invalid client token: ${client_code}`);
      return res.status(401).json({
        status: 'client_error'
      });
    }

    writeLog(`✅ Client verified: ${client_code}, status: ${client.status}`);

    res.json({
      status: 'success',
      client_status: client.status
    });

  } catch (error) {
    writeLog(`❌ Client verify error: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Client verification service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// External API: Direct Verification (Combined)
app.post('/api/external/verify-direct', async (req, res) => {
  try {
    const {
      agent_login_name,
      agent_login_password,
      client_code,
      client_token
    } = req.body;

    writeLog(`🔐 Direct verify: agent=${agent_login_name}, client=${client_code}`);

    if (!agent_login_name || !agent_login_password || !client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // التحقق من الوكيل
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: agent_login_name,
        isActive: true
      }
    });

    if (!agent) {
      writeLog(`❌ Agent not found: ${agent_login_name}`);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    const isPasswordValid = await bcrypt.compare(agent_login_password, agent.loginPassword);

    if (!isPasswordValid) {
      writeLog(`❌ Invalid agent password: ${agent_login_name}`);
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    // التحقق من العميل
    const client = await prisma.client.findFirst({
      where: {
        clientCode: parseInt(client_code)
      }
    });

    if (!client) {
      writeLog(`❌ Client not found: ${client_code}`);
      return res.status(404).json({
        status: 'client_error'
      });
    }

    // التحقق من الـ token
    let isTokenValid = false;

    if (client.token === client_token) {
      isTokenValid = true;
    } else if (client.password && client.password.startsWith('$2b$')) {
      try {
        isTokenValid = await bcrypt.compare(client_token, client.password);
      } catch (error) {
        console.error('Bcrypt comparison error:', error);
      }
    } else if (client.password === client_token) {
      isTokenValid = true;
    }

    if (!isTokenValid) {
      writeLog(`❌ Invalid client token: ${client_code}`);
      return res.status(401).json({
        status: 'client_error'
      });
    }

    // تسجيل العملية الناجحة
    try {
      await prisma.dataRecord.create({
        data: {
          agentId: agent.id,
          clientId: client.id,
          clientCode: client_code.toString(),
          clientPassword: client_token,
          operationDate: new Date(),
          operationStatus: 1,
          agentReference: Math.floor(Date.now() / 1000),
          clientIpAddress: req.ip || 'unknown'
        }
      });
    } catch (recordError) {
      writeLog(`⚠️ Failed to log operation: ${recordError.message}`);
    }

    writeLog(`✅ Direct verify successful: agent=${agent_login_name}, client=${client_code}, status=${client.status}`);

    res.json({
      status: 'success',
      client_status: client.status
    });

  } catch (error) {
    writeLog(`❌ Direct verify error: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// API Stats للمطورين
app.get('/api/external/stats', async (req, res) => {
  try {
    writeLog('📊 External stats request');

    const [totalClients, activeClients, totalAgents, activeAgents] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.agent.count(),
      prisma.agent.count({ where: { isActive: true } })
    ]);

    const stats = {
      success: true,
      data: {
        totalClients,
        activeClients,
        blockedClients: totalClients - activeClients,
        totalAgents,
        activeAgents,
        inactiveAgents: totalAgents - activeAgents
      }
    };

    writeLog('✅ External stats returned');

    res.json(stats);

  } catch (error) {
    writeLog(`❌ External stats error: ${error.message}`);
    res.status(500).json({
      error: 'Internal server error',
      message: 'خطأ في الخادم'
    });
  }
});

// 404 handler for unknown API endpoints
app.use('/api/*', (req, res) => {
  res.status(404).json({
    status: 'error',
    message: 'API endpoint not found',
    error_code: 'ENDPOINT_NOT_FOUND',
    path: req.path
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    service: 'External API Server',
    port: PORT,
    status: 'running',
    endpoints: [
      'GET /health',
      'GET /api/external/health',
      'POST /api/external/agent/auth',
      'POST /api/external/client/verify',
      'POST /api/external/verify-direct',
      'GET /api/external/stats'
    ],
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
  writeLog('🚀 External API Server Started!');
  writeLog(`📡 Port: ${PORT}`);
  writeLog(`🌐 Local: http://localhost:${PORT}`);
  writeLog(`🌐 Internal: http://**************:${PORT}`);
  writeLog(`🌍 External: http://***********:${PORT}`);
  writeLog('✅ Ready to serve external API requests!');
  writeLog('🔧 Dedicated server for developers!');
  
  console.log('🚀 External API Server Started!');
  console.log(`📡 Port: ${PORT}`);
  console.log(`🌐 Local: http://localhost:${PORT}`);
  console.log(`🌐 Internal: http://**************:${PORT}`);
  console.log(`🌍 External: http://***********:${PORT}`);
  console.log('✅ Ready to serve external API requests!');
  console.log('🔧 Dedicated server for developers!');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  writeLog('\n🛑 Shutting down External API server...');
  await prisma.$disconnect();
  process.exit(0);
});
