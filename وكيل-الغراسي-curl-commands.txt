# أوامر cURL لوكيل الغراسي - التحقق من العميل
# Yemen Client Management System - Agent cURL Commands

# ========================================
# الخطوة 1: تسجيل دخول الوكيل
# ========================================

curl -X POST http://localhost:8080/api/external/agent/auth \
  -H "Content-Type: application/json" \
  -d '{
    "login_name": "agent001",
    "login_password": "agent123"
  }'

# النتيجة المتوقعة:
# {
#   "status": "success",
#   "message": "Agent authenticated successfully",
#   "data": {
#     "agent_id": 3,
#     "agent_name": "وكيل اختبار",
#     "agency_type": "وكيل يمن موبايل",
#     "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
#     "expires_at": "2025-07-01T23:40:00Z"
#   }
# }

# ========================================
# الخطوة 2: التحقق من العميل
# ========================================
# ملاحظة: استبدل YOUR_AGENT_TOKEN بالتوكن الذي حصلت عليه من الخطوة الأولى

curl -X POST http://localhost:8080/api/external/client/verify \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_AGENT_TOKEN" \
  -d '{
    "client_code": "1000",
    "token": "ABC12345"
  }'

# النتيجة المتوقعة (عميل موجود):
# {
#   "status": "success",
#   "message": "Client verified successfully",
#   "data": {
#     "client_code": 1000,
#     "client_name": "محمد علي الحاشدي",
#     "app_name": "تطبيق العميل",
#     "status": 1,
#     "ip_address": "*************",
#     "created_date": "2025-06-30",
#     "created_by_user": "admin"
#   }
# }

# النتيجة المتوقعة (عميل غير موجود):
# {
#   "status": "error",
#   "message": "Client not found",
#   "error_code": "CLIENT_NOT_FOUND"
# }

# النتيجة المتوقعة (توكن خاطئ):
# {
#   "status": "error",
#   "message": "Invalid client token",
#   "error_code": "TOKEN_MISMATCH"
# }

# ========================================
# أمثلة إضافية
# ========================================

# فحص حالة النظام:
curl -X GET http://localhost:8080/api/external/health

# الحصول على إحصائيات الوكيل:
curl -X GET http://localhost:8080/api/external/agent/stats \
  -H "Authorization: Bearer YOUR_AGENT_TOKEN"

# الحصول على سجل العمليات:
curl -X GET "http://localhost:8080/api/external/agent/operations?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_AGENT_TOKEN"

# تسجيل خروج الوكيل:
curl -X POST http://localhost:8080/api/external/agent/logout \
  -H "Authorization: Bearer YOUR_AGENT_TOKEN"

# ========================================
# مثال كامل بـ PowerShell (Windows)
# ========================================

# الخطوة 1: تسجيل الدخول
$authResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/external/agent/auth" -Method POST -ContentType "application/json" -Body '{"login_name": "agent001", "login_password": "agent123"}'

# استخراج التوكن
$agentToken = $authResponse.data.token

# الخطوة 2: التحقق من العميل
$headers = @{
    "Authorization" = "Bearer $agentToken"
    "Content-Type" = "application/json"
}

$clientData = @{
    client_code = "1000"
    token = "ABC12345"
} | ConvertTo-Json

$verifyResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/external/client/verify" -Method POST -Headers $headers -Body $clientData

# عرض النتيجة
Write-Host "نتيجة التحقق من العميل:"
$verifyResponse | ConvertTo-Json -Depth 3

# ========================================
# مثال بـ Python
# ========================================

import requests
import json

# إعدادات الوكيل
AGENT_CONFIG = {
    'server_url': 'http://localhost:8080/api/external',
    'login_name': 'agent001',
    'password': 'agent123'
}

# بيانات العميل
CLIENT_DATA = {
    'client_code': '1000',
    'token': 'ABC12345'
}

def authenticate_agent():
    """تسجيل دخول الوكيل"""
    url = f"{AGENT_CONFIG['server_url']}/agent/auth"
    data = {
        'login_name': AGENT_CONFIG['login_name'],
        'login_password': AGENT_CONFIG['password']
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    if result['status'] == 'success':
        print(f"✅ تم تسجيل الدخول: {result['data']['agent_name']}")
        return result['data']['token']
    else:
        print(f"❌ فشل تسجيل الدخول: {result['message']}")
        return None

def verify_client(agent_token):
    """التحقق من العميل"""
    url = f"{AGENT_CONFIG['server_url']}/client/verify"
    headers = {
        'Authorization': f'Bearer {agent_token}',
        'Content-Type': 'application/json'
    }
    data = {
        'client_code': CLIENT_DATA['client_code'],
        'token': CLIENT_DATA['token']
    }
    
    response = requests.post(url, json=data, headers=headers)
    result = response.json()
    
    if result['status'] == 'success':
        client = result['data']
        print(f"✅ تم العثور على العميل: {client['client_name']}")
        print(f"   حالة العميل: {'نشط' if client['status'] == 1 else 'غير نشط'}")
        print(f"   التوكن: مطابق")
        return True
    else:
        print(f"❌ فشل التحقق: {result['message']}")
        return False

# تنفيذ العملية الكاملة
def main():
    print("🚀 بدء عملية التحقق من العميل...")
    
    # تسجيل دخول الوكيل
    agent_token = authenticate_agent()
    if not agent_token:
        return
    
    # التحقق من العميل
    verify_client(agent_token)

if __name__ == "__main__":
    main()

# ========================================
# مثال بـ PHP
# ========================================

<?php
// إعدادات الوكيل
$agentConfig = [
    'server_url' => 'http://localhost:8080/api/external',
    'login_name' => 'agent001',
    'password' => 'agent123'
];

// بيانات العميل
$clientData = [
    'client_code' => '1000',
    'token' => 'ABC12345'
];

function authenticateAgent($config) {
    $url = $config['server_url'] . '/agent/auth';
    $data = [
        'login_name' => $config['login_name'],
        'login_password' => $config['password']
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    $response = json_decode($result, true);
    
    if ($response['status'] === 'success') {
        echo "✅ تم تسجيل الدخول: " . $response['data']['agent_name'] . "\n";
        return $response['data']['token'];
    } else {
        echo "❌ فشل تسجيل الدخول: " . $response['message'] . "\n";
        return null;
    }
}

function verifyClient($agentToken, $clientData, $serverUrl) {
    $url = $serverUrl . '/client/verify';
    $data = [
        'client_code' => $clientData['client_code'],
        'token' => $clientData['token']
    ];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/json\r\n" .
                       "Authorization: Bearer $agentToken\r\n",
            'method' => 'POST',
            'content' => json_encode($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    $response = json_decode($result, true);
    
    if ($response['status'] === 'success') {
        $client = $response['data'];
        echo "✅ تم العثور على العميل: " . $client['client_name'] . "\n";
        echo "   حالة العميل: " . ($client['status'] == 1 ? 'نشط' : 'غير نشط') . "\n";
        echo "   التوكن: مطابق\n";
        return true;
    } else {
        echo "❌ فشل التحقق: " . $response['message'] . "\n";
        return false;
    }
}

// تنفيذ العملية
echo "🚀 بدء عملية التحقق من العميل...\n";

$agentToken = authenticateAgent($agentConfig);
if ($agentToken) {
    verifyClient($agentToken, $clientData, $agentConfig['server_url']);
}
?>

# ========================================
# ملاحظات مهمة
# ========================================

1. استبدل "localhost" بـ "***********" للوصول من خارج الخادم
2. تأكد من أن المنفذ 8080 مفتوح ومتاح
3. احفظ التوكن بشكل آمن ولا تشاركه
4. التوكن ينتهي بعد 24 ساعة ويحتاج تجديد
5. تحقق من حالة النظام أولاً قبل إرسال الطلبات

# ========================================
# أكواد الأخطاء الشائعة
# ========================================

AUTH_FAILED          - فشل مصادقة الوكيل
CLIENT_NOT_FOUND     - العميل غير موجود
TOKEN_MISMATCH       - التوكن غير مطابق
INVALID_AGENT_TOKEN  - توكن الوكيل غير صالح
RATE_LIMIT_EXCEEDED  - تجاوز حد الطلبات
SERVER_ERROR         - خطأ في الخادم
