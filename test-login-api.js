/**
 * اختبار API تسجيل الدخول
 */

async function testLoginAPI() {
  console.log('🔐 اختبار API تسجيل الدخول...\n');

  try {
    // اختبار تسجيل دخول صحيح
    console.log('1️⃣ اختبار تسجيل دخول صحيح:');
    const loginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        loginName: 'admin',
        password: 'admin123',
        deviceId: 'test_device_' + Date.now()
      })
    });

    console.log(`   📡 Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      console.log('   ✅ تسجيل الدخول نجح!');
      console.log(`   👤 المستخدم: ${loginData.user?.username}`);
      console.log(`   🔑 النجاح: ${loginData.success}`);
      console.log(`   📝 الرسالة: ${loginData.message}`);
    } else {
      const errorData = await loginResponse.json();
      console.log('   ❌ تسجيل الدخول فشل!');
      console.log(`   📝 الخطأ: ${errorData.message || errorData.error}`);
    }
    console.log('');

    // اختبار تسجيل دخول خاطئ
    console.log('2️⃣ اختبار تسجيل دخول خاطئ:');
    const wrongLoginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        loginName: 'wrong_user',
        password: 'wrong_password',
        deviceId: 'test_device_' + Date.now()
      })
    });

    console.log(`   📡 Status: ${wrongLoginResponse.status}`);
    
    if (wrongLoginResponse.ok) {
      console.log('   ⚠️ تسجيل الدخول نجح (غير متوقع)');
    } else {
      const errorData = await wrongLoginResponse.json();
      console.log('   ✅ تسجيل الدخول فشل كما هو متوقع');
      console.log(`   📝 الرسالة: ${errorData.message || errorData.error}`);
    }
    console.log('');

    // اختبار مع hash8080
    console.log('3️⃣ اختبار تسجيل دخول hash8080:');
    const hashLoginResponse = await fetch('http://localhost:8080/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        loginName: 'hash8080',
        password: 'hash8080',
        deviceId: 'test_device_' + Date.now()
      })
    });

    console.log(`   📡 Status: ${hashLoginResponse.status}`);
    
    if (hashLoginResponse.ok) {
      const hashData = await hashLoginResponse.json();
      console.log('   ✅ تسجيل دخول hash8080 نجح!');
      console.log(`   👤 المستخدم: ${hashData.user?.username}`);
    } else {
      const errorData = await hashLoginResponse.json();
      console.log('   ❌ تسجيل دخول hash8080 فشل');
      console.log(`   📝 الرسالة: ${errorData.message || errorData.error}`);
    }

  } catch (error) {
    console.error('❌ خطأ في اختبار تسجيل الدخول:', error.message);
  }
}

testLoginAPI().catch(console.error);
