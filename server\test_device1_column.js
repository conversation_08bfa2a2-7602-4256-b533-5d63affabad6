const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function testDevice1Column() {
  try {
    console.log('🧪 اختبار العمود الجديد device1')
    console.log('==============================')
    
    // البحث عن المستخدم محمد الحاشدي
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        device1: true,
        isActive: true
      }
    })

    if (!user) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }
    
    console.log(`👤 المستخدم: ${user.username}`)
    console.log(`🆔 معرف المستخدم: ${user.id}`)
    console.log(`📱 deviceId (قديم): ${user.deviceId || 'فارغ'}`)
    console.log(`📱 device1 (جديد): ${user.device1 || 'فارغ'}`)
    console.log(`✅ نشط: ${user.isActive ? 'نعم' : 'لا'}`)
    
    // اختبار منطق التحقق من الجهاز
    const testDeviceId = 'honbi5nms_1751046183491'
    console.log('')
    console.log(`🧪 اختبار التحقق من الجهاز: ${testDeviceId}`)
    
    let isDeviceAllowed = false;
    let allowedDevices = [];
    
    // التحقق من العمود الجديد device1 أولاً
    if (user.device1) {
      allowedDevices.push(user.device1);
      if (user.device1 === testDeviceId) {
        isDeviceAllowed = true;
        console.log('✅ الجهاز موجود في العمود الجديد device1')
      }
    }
    
    // التحقق من العمود القديم deviceId
    if (user.deviceId && !isDeviceAllowed) {
      const oldDevices = user.deviceId.includes(',')
        ? user.deviceId.split(',').map(id => id.trim())
        : [user.deviceId];
      
      allowedDevices = [...allowedDevices, ...oldDevices];
      
      if (oldDevices.includes(testDeviceId)) {
        isDeviceAllowed = true;
        console.log('✅ الجهاز موجود في العمود القديم deviceId')
      }
    }
    
    // إزالة التكرارات
    allowedDevices = [...new Set(allowedDevices)];
    
    console.log(`📱 الأجهزة المسموحة: ${allowedDevices.join(', ')}`)
    console.log(`🔐 هل الجهاز مسموح: ${isDeviceAllowed ? 'نعم ✅' : 'لا ❌'}`)
    
    // اختبار تسجيل الدخول الفعلي
    console.log('')
    console.log('🧪 اختبار تسجيل الدخول الفعلي:')
    
    try {
      const response = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          loginName: 'hash8080',
          password: 'hash8080',
          deviceId: testDeviceId
        })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        console.log('✅ تسجيل الدخول نجح!')
        console.log(`🔑 التوكن: ${result.token ? 'تم إنشاؤه بنجاح' : 'لم يتم إنشاؤه'}`)
        console.log(`👤 المستخدم: ${result.user ? result.user.username : 'غير متوفر'}`)
      } else {
        console.log('❌ تسجيل الدخول فشل!')
        console.log(`📝 السبب: ${result.error || 'غير محدد'}`)
        console.log(`📝 التفاصيل: ${result.message || 'غير متوفرة'}`)
        console.log(`📝 الأجهزة المسموحة: ${result.authorizedDevices ? result.authorizedDevices.join(', ') : 'غير متوفرة'}`)
      }
      
    } catch (error) {
      console.log(`❌ خطأ في اختبار تسجيل الدخول: ${error.message}`)
    }
    
    console.log('')
    console.log('📊 ملخص الاختبار:')
    console.log('==================')
    console.log(`✅ العمود device1 يعمل: ${user.device1 ? 'نعم' : 'لا'}`)
    console.log(`✅ العمود deviceId يعمل: ${user.deviceId ? 'نعم' : 'لا'}`)
    console.log(`✅ منطق التحقق يعمل: ${isDeviceAllowed ? 'نعم' : 'لا'}`)
    console.log(`✅ تسجيل الدخول يعمل: سيتم التحقق أعلاه`)
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDevice1Column()
