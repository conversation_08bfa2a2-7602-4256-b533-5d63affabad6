const { PrismaClient } = require('./server/node_modules/@prisma/client')

const prisma = new PrismaClient()

async function checkUserData() {
  try {
    console.log('🔍 فحص بيانات المستخدم hash8080...')
    console.log('=' .repeat(50))
    
    const user = await prisma.user.findFirst({
      where: { loginName: 'hash8080' }
    })
    
    if (user) {
      console.log('👤 بيانات المستخدم:')
      console.log(`   الرقم: ${user.id}`)
      console.log(`   الاسم: ${user.username}`)
      console.log(`   اسم الدخول: ${user.loginName}`)
      console.log(`   الحالة: ${user.isActive ? 'نشط ✅' : 'غير نشط ❌'}`)
      console.log(`   الجهاز القديم: ${user.deviceId || 'غير محدد'}`)
      console.log(`   الجهاز الجديد: ${user.device1 || 'غير محدد'}`)
      console.log(`   مدير النظام: ${user.permissions?.isAdmin ? 'نعم ✅' : 'لا ❌'}`)
      console.log('')
      
      // اختبار تسجيل الدخول مع الجهاز الصحيح
      const deviceToUse = user.device1 || user.deviceId || 'honbi5nms_1751046183491'
      
      console.log(`🔐 اختبار تسجيل الدخول مع الجهاز: ${deviceToUse}`)
      
      const response = await fetch('http://localhost:8080/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          loginName: 'hash8080',
          password: 'hash8080',
          deviceId: deviceToUse
        })
      })
      
      const result = await response.json()
      
      console.log('')
      console.log('📊 نتيجة تسجيل الدخول:')
      console.log(`   الحالة: ${response.ok ? 'نجح ✅' : 'فشل ❌'}`)
      console.log(`   الكود: ${response.status}`)
      
      if (response.ok && result.user) {
        console.log('')
        console.log('✅ بيانات المستخدم المستلمة من الخادم:')
        console.log(`   الرقم: ${result.user.id}`)
        console.log(`   الاسم: ${result.user.username}`)
        console.log(`   اسم الدخول: ${result.user.loginName}`)
        console.log(`   isActive موجود: ${result.user.isActive !== undefined ? 'نعم ✅' : 'لا ❌'}`)
        console.log(`   قيمة isActive: ${result.user.isActive}`)
        console.log(`   الحالة: ${result.user.isActive ? 'نشط ✅' : 'غير نشط ❌'}`)
        console.log(`   مدير النظام: ${result.user.permissions?.isAdmin ? 'نعم ✅' : 'لا ❌'}`)
        
        console.log('')
        console.log('🔍 تحليل المشكلة:')
        if (result.user.isActive === undefined) {
          console.log('❌ المشكلة: isActive غير موجود في الرد من الخادم')
          console.log('🔧 الحل: تحديث الخادم المستخدم حالياً')
        } else if (result.user.isActive === true) {
          console.log('✅ isActive موجود ويظهر نشط')
          console.log('💡 المشكلة قد تكون في localStorage أو المكون')
        } else {
          console.log('⚠️ المستخدم غير نشط في قاعدة البيانات')
        }
        
      } else {
        console.log('')
        console.log('❌ فشل في تسجيل الدخول:')
        console.log(`   الخطأ: ${result.error || 'غير محدد'}`)
      }
      
    } else {
      console.log('❌ المستخدم غير موجود')
    }
    
  } catch (error) {
    console.error('❌ خطأ:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

checkUserData()
