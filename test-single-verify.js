/**
 * اختبار واحد لـ verify-direct
 */

async function testSingleVerify() {
  console.log('🧪 اختبار واحد لـ verify-direct...\n');

  try {
    const response = await fetch('http://localhost:8080/api/external/verify-direct', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      })
    });

    console.log(`📡 Status: ${response.status}`);
    
    const result = await response.json();
    console.log('📝 Response:', JSON.stringify(result, null, 2));
    
    if (response.ok && result.status === 'success') {
      console.log('✅ نجح الاختبار!');
    } else {
      console.log('❌ فشل الاختبار');
      if (result.debug) {
        console.log('🐛 Debug info:', result.debug);
      }
    }
    
  } catch (error) {
    console.log(`❌ خطأ في الاختبار: ${error.message}`);
  }
}

testSingleVerify().catch(console.error);
