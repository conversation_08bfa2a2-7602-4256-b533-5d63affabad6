@echo off
echo 🚀 تشغيل خادم النظام...
echo ========================

cd /d C:\yemclinet

echo 📋 التحقق من المجلد الحالي...
echo %CD%

echo 📦 التحقق من ملفات الخادم...
if exist "server\working-server.js" (
    echo ✅ تم العثور على working-server.js
) else (
    echo ❌ لم يتم العثور على working-server.js
)

if exist "server\complete-server.js" (
    echo ✅ تم العثور على complete-server.js
) else (
    echo ❌ لم يتم العثور على complete-server.js
)

echo.
echo 🔧 محاولة تشغيل الخادم...
echo ========================

echo 📝 محاولة 1: working-server.js
node server\working-server.js

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تشغيل working-server.js
    echo.
    echo 📝 محاولة 2: complete-server.js
    node server\complete-server.js
)

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تشغيل complete-server.js
    echo.
    echo 📝 محاولة 3: الخادم البسيط
    node خادم-بسيط.js
)

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في تشغيل جميع الخوادم
    echo 💡 تحقق من تثبيت Node.js والمكتبات المطلوبة
    echo.
    echo 🔧 لحل المشكلة:
    echo 1. تأكد من تثبيت Node.js
    echo 2. شغل: npm install في مجلد server
    echo 3. تحقق من ملفات الخادم
)

echo.
echo ⏸️ اضغط أي مفتاح للخروج...
pause > nul
