// اختبار سريع لإنشاء عميل مع userId
const { prisma } = require('./middleware/prisma');

async function testCreateClient() {
  try {
    console.log('🧪 اختبار إنشاء عميل مع userId...');
    
    // البحث عن مستخدم
    const user = await prisma.user.findFirst({
      where: { username: 'admin' }
    });
    
    if (!user) {
      console.log('❌ لم يتم العثور على مستخدم admin');
      return;
    }
    
    console.log('👤 المستخدم الموجود:', {
      id: user.id,
      username: user.username,
      loginName: user.loginName
    });
    
    // إنشاء عميل جديد
    const newClient = await prisma.client.create({
      data: {
        clientName: 'عميل اختبار تلقائي',
        appName: 'تطبيق اختبار',
        cardNumber: '888777666',
        clientCode: 8888,
        password: 'test1234',
        ipAddress: '192.168.1.888',
        status: 1,
        userId: user.id // إضافة userId يدوياً للاختبار
      }
    });
    
    console.log('✅ تم إنشاء العميل:', {
      id: newClient.id,
      clientCode: newClient.clientCode,
      userId: newClient.userId,
      clientName: newClient.clientName
    });
    
    // حذف العميل التجريبي
    await prisma.client.delete({
      where: { id: newClient.id }
    });
    
    console.log('🗑️ تم حذف العميل التجريبي');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testCreateClient();
