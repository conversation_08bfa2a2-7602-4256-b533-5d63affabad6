<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار حالة المستخدم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 30px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(45deg, #2c3e50, #3498db);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h1 {
            margin: 0;
            font-size: 28px;
        }
        
        .subtitle {
            margin: 15px 0 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-right: 4px solid #28a745;
        }
        
        .result {
            background: #2c3e50;
            color: white;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .profile-mock {
            background: #ecf0f1;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .profile-mock h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
        }
        
        .profile-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #bdc3c7;
        }
        
        .profile-field:last-child {
            border-bottom: none;
        }
        
        .profile-label {
            font-weight: bold;
            color: #7f8c8d;
        }
        
        .profile-value {
            color: #2c3e50;
        }
        
        .status-active {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-inactive {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .status-unknown {
            color: #f39c12;
            font-weight: bold;
        }
        
        button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin: 10px 5px;
            transition: all 0.3s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }
        
        .problem-analysis {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .solution {
            background: #d4edda;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار حالة المستخدم</h1>
            <p class="subtitle">فحص مشكلة عرض "غير نشط" في الملف الشخصي</p>
        </div>

        <!-- وصف المشكلة -->
        <div class="problem-analysis">
            <h3>🔍 وصف المشكلة:</h3>
            <p>المستخدم يظهر كـ "غير نشط" في نافذة الملف الشخصي مع أنه نشط ومدير للنظام.</p>
            <p><strong>السبب:</strong> الخادم لا يرسل حقل <code>isActive</code> في بيانات المستخدم عند تسجيل الدخول.</p>
        </div>

        <!-- الحل -->
        <div class="solution">
            <h3>🔧 الحل المطبق:</h3>
            <p>تم إضافة <code>isActive: user.isActive</code> إلى جميع خوادم تسجيل الدخول:</p>
            <ul>
                <li>✅ server/complete-server.js</li>
                <li>✅ server/working-server.js</li>
                <li>✅ server/main-server.js</li>
            </ul>
        </div>

        <!-- اختبار تسجيل الدخول -->
        <div class="test-section">
            <h3>🔐 اختبار تسجيل الدخول</h3>
            <button onclick="testLogin()">تشغيل اختبار تسجيل الدخول</button>
            <div id="loginResult" class="result" style="display: none;"></div>
        </div>

        <!-- محاكاة الملف الشخصي -->
        <div class="test-section">
            <h3>👤 محاكاة الملف الشخصي</h3>
            <div id="profileMock" class="profile-mock" style="display: none;">
                <h3>👤 الملف الشخصي</h3>
                <div class="profile-field">
                    <span class="profile-label">رقم المستخدم:</span>
                    <span class="profile-value" id="userId">-</span>
                </div>
                <div class="profile-field">
                    <span class="profile-label">اسم المستخدم:</span>
                    <span class="profile-value" id="username">-</span>
                </div>
                <div class="profile-field">
                    <span class="profile-label">اسم الدخول:</span>
                    <span class="profile-value" id="loginName">-</span>
                </div>
                <div class="profile-field">
                    <span class="profile-label">الحالة:</span>
                    <span class="profile-value" id="userStatus">-</span>
                </div>
                <div class="profile-field">
                    <span class="profile-label">النوع:</span>
                    <span class="profile-value" id="userType">-</span>
                </div>
            </div>
        </div>

        <!-- تحليل النتائج -->
        <div class="test-section">
            <h3>📊 تحليل النتائج</h3>
            <div id="analysis" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            const profileDiv = document.getElementById('profileMock');
            const analysisDiv = document.getElementById('analysis');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = '⏳ جاري اختبار تسجيل الدخول...';
            
            try {
                const response = await fetch('http://localhost:8080/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        loginName: 'hash8080',
                        password: 'hash8080',
                        deviceId: 'test_device_' + Date.now()
                    })
                });
                
                const result = await response.json();
                
                let output = '🧪 نتائج اختبار تسجيل الدخول:\n';
                output += '=' .repeat(50) + '\n\n';
                output += `📊 حالة الاستجابة: ${response.ok ? 'نجح ✅' : 'فشل ❌'}\n`;
                output += `🔢 كود الاستجابة: ${response.status}\n\n`;
                
                if (response.ok && result.user) {
                    output += '👤 بيانات المستخدم المستلمة:\n';
                    output += `   الرقم: ${result.user.id}\n`;
                    output += `   الاسم: ${result.user.username}\n`;
                    output += `   اسم الدخول: ${result.user.loginName}\n`;
                    output += `   isActive موجود: ${result.user.isActive !== undefined ? 'نعم ✅' : 'لا ❌'}\n`;
                    output += `   قيمة isActive: ${result.user.isActive}\n`;
                    output += `   الحالة: ${result.user.isActive ? 'نشط ✅' : 'غير نشط ❌'}\n`;
                    output += `   مدير النظام: ${result.user.permissions?.isAdmin ? 'نعم ✅' : 'لا ❌'}\n\n`;
                    
                    // تحديث محاكاة الملف الشخصي
                    updateProfileMock(result.user);
                    profileDiv.style.display = 'block';
                    
                    // تحليل النتائج
                    analyzeResults(result.user);
                    analysisDiv.style.display = 'block';
                    
                } else {
                    output += '❌ فشل في تسجيل الدخول:\n';
                    output += `   الخطأ: ${result.error || 'غير محدد'}\n`;
                }
                
                resultDiv.textContent = output;
                
            } catch (error) {
                resultDiv.textContent = `❌ خطأ في الاتصال: ${error.message}`;
            }
        }
        
        function updateProfileMock(user) {
            document.getElementById('userId').textContent = user.id;
            document.getElementById('username').textContent = user.username;
            document.getElementById('loginName').textContent = user.loginName;
            
            const statusElement = document.getElementById('userStatus');
            if (user.isActive !== undefined) {
                if (user.isActive) {
                    statusElement.textContent = 'نشط ✅';
                    statusElement.className = 'profile-value status-active';
                } else {
                    statusElement.textContent = 'غير نشط ❌';
                    statusElement.className = 'profile-value status-inactive';
                }
            } else {
                statusElement.textContent = 'غير محدد ⚠️';
                statusElement.className = 'profile-value status-unknown';
            }
            
            document.getElementById('userType').textContent = 
                user.permissions?.isAdmin ? 'مدير النظام' : 'مستخدم عادي';
        }
        
        function analyzeResults(user) {
            const analysisDiv = document.getElementById('analysis');
            let analysis = '';
            
            if (user.isActive === undefined) {
                analysis = `
                    <div class="problem-analysis">
                        <h4>❌ المشكلة لم يتم حلها بعد</h4>
                        <p>حقل <code>isActive</code> لا يزال غير موجود في بيانات المستخدم.</p>
                        <p><strong>الإجراء المطلوب:</strong> تأكد من إعادة تشغيل الخادم بعد التحديثات.</p>
                    </div>
                `;
            } else if (user.isActive === true) {
                analysis = `
                    <div class="solution">
                        <h4>✅ تم حل المشكلة بنجاح!</h4>
                        <p>حقل <code>isActive</code> موجود ويظهر القيمة الصحيحة: <strong>نشط</strong></p>
                        <p>الآن سيظهر المستخدم كـ "نشط" في نافذة الملف الشخصي.</p>
                    </div>
                `;
            } else {
                analysis = `
                    <div class="problem-analysis">
                        <h4>⚠️ المستخدم غير نشط في قاعدة البيانات</h4>
                        <p>حقل <code>isActive</code> موجود لكن قيمته <code>false</code>.</p>
                        <p><strong>الإجراء المطلوب:</strong> تفعيل المستخدم في قاعدة البيانات.</p>
                    </div>
                `;
            }
            
            analysisDiv.innerHTML = analysis;
        }
        
        // تشغيل الاختبار تلقائياً عند تحميل الصفحة
        window.onload = function() {
            console.log('🧪 صفحة اختبار حالة المستخدم جاهزة');
        };
    </script>
</body>
</html>
