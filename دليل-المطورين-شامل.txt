# 🚀 دليل المطورين الشامل - نظام التحقق من العملاء
# Yemen Client Management System - Complete Developer Guide

========================================
📋 نظرة عامة:
========================================

🎯 الغرض: التحقق من حالة العملاء عبر API
🔑 الوصول: الوكلاء المعتمدون فقط
📊 النتيجة: حالة العميل أو رسالة خطأ محددة
🌐 العنوان: http://***********:8080/api/external/verify-direct

========================================
🔐 بيانات الاختبار:
========================================

## الوكيل التجريبي:
اسم المستخدم: testuser
كلمة المرور: test123
الاسم: فحص تجريبي

## العملاء التجريبيون:
1004 + TEST1004 = نشط (نجاح متوقع)
1005 + TEST1005 = غير نشط (نجاح لكن غير نشط)
9999 + DUMMY999 = نشط (للاختبار)

## عملاء إضافيون:
1000 + ABC12345 = نشط
1001 + XYZ67890 = نشط
1002 + DEF54321 = نشط

========================================
📡 تفاصيل الـ API:
========================================

**عنوان الخادم:** http://***********:8080/api/external/verify-direct
**طريقة الطلب:** POST
**نوع المحتوى:** application/json

**البيانات المطلوبة:**
```json
{
  "agent_login_name": "testuser",
  "agent_login_password": "test123",
  "client_code": "1004",
  "client_token": "TEST1004"
}
```

========================================
📊 الردود المختلفة:
========================================

## 1. ✅ نجح التحقق - العميل نشط:
```json
{"status":"success","client_status":1}
```

## 2. ✅ نجح التحقق - العميل غير نشط:
```json
{"status":"success","client_status":0}
```

## 3. ❌ خطأ في بيانات الوكيل:
```json
{"status":"agent_error"}
```
**الأسباب:** اسم المستخدم أو كلمة المرور خاطئة

## 4. ❌ خطأ في بيانات العميل:
```json
{"status":"client_error"}
```
**الأسباب:** العميل غير موجود أو التوكن غير مطابق

## 5. ❌ خطأ في الخادم:
```json
{"status":"error"}
```
**الأسباب:** خطأ عام في النظام

========================================
🧪 اختبارات مقترحة:
========================================

## اختبار النجاح:
```bash
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "testuser",
    "agent_login_password": "test123",
    "client_code": "1004",
    "client_token": "TEST1004"
  }'
```
**النتيجة المتوقعة:** `{"status":"success","client_status":1}`

## اختبار خطأ الوكيل:
```bash
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "testuser",
    "agent_login_password": "WRONG_PASSWORD",
    "client_code": "1004",
    "client_token": "TEST1004"
  }'
```
**النتيجة المتوقعة:** `{"status":"agent_error"}`

## اختبار خطأ العميل:
```bash
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "testuser",
    "agent_login_password": "test123",
    "client_code": "8888",
    "client_token": "INVALID"
  }'
```
**النتيجة المتوقعة:** `{"status":"client_error"}`

## اختبار توكن خاطئ:
```bash
curl -X POST http://***********:8080/api/external/verify-direct \
  -H "Content-Type: application/json" \
  -d '{
    "agent_login_name": "testuser",
    "agent_login_password": "test123",
    "client_code": "1004",
    "client_token": "WRONG_TOKEN"
  }'
```
**النتيجة المتوقعة:** `{"status":"client_error"}`

========================================
💻 أمثلة الكود:
========================================

## JavaScript (Node.js / Browser):
```javascript
async function verifyClient(clientCode, clientToken) {
    try {
        const response = await fetch('http://***********:8080/api/external/verify-direct', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                agent_login_name: 'testuser',
                agent_login_password: 'test123',
                client_code: clientCode,
                client_token: clientToken
            })
        });

        const result = await response.json();
        
        switch (result.status) {
            case 'success':
                if (result.client_status === 1) {
                    console.log('✅ العميل نشط - يمكن المتابعة');
                    return { success: true, active: true };
                } else {
                    console.log('⚠️ العميل غير نشط - لا يمكن المتابعة');
                    return { success: true, active: false };
                }
            case 'agent_error':
                console.log('❌ خطأ في بيانات الوكيل');
                return { success: false, error: 'agent_error' };
            case 'client_error':
                console.log('❌ خطأ في بيانات العميل');
                return { success: false, error: 'client_error' };
            default:
                console.log('❌ خطأ في النظام');
                return { success: false, error: 'system_error' };
        }
    } catch (error) {
        console.log('❌ خطأ في الاتصال');
        return { success: false, error: 'connection_error' };
    }
}

// الاستخدام:
const result = await verifyClient('1004', 'TEST1004');
if (result.success && result.active) {
    // العميل نشط - تابع العملية
} else {
    // العميل غير نشط أو خطأ
}
```

## PHP:
```php
<?php
function verifyClient($clientCode, $clientToken) {
    $url = 'http://***********:8080/api/external/verify-direct';
    
    $data = array(
        'agent_login_name' => 'testuser',
        'agent_login_password' => 'test123',
        'client_code' => $clientCode,
        'client_token' => $clientToken
    );
    
    $options = array(
        'http' => array(
            'header' => "Content-type: application/json\r\n",
            'method' => 'POST',
            'content' => json_encode($data),
            'timeout' => 30
        )
    );
    
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
    
    if ($result === FALSE) {
        return array('success' => false, 'error' => 'connection_error');
    }
    
    $response = json_decode($result, true);
    
    switch ($response['status']) {
        case 'success':
            return array(
                'success' => true,
                'active' => $response['client_status'] == 1
            );
        case 'agent_error':
            return array('success' => false, 'error' => 'agent_error');
        case 'client_error':
            return array('success' => false, 'error' => 'client_error');
        default:
            return array('success' => false, 'error' => 'system_error');
    }
}

// الاستخدام:
$result = verifyClient('1004', 'TEST1004');

if ($result['success']) {
    if ($result['active']) {
        echo "العميل نشط - يمكن المتابعة";
    } else {
        echo "العميل غير نشط - لا يمكن المتابعة";
    }
} else {
    echo "خطأ: " . $result['error'];
}
?>
```

## Python:
```python
import requests
import json

def verify_client(client_code, client_token):
    url = 'http://***********:8080/api/external/verify-direct'
    
    data = {
        'agent_login_name': 'testuser',
        'agent_login_password': 'test123',
        'client_code': client_code,
        'client_token': client_token
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        result = response.json()
        
        if result['status'] == 'success':
            return {
                'success': True,
                'active': result['client_status'] == 1
            }
        elif result['status'] == 'agent_error':
            return {'success': False, 'error': 'agent_error'}
        elif result['status'] == 'client_error':
            return {'success': False, 'error': 'client_error'}
        else:
            return {'success': False, 'error': 'system_error'}
    except:
        return {'success': False, 'error': 'connection_error'}

# الاستخدام:
result = verify_client('1004', 'TEST1004')

if result['success']:
    if result['active']:
        print('العميل نشط - يمكن المتابعة')
    else:
        print('العميل غير نشط - لا يمكن المتابعة')
else:
    print(f'خطأ: {result["error"]}')
```

## C#:
```csharp
using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class ClientVerifier
{
    private static readonly HttpClient client = new HttpClient();
    private readonly string serverUrl = "http://***********:8080/api/external/verify-direct";
    
    public async Task<dynamic> VerifyClient(string clientCode, string clientToken)
    {
        try
        {
            var data = new {
                agent_login_name = "testuser",
                agent_login_password = "test123",
                client_code = clientCode,
                client_token = clientToken
            };

            var json = JsonConvert.SerializeObject(data);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await client.PostAsync(serverUrl, content);
            var result = await response.Content.ReadAsStringAsync();
            var parsed = JsonConvert.DeserializeObject<dynamic>(result);

            switch ((string)parsed.status)
            {
                case "success":
                    return new {
                        success = true,
                        active = (int)parsed.client_status == 1
                    };
                case "agent_error":
                    return new { success = false, error = "agent_error" };
                case "client_error":
                    return new { success = false, error = "client_error" };
                default:
                    return new { success = false, error = "system_error" };
            }
        }
        catch
        {
            return new { success = false, error = "connection_error" };
        }
    }
}

// الاستخدام:
var verifier = new ClientVerifier();
var result = await verifier.VerifyClient("1004", "TEST1004");

if (result.success)
{
    if (result.active)
    {
        Console.WriteLine("العميل نشط - يمكن المتابعة");
    }
    else
    {
        Console.WriteLine("العميل غير نشط - لا يمكن المتابعة");
    }
}
else
{
    Console.WriteLine($"خطأ: {result.error}");
}
```

========================================
🔧 معالجة الأخطاء:
========================================

## أنواع الأخطاء:
1. **agent_error:** بيانات الوكيل خاطئة
2. **client_error:** بيانات العميل خاطئة
3. **connection_error:** مشكلة في الاتصال
4. **system_error:** خطأ في النظام

## نصائح المعالجة:
- استخدم try-catch للأخطاء
- تحقق من حالة الاستجابة
- اعرض رسائل واضحة للمستخدم
- سجل الأخطاء للمراجعة

========================================
🛡️ نصائح الأمان:
========================================

✅ احفظ بيانات الوكيل بشكل آمن
✅ لا تشارك كلمة المرور مع أحد
✅ استخدم HTTPS في الإنتاج
✅ تحقق من النتائج قبل اتخاذ إجراءات
✅ اعتمد على معالجة الأخطاء الشاملة

========================================
📞 الدعم الفني:
========================================

في حالة وجود مشاكل:
1. تحقق من عنوان الخادم
2. تأكد من صحة بيانات الوكيل
3. تحقق من اتصال الإنترنت
4. راجع رسائل الخطأ
5. اتصل بالدعم الفني إذا استمرت المشكلة

========================================
🎯 ملخص سريع:
========================================

🔹 **عنوان:** http://***********:8080/api/external/verify-direct
🔹 **طريقة:** POST
🔹 **البيانات:** بيانات الوكيل + بيانات العميل
🔹 **النتائج:** success (مع حالة العميل) أو رسالة خطأ محددة
🔹 **الاختبار:** testuser / test123
🔹 **العملاء:** 1004, 1005, 9999, 1000, 1001, 1002

🎉 جاهز للاختبار والتطوير!
