const { PrismaClient } = require('./server/node_modules/@prisma/client')
const bcrypt = require('./server/node_modules/bcrypt')

const prisma = new PrismaClient()

async function createTestAgent() {
  try {
    console.log('🔧 إنشاء وكيل تجريبي وعملاء تجريبيين...')
    console.log('=' .repeat(60))

    // إنشاء الوكيل التجريبي
    console.log('👤 إنشاء الوكيل التجريبي...')

    const hashedPassword = await bcrypt.hash('test123', 10)

    const testAgent = await prisma.agent.create({
      data: {
        agentName: 'فحص تجريبي',
        agencyName: 'وكالة الاختبار',
        agencyType: 'وكيل اختبار',
        ipAddress: '*************',
        loginName: 'testuser',
        loginPassword: hashedPassword,
        isActive: true
      }
    })

    console.log('✅ تم إنشاء الوكيل التجريبي:')
    console.log(`   الرقم: ${testAgent.id}`)
    console.log(`   الاسم: ${testAgent.agentName}`)
    console.log(`   اسم المستخدم: testuser`)
    console.log(`   كلمة المرور: test123`)
    console.log('')

    // إنشاء العملاء التجريبيين
    console.log('👥 إنشاء العملاء التجريبيين...')

    // الحصول على مستخدم افتراضي
    const defaultUser = await prisma.user.findFirst({
      where: { isActive: true }
    })

    if (!defaultUser) {
      console.error('❌ لم يتم العثور على مستخدم نشط لربط العملاء')
      return
    }

    // عميل تجريبي 1004
    const client1004 = await prisma.client.create({
      data: {
        clientCode: 1004,
        clientName: 'عميل تجريبي 1004',
        appName: 'تطبيق اختبار 1004',
        password: 'client1004pass',
        token: 'TEST1004',
        ipAddress: '*************',
        cardNumber: '1004000000000000',
        status: 1,
        userId: defaultUser.id
      }
    })

    // عميل تجريبي 1005
    const client1005 = await prisma.client.create({
      data: {
        clientCode: 1005,
        clientName: 'عميل تجريبي 1005',
        appName: 'تطبيق اختبار 1005',
        password: 'client1005pass',
        token: 'TEST1005',
        ipAddress: '*************',
        cardNumber: '1005000000000000',
        status: 0, // غير نشط للاختبار
        userId: defaultUser.id
      }
    })

    // عميل تجريبي 9999 (للاختبار الفاشل)
    const client9999 = await prisma.client.create({
      data: {
        clientCode: 9999,
        clientName: 'عميل وهمي للاختبار',
        appName: 'تطبيق وهمي',
        password: 'dummypass',
        token: 'DUMMY999',
        ipAddress: '192.168.1.999',
        cardNumber: '9999000000000000',
        status: 1,
        userId: defaultUser.id
      }
    })

    console.log('✅ تم إنشاء العملاء التجريبيين:')
    console.log('')
    console.log(`العميل 1004:`)
    console.log(`   الرقم: ${client1004.id}`)
    console.log(`   الاسم: ${client1004.clientName}`)
    console.log(`   الرمز: ${client1004.clientCode}`)
    console.log(`   التوكن: ${client1004.token}`)
    console.log(`   الحالة: ${client1004.status === 1 ? 'نشط' : 'غير نشط'}`)
    console.log('')

    console.log(`العميل 1005:`)
    console.log(`   الرقم: ${client1005.id}`)
    console.log(`   الاسم: ${client1005.clientName}`)
    console.log(`   الرمز: ${client1005.clientCode}`)
    console.log(`   التوكن: ${client1005.token}`)
    console.log(`   الحالة: ${client1005.status === 1 ? 'نشط' : 'غير نشط'}`)
    console.log('')

    console.log(`العميل 9999:`)
    console.log(`   الرقم: ${client9999.id}`)
    console.log(`   الاسم: ${client9999.clientName}`)
    console.log(`   الرمز: ${client9999.clientCode}`)
    console.log(`   التوكن: ${client9999.token}`)
    console.log(`   الحالة: ${client9999.status === 1 ? 'نشط' : 'غير نشط'}`)
    console.log('')

    // عرض ملخص شامل
    console.log('📋 ملخص البيانات التجريبية:')
    console.log('=' .repeat(50))
    console.log('')
    console.log('🏢 الوكيل التجريبي:')
    console.log('   اسم المستخدم: testuser')
    console.log('   كلمة المرور: test123')
    console.log('   الاسم: فحص تجريبي')
    console.log('')
    console.log('👥 العملاء التجريبيون:')
    console.log('   1004 + TEST1004 = نشط (نجاح متوقع)')
    console.log('   1005 + TEST1005 = غير نشط (نجاح لكن غير نشط)')
    console.log('   9999 + DUMMY999 = نشط (للاختبار)')
    console.log('')
    console.log('🔧 اختبارات مقترحة:')
    console.log('   ✅ testuser + test123 + 1004 + TEST1004 = success, client_status: 1')
    console.log('   ⚠️  testuser + test123 + 1005 + TEST1005 = success, client_status: 0')
    console.log('   ❌ testuser + WRONG + 1004 + TEST1004 = agent_error')
    console.log('   ❌ testuser + test123 + 1004 + WRONG = client_error')
    console.log('   ❌ testuser + test123 + 8888 + INVALID = client_error')
    console.log('')
    console.log('🎉 تم إنشاء جميع البيانات التجريبية بنجاح!')

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestAgent()
