# 🚀 YemClient Production Server

خادم إنتاج متقدم لنظام إدارة العملاء والوكلاء اليمني مع PM2 وWinston للمراقبة والأمان المحسن.

## 📋 المميزات

### 🔒 الأمان
- **Helmet.js** لحماية HTTP headers
- **Rate Limiting** لمنع الهجمات
- **CORS** محكم التكوين
- **Session Security** مع cookies آمنة
- **Input Validation** شامل
- **Winston Logging** متقدم

### ⚡ الأداء
- **Compression** لضغط الاستجابات
- **PM2** لإدارة العمليات
- **Database Connection Pooling**
- **Static File Caching**
- **Graceful Shutdown**

### 📊 المراقبة
- **<PERSON> Logger** مع ملفات منفصلة
- **PM2 Monitoring** 
- **Health Checks**
- **Metrics Endpoint**
- **Error Tracking**

## 🛠️ التثبيت

### 1. تثبيت التبعيات
```bash
cd server
npm install
```

### 2. تثبيت PM2 عالمياً
```bash
npm install -g pm2
```

### 3. إعداد قاعدة البيانات
```bash
npm run db:generate
npm run db:migrate
```

## 🚀 التشغيل

### التطوير
```bash
# تشغيل خادم التطوير
npm run dev

# أو تشغيل خادم قاعدة البيانات فقط
node database-only-server.js
```

### الإنتاج مع PM2
```bash
# بدء الخادم مع PM2
npm run pm2:start

# مراقبة الحالة
npm run pm2:status

# عرض اللوقات
npm run pm2:logs

# مراقبة الأداء
npm run pm2:monit

# إعادة تشغيل
npm run pm2:restart

# إعادة تحميل بدون انقطاع
npm run pm2:reload

# إيقاف الخادم
npm run pm2:stop

# حذف من PM2
npm run pm2:delete
```

### التشغيل المباشر
```bash
# تشغيل الإنتاج مباشرة
npm run prod

# تشغيل عادي
npm start
```

## 📊 المراقبة والصحة

### فحص الصحة
```bash
# فحص صحة الخادم
curl http://localhost:8080/health

# أو استخدام npm script
npm run health
```

### المقاييس
```bash
# عرض مقاييس الأداء
curl http://localhost:8080/metrics
```

### اللوقات
```bash
# عرض لوقات PM2
npm run pm2:logs

# مسح ملفات اللوق
npm run logs:clear
```

## 🔧 التكوين

### متغيرات البيئة (.env)
```env
# Database
DATABASE_URL="postgresql://postgres:yemen123@localhost:5432/yemclient_db"

# Server
PORT=8080
NODE_ENV=production

# Security
JWT_SECRET="your-super-secret-jwt-key"
SESSION_SECRET="yemclient-ultra-secure-session-key"

# Logging
LOG_LEVEL=info
```

### تكوين PM2 (ecosystem.config.js)
```javascript
module.exports = {
  apps: [{
    name: 'yemclient-server',
    script: './production-server.js',
    instances: 1,
    exec_mode: 'fork',
    max_memory_restart: '1G',
    autorestart: true,
    watch: false
  }]
};
```

## 📁 هيكل الملفات

```
server/
├── production-server.js      # خادم الإنتاج الرئيسي
├── database-only-server.js   # خادم قاعدة البيانات فقط
├── ecosystem.config.js       # تكوين PM2
├── package.json              # التبعيات والسكريبتات
├── .env                      # متغيرات البيئة
├── logs/                     # ملفات اللوق
│   ├── error.log            # أخطاء فقط
│   ├── combined.log         # جميع اللوقات
│   ├── pm2-error.log        # أخطاء PM2
│   ├── pm2-out.log          # مخرجات PM2
│   └── pm2-combined.log     # لوقات PM2 مجمعة
└── prisma/                   # قاعدة البيانات
    ├── schema.prisma
    └── migrations/
```

## 🔗 نقاط النهاية (Endpoints)

### الأساسية
- `GET /health` - فحص صحة الخادم
- `GET /ready` - جاهزية الخادم لـ PM2
- `GET /metrics` - مقاييس الأداء

### المصادقة
- `POST /api/auth/login` - تسجيل الدخول
- `POST /api/auth/logout` - تسجيل الخروج

### البيانات
- `GET /api/users` - قائمة المستخدمين
- `GET /api/clients` - قائمة العملاء
- `GET /api/agents` - قائمة الوكلاء
- `GET /api/data-records` - سجلات البيانات
- `GET /api/security/stats` - إحصائيات الأمان

## 🛡️ الأمان

### Rate Limiting
- **عام:** 100 طلب كل 15 دقيقة لكل IP
- **تسجيل الدخول:** 5 محاولات كل 15 دقيقة لكل IP

### Headers الأمان
- Content Security Policy
- X-Frame-Options
- X-Content-Type-Options
- Referrer-Policy
- X-XSS-Protection

### الجلسات
- HttpOnly cookies
- Secure cookies في الإنتاج
- SameSite protection
- Session timeout (24 ساعة)

## 📈 الأداء

### الضغط
- Gzip compression للاستجابات
- Static file caching
- ETag support

### قاعدة البيانات
- Connection pooling
- Query optimization
- Error handling

### الذاكرة
- Memory monitoring
- Automatic restart عند 1GB
- Garbage collection optimization

## 🔍 استكشاف الأخطاء

### مشاكل شائعة

#### الخادم لا يبدأ
```bash
# فحص اللوقات
npm run pm2:logs

# فحص الحالة
npm run pm2:status

# فحص قاعدة البيانات
npm run db:studio
```

#### مشاكل قاعدة البيانات
```bash
# إعادة توليد Prisma client
npm run db:generate

# تشغيل migrations
npm run db:migrate
```

#### مشاكل الذاكرة
```bash
# مراقبة الذاكرة
npm run pm2:monit

# إعادة تشغيل
npm run pm2:restart
```

## 📝 اللوقات

### مستويات اللوق
- **error:** أخطاء فقط
- **warn:** تحذيرات وأخطاء
- **info:** معلومات عامة (افتراضي)
- **debug:** تفاصيل التطوير

### ملفات اللوق
- `logs/error.log` - أخطاء فقط (5MB × 5 ملفات)
- `logs/combined.log` - جميع اللوقات (5MB × 5 ملفات)
- `logs/pm2-*.log` - لوقات PM2

## 🚀 النشر

### إعداد الخادم
```bash
# تثبيت Node.js و PM2
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
npm install -g pm2

# استنساخ المشروع
git clone <repository>
cd server

# تثبيت التبعيات
npm install

# إعداد قاعدة البيانات
npm run db:generate
npm run db:migrate

# بدء الخادم
npm run pm2:start

# حفظ تكوين PM2
npm run pm2:save

# إعداد بدء تلقائي
npm run pm2:startup
```

### تحديث الإنتاج
```bash
# سحب التحديثات
git pull origin main

# تثبيت التبعيات الجديدة
npm install

# تشغيل migrations
npm run db:migrate

# إعادة تحميل بدون انقطاع
npm run pm2:reload
```

## 📞 الدعم

للمساعدة والدعم:
- فحص اللوقات: `npm run pm2:logs`
- مراقبة الأداء: `npm run pm2:monit`
- فحص الصحة: `npm run health`

---

**تم تطويره بواسطة:** Augment Agent  
**الإصدار:** 2.0.0  
**التاريخ:** يوليو 2025
