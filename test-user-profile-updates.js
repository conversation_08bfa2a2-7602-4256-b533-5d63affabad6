/**
 * اختبار تحديثات نافذة تعديل بيانات المستخدم
 */

async function testUserProfileUpdates() {
  console.log('👤 اختبار تحديثات نافذة تعديل بيانات المستخدم...\n');

  let totalTests = 0;
  let passedTests = 0;

  try {
    // اختبار 1: API تحديث معرف الجهاز2
    console.log('1️⃣ اختبار API تحديث معرف الجهاز2:');
    totalTests++;
    
    // محاولة تحديث معرف جهاز2 لمستخدم تجريبي
    const testDevice1 = 'test_device2_' + Date.now();
    const updateResponse = await fetch('http://localhost:8080/api/users/1/device', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        device1: testDevice1
      })
    });

    console.log(`   📡 Status: ${updateResponse.status}`);
    
    if (updateResponse.ok) {
      const updateData = await updateResponse.json();
      if (updateData.message && updateData.message.includes('معرف الجهاز2')) {
        passedTests++;
        console.log('   ✅ API تحديث معرف الجهاز2 يعمل!');
        console.log(`   📱 معرف الجهاز2 الجديد: ${updateData.user?.device1}`);
      } else {
        console.log('   ❌ API تحديث معرف الجهاز2 لا يعطي الاستجابة المتوقعة');
      }
    } else {
      const errorData = await updateResponse.json();
      console.log('   ❌ API تحديث معرف الجهاز2 فشل');
      console.log(`   📝 Error: ${errorData.error}`);
    }
    console.log('');

    // اختبار 2: التحقق من ملف UserProfileDialog
    console.log('2️⃣ اختبار ملف UserProfileDialog:');
    totalTests++;
    
    try {
      const fs = require('fs');
      const userProfileContent = fs.readFileSync('client/src/components/dialogs/UserProfileDialog.jsx', 'utf8');
      
      const hasEditingDevice = userProfileContent.includes('editingDevice');
      const hasDevice1Value = userProfileContent.includes('device1Value');
      const hasDeviceMutation = userProfileContent.includes('deviceMutation');
      const hasDevice1Field = userProfileContent.includes('معرف جهاز2');
      const hasEditIcon = userProfileContent.includes('EditIcon');
      
      if (hasEditingDevice && hasDevice1Value && hasDeviceMutation && hasDevice1Field && hasEditIcon) {
        passedTests++;
        console.log('   ✅ ملف UserProfileDialog محدث بالكامل!');
        console.log('   📝 حالة التعديل: موجودة');
        console.log('   📱 قيمة معرف الجهاز2: موجودة');
        console.log('   🔄 دالة التحديث: موجودة');
        console.log('   🏷️ حقل معرف جهاز2: موجود');
        console.log('   ✏️ أيقونة التعديل: موجودة');
      } else {
        console.log('   ❌ ملف UserProfileDialog غير مكتمل');
        console.log(`   📝 حالة التعديل: ${hasEditingDevice ? '✅' : '❌'}`);
        console.log(`   📱 قيمة معرف الجهاز2: ${hasDevice1Value ? '✅' : '❌'}`);
        console.log(`   🔄 دالة التحديث: ${hasDeviceMutation ? '✅' : '❌'}`);
        console.log(`   🏷️ حقل معرف جهاز2: ${hasDevice1Field ? '✅' : '❌'}`);
        console.log(`   ✏️ أيقونة التعديل: ${hasEditIcon ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.log('   ❌ فشل في قراءة ملف UserProfileDialog');
      console.log(`   📝 Error: ${error.message}`);
    }
    console.log('');

    // اختبار 3: التحقق من إصلاح أيقونة العين في صفحة الوكلاء
    console.log('3️⃣ اختبار إصلاح أيقونة العين في صفحة الوكلاء:');
    totalTests++;
    
    try {
      const fs = require('fs');
      const agentsPageContent = fs.readFileSync('client/src/pages/AgentsPage.jsx', 'utf8');
      
      const hasViewMode = agentsPageContent.includes('viewMode');
      const hasViewModeTrue = agentsPageContent.includes('setViewMode(true)');
      const hasViewModeFalse = agentsPageContent.includes('setViewMode(false)');
      const hasViewTitle = agentsPageContent.includes('عرض تفاصيل الوكيل');
      const hasViewModeInForm = agentsPageContent.includes('viewMode={viewMode}');
      
      if (hasViewMode && hasViewModeTrue && hasViewModeFalse && hasViewTitle && hasViewModeInForm) {
        passedTests++;
        console.log('   ✅ إصلاح أيقونة العين في صفحة الوكلاء مكتمل!');
        console.log('   👁️ وضع المشاهدة: موجود');
        console.log('   🔄 تبديل الأوضاع: موجود');
        console.log('   📋 عنوان المشاهدة: موجود');
        console.log('   📝 تمرير الوضع للنموذج: موجود');
      } else {
        console.log('   ❌ إصلاح أيقونة العين في صفحة الوكلاء غير مكتمل');
        console.log(`   👁️ وضع المشاهدة: ${hasViewMode ? '✅' : '❌'}`);
        console.log(`   🔄 تبديل الأوضاع: ${hasViewModeTrue && hasViewModeFalse ? '✅' : '❌'}`);
        console.log(`   📋 عنوان المشاهدة: ${hasViewTitle ? '✅' : '❌'}`);
        console.log(`   📝 تمرير الوضع للنموذج: ${hasViewModeInForm ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.log('   ❌ فشل في قراءة ملف صفحة الوكلاء');
      console.log(`   📝 Error: ${error.message}`);
    }
    console.log('');

    // اختبار 4: التحقق من إصلاح أيقونة العين في صفحة المستخدمين
    console.log('4️⃣ اختبار إصلاح أيقونة العين في صفحة المستخدمين:');
    totalTests++;
    
    try {
      const fs = require('fs');
      const usersPageContent = fs.readFileSync('client/src/pages/UsersPage.jsx', 'utf8');
      
      const hasViewMode = usersPageContent.includes('viewMode');
      const hasViewModeTrue = usersPageContent.includes('setViewMode(true)');
      const hasViewModeFalse = usersPageContent.includes('setViewMode(false)');
      const hasViewTitle = usersPageContent.includes('عرض تفاصيل المستخدم');
      const hasViewModeInForm = usersPageContent.includes('viewMode={viewMode}');
      
      if (hasViewMode && hasViewModeTrue && hasViewModeFalse && hasViewTitle && hasViewModeInForm) {
        passedTests++;
        console.log('   ✅ إصلاح أيقونة العين في صفحة المستخدمين مكتمل!');
        console.log('   👁️ وضع المشاهدة: موجود');
        console.log('   🔄 تبديل الأوضاع: موجود');
        console.log('   📋 عنوان المشاهدة: موجود');
        console.log('   📝 تمرير الوضع للنموذج: موجود');
      } else {
        console.log('   ❌ إصلاح أيقونة العين في صفحة المستخدمين غير مكتمل');
        console.log(`   👁️ وضع المشاهدة: ${hasViewMode ? '✅' : '❌'}`);
        console.log(`   🔄 تبديل الأوضاع: ${hasViewModeTrue && hasViewModeFalse ? '✅' : '❌'}`);
        console.log(`   📋 عنوان المشاهدة: ${hasViewTitle ? '✅' : '❌'}`);
        console.log(`   📝 تمرير الوضع للنموذج: ${hasViewModeInForm ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.log('   ❌ فشل في قراءة ملف صفحة المستخدمين');
      console.log(`   📝 Error: ${error.message}`);
    }
    console.log('');

    // اختبار 5: التحقق من تحديث التخطيط في UserProfileDialog
    console.log('5️⃣ اختبار تحديث التخطيط في UserProfileDialog:');
    totalTests++;
    
    try {
      const fs = require('fs');
      const userProfileContent = fs.readFileSync('client/src/components/dialogs/UserProfileDialog.jsx', 'utf8');
      
      const hasCompactLayout = userProfileContent.includes('md={4}');
      const hasMinWidth100 = userProfileContent.includes('minWidth: \'100px\'');
      const hasFontSize085 = userProfileContent.includes('fontSize: \'0.85rem\'');
      const hasDevice1InBasicInfo = userProfileContent.includes('معرف جهاز2:') && userProfileContent.includes('user.device1');
      
      if (hasCompactLayout && hasMinWidth100 && hasFontSize085 && hasDevice1InBasicInfo) {
        passedTests++;
        console.log('   ✅ تحديث التخطيط في UserProfileDialog مكتمل!');
        console.log('   📐 تخطيط مضغوط: موجود (md={4})');
        console.log('   📏 عرض أصغر للتسميات: موجود (100px)');
        console.log('   🔤 خط أصغر: موجود (0.85rem)');
        console.log('   📱 معرف جهاز2 في المعلومات الأساسية: موجود');
      } else {
        console.log('   ❌ تحديث التخطيط في UserProfileDialog غير مكتمل');
        console.log(`   📐 تخطيط مضغوط: ${hasCompactLayout ? '✅' : '❌'}`);
        console.log(`   📏 عرض أصغر للتسميات: ${hasMinWidth100 ? '✅' : '❌'}`);
        console.log(`   🔤 خط أصغر: ${hasFontSize085 ? '✅' : '❌'}`);
        console.log(`   📱 معرف جهاز2 في المعلومات الأساسية: ${hasDevice1InBasicInfo ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.log('   ❌ فشل في قراءة ملف UserProfileDialog للتخطيط');
      console.log(`   📝 Error: ${error.message}`);
    }

    console.log('\n' + '='.repeat(70));
    console.log('📋 ملخص اختبار تحديثات نافذة تعديل بيانات المستخدم:');
    console.log(`📊 إجمالي الاختبارات: ${totalTests}`);
    console.log(`✅ الاختبارات الناجحة: ${passedTests}`);
    console.log(`❌ الاختبارات الفاشلة: ${totalTests - passedTests}`);
    console.log(`📈 معدل النجاح: ${Math.round((passedTests / totalTests) * 100)}%`);
    console.log('');

    if (passedTests === totalTests) {
      console.log('🎉 جميع التحديثات تم تطبيقها بنجاح!');
      console.log('✅ API تحديث معرف الجهاز2: يعمل');
      console.log('✅ نافذة تعديل بيانات المستخدم: محدثة');
      console.log('✅ إصلاح أيقونة العين للوكلاء: مكتمل');
      console.log('✅ إصلاح أيقونة العين للمستخدمين: مكتمل');
      console.log('✅ تحديث التخطيط: مكتمل');
      console.log('');
      console.log('🚀 النظام جاهز للاستخدام!');
    } else {
      console.log('⚠️ بعض التحديثات لم تكتمل بعد');
    }

    console.log('\n🔧 التحديثات المطبقة:');
    console.log('   📱 إضافة حقل "معرف جهاز2" قابل للتعديل');
    console.log('   📐 تصغير مربعات اسم المستخدم واسم الدخول');
    console.log('   🔄 إضافة API لتحديث معرف الجهاز2');
    console.log('   👁️ إصلاح أيقونة العين للمشاهدة فقط');
    console.log('   📋 تحديث عناوين النوافذ (عرض/تعديل)');
    console.log('   🎨 تحسين التخطيط والتصميم');
    console.log('');
    console.log('🌐 للاختبار:');
    console.log('   📍 النظام الأساسي: http://localhost:8080');
    console.log('   👤 المستخدمين: قائمة المستخدمين → أيقونة العين');
    console.log('   🏢 الوكلاء: قائمة الوكلاء → أيقونة العين');
    console.log('   📱 معرف جهاز2: الملف الشخصي → تعديل معرف الجهاز2');

  } catch (error) {
    console.error('❌ خطأ في اختبار تحديثات نافذة تعديل بيانات المستخدم:', error.message);
  }
}

testUserProfileUpdates().catch(console.error);
