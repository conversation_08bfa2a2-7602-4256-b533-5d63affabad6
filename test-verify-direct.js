/**
 * اختبار API verify-direct
 */

async function testVerifyDirect() {
  console.log('🧪 اختبار API verify-direct...\n');

  const testCases = [
    {
      name: 'اختبار صحيح - agent001 + client 1004 (token)',
      data: {
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      }
    },
    {
      name: 'اختبار صحيح - testuser + client 1000 (token)',
      data: {
        agent_login_name: 'testuser',
        agent_login_password: 'test123',
        client_code: 1000,
        client_token: 'ABC12345'
      }
    },
    {
      name: 'اختبار صحيح - almutarib + client 1002 (token)',
      data: {
        agent_login_name: 'almutarib',
        agent_login_password: 'almutarib123',
        client_code: 1002,
        client_token: 'DEF54321'
      }
    },
    {
      name: 'اختبار خطأ - وكيل غير صحيح',
      data: {
        agent_login_name: 'wrong_agent',
        agent_login_password: 'wrong_pass',
        client_code: 1004,
        client_token: 'UNdZqPVxrxAX'
      }
    },
    {
      name: 'اختبار خطأ - عميل غير صحيح',
      data: {
        agent_login_name: 'agent001',
        agent_login_password: 'agent123',
        client_code: 9999,
        client_token: 'wrong_token'
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}:`);

    try {
      const response = await fetch('http://localhost:8080/api/external/verify-direct', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(testCase.data)
      });

      console.log(`   📡 Status: ${response.status}`);

      const result = await response.json();
      console.log(`   📝 Response:`, result);

      if (response.ok && result.status === 'success') {
        console.log('   ✅ نجح الاختبار!');
      } else {
        console.log('   ❌ فشل الاختبار (متوقع للحالات الخاطئة)');
      }

    } catch (error) {
      console.log(`   ❌ خطأ في الاختبار: ${error.message}`);
    }

    console.log('');
  }

  // اختبار الوصول الخارجي
  console.log('🌐 اختبار الوصول الخارجي:');
  try {
    const response = await fetch('http://***********:8080/api/external/health');
    console.log(`   📡 Status: ${response.status}`);

    if (response.ok) {
      const result = await response.json();
      console.log('   ✅ الوصول الخارجي يعمل!');
      console.log(`   📊 Database: ${result.data?.database}`);
    } else {
      console.log('   ❌ الوصول الخارجي لا يعمل');
    }
  } catch (error) {
    console.log(`   ❌ خطأ في الوصول الخارجي: ${error.message}`);
  }
}

testVerifyDirect().catch(console.error);
