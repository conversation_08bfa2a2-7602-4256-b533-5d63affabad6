const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');

// تحميل متغيرات البيئة
require('dotenv').config();

// إعداد Prisma بدون logging مفصل
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL || "postgresql://postgres:yemen123@localhost:5432/yemclient_db"
    }
  }
});

const app = express();
const PORT = 8080;

// إنشاء مجلد logs إذا لم يكن موجوداً
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// دالة للكتابة في ملف اللوق
function writeLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;

  console.log(`🔄 SERVER: ${message}`);

  try {
    const logFile = path.join(logsDir, `server-${new Date().toISOString().split('T')[0]}.log`);
    fs.appendFileSync(logFile, logMessage);
  } catch (error) {
    console.error('Log write error:', error.message);
  }
}

// Middleware
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Device-ID']
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress;
  writeLog(`${req.method} ${req.path} from ${clientIP}`);
  next();
});

// Health check
app.get('/health', async (req, res) => {
  try {
    await prisma.$connect();
    const userCount = await prisma.user.count();

    res.json({
      status: 'OK',
      timestamp: new Date().toISOString(),
      database: 'connected',
      userCount: userCount
    });
  } catch (error) {
    writeLog(`Health check failed: ${error.message}`);
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      database: 'disconnected',
      error: error.message
    });
  }
});

// Test database connection
app.get('/api/test-db', async (req, res) => {
  try {
    writeLog('Testing database connection...');

    await prisma.$connect();
    writeLog('✅ Database connected successfully');

    const userCount = await prisma.user.count();
    const clientCount = await prisma.client.count();
    const agentCount = await prisma.agent.count();
    const dataRecordCount = await prisma.dataRecord.count();

    writeLog(`📊 Database stats: Users=${userCount}, Clients=${clientCount}, Agents=${agentCount}, DataRecords=${dataRecordCount}`);

    res.json({
      success: true,
      message: 'Database connection successful',
      stats: {
        users: userCount,
        clients: clientCount,
        agents: agentCount,
        dataRecords: dataRecordCount
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    writeLog(`❌ Database connection failed: ${error.message}`);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Database connection failed'
    });
  }
});

// Auth API
app.post('/api/auth/login', async (req, res) => {
  try {
    const { loginName, password, deviceId } = req.body;
    writeLog(`Login attempt for: ${loginName}`);

    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username: loginName },
          { loginName: loginName }
        ]
      }
    });

    if (!user) {
      writeLog(`❌ User not found: ${loginName}`);
      return res.status(401).json({ success: false, message: 'بيانات الدخول غير صحيحة' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      writeLog(`❌ Invalid password for: ${loginName}`);
      return res.status(401).json({ success: false, message: 'بيانات الدخول غير صحيحة' });
    }

    // التحقق من الجهاز - Device Validation صارم
    if (!deviceId) {
      writeLog(`❌ Device ID missing for: ${loginName}`);
      return res.status(400).json({
        success: false,
        message: 'Device ID مطلوب للدخول'
      });
    }

    let isDeviceAuthorized = false;
    let authorizedDevices = [];

    // فحص device1 أولاً (العمود الجديد)
    if (user.device1) {
      authorizedDevices.push(user.device1);
      if (user.device1 === deviceId) {
        isDeviceAuthorized = true;
        writeLog(`✅ Device authorized via device1: ${deviceId.substring(0, 10)}...`);
      }
    }

    // فحص deviceId (العمود القديم) إذا لم يتم العثور على الجهاز في device1
    if (!isDeviceAuthorized && user.deviceId) {
      const oldDevices = user.deviceId.includes(',')
        ? user.deviceId.split(',').map(id => id.trim())
        : [user.deviceId];

      authorizedDevices = [...authorizedDevices, ...oldDevices];

      if (oldDevices.includes(deviceId)) {
        isDeviceAuthorized = true;
        writeLog(`✅ Device authorized via deviceId: ${deviceId.substring(0, 10)}...`);
      }
    }

    // إذا لم يكن هناك أجهزة مسجلة، السماح بالدخول وتسجيل الجهاز
    if (!user.device1 && !user.deviceId) {
      writeLog('📱 No devices registered, registering current device');

      // تحديث device1 بالجهاز الحالي
      await prisma.user.update({
        where: { id: user.id },
        data: { device1: deviceId }
      });

      isDeviceAuthorized = true;
      authorizedDevices = [deviceId];
    }

    // رفض الدخول إذا لم يكن الجهاز مصرحاً
    if (!isDeviceAuthorized) {
      writeLog(`❌ Device not authorized: ${deviceId.substring(0, 10)}... for user: ${loginName}`);
      writeLog(`   Authorized devices: ${authorizedDevices.join(', ')}`);

      return res.status(403).json({
        success: false,
        message: 'هذا الجهاز غير مصرح له بالدخول',
        error: 'يرجى تسجيل الدخول من أحد الأجهزة المصرح بها',
        authorizedDevices: authorizedDevices.map(id => id.substring(0, 10) + '...'),
        currentDevice: deviceId.substring(0, 10) + '...'
      });
    }

    await prisma.loginAttempt.create({
      data: {
        userId: user.id,
        ipAddress: req.ip || req.connection.remoteAddress,
        deviceId: deviceId,
        success: true,
        userType: 'user'
      }
    });

    // إنشاء token بسيط للجلسة
    const token = `token_${user.id}_${Date.now()}`;

    writeLog(`✅ Login successful for: ${loginName}`);

    res.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        username: user.username,
        loginName: user.loginName,
        permissions: user.permissions
      },
      token: token
    });

  } catch (error) {
    writeLog(`❌ Login error: ${error.message}`);
    res.status(500).json({ success: false, message: 'خطأ في الخادم' });
  }
});

// Dashboard Stats API
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    writeLog('Fetching dashboard stats...');

    const [totalUsers, totalClients, totalAgents, totalDataRecords, totalSecurityRecords] = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.agent.count(),
      prisma.dataRecord.count(),
      prisma.loginAttempt.count()
    ]);

    const activeUsers = await prisma.user.count({
      where: { isActive: true }
    });

    const stats = {
      totalUsers,
      totalClients,
      totalAgents,
      totalDataRecords,
      totalSecurityRecords,
      activeUsers,
      systemHealth: 'excellent',
      timestamp: new Date().toISOString()
    };

    writeLog(`📊 Dashboard stats: Users=${totalUsers}, Clients=${totalClients}, Agents=${totalAgents}, DataRecords=${totalDataRecords}, Security=${totalSecurityRecords}`);
    res.json(stats);

  } catch (error) {
    writeLog(`❌ Dashboard stats error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب الإحصائيات' });
  }
});

// Recent Activity API
app.get('/api/dashboard/recent-activity', async (req, res) => {
  try {
    const recentLogins = await prisma.loginAttempt.findMany({
      take: 5,
      orderBy: { timestamp: 'desc' },
      include: {
        user: { select: { username: true, loginName: true } }
      }
    });

    const activities = recentLogins.map(login => ({
      id: login.id,
      type: login.success ? 'login_success' : 'login_failed',
      description: `${login.success ? 'تسجيل دخول ناجح' : 'محاولة دخول فاشلة'} - ${login.user?.username || login.user?.loginName || 'مستخدم غير معروف'}`,
      timestamp: login.timestamp,
      ip: login.ipAddress
    }));

    res.json(activities);

  } catch (error) {
    writeLog(`❌ Recent activity error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب النشاطات الأخيرة' });
  }
});

// Security Stats API
app.get('/api/security/stats', async (req, res) => {
  try {
    const totalAttempts = await prisma.loginAttempt.count();
    const successfulAttempts = await prisma.loginAttempt.count({ where: { success: true } });
    const failedAttempts = await prisma.loginAttempt.count({ where: { success: false } });

    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    const todayAttempts = await prisma.loginAttempt.count({
      where: { timestamp: { gte: todayStart } }
    });

    res.json({
      totalAttempts,
      successfulAttempts,
      failedAttempts,
      todayAttempts,
      successRate: totalAttempts > 0 ? ((successfulAttempts / totalAttempts) * 100).toFixed(1) : 0
    });

  } catch (error) {
    writeLog(`❌ Security stats error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب إحصائيات الأمان' });
  }
});

// Clients API
app.get('/api/clients', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    writeLog(`Fetching clients: page=${page}, limit=${limit}, skip=${skip}`);

    const [clients, total] = await Promise.all([
      prisma.client.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.client.count()
    ]);

    writeLog(`Found ${clients.length} clients out of ${total} total`);

    // جلب معلومات المستخدمين منفصلة إذا كان هناك userId
    const userIds = [...new Set(clients.map(c => c.userId).filter(Boolean))];
    const users = userIds.length > 0 ? await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, username: true, loginName: true }
    }) : [];

    const formattedClients = clients.map(client => {
      const user = users.find(u => u.id === client.userId);
      return {
        ...client,
        createdByUserName: user ? (user.username || user.loginName) : 'غير محدد'
      };
    });

    res.json({
      data: formattedClients,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    writeLog(`❌ Clients API error: ${error.message}`);
    writeLog(`❌ Error stack: ${error.stack}`);
    res.status(500).json({ error: 'فشل في جلب العملاء', details: error.message });
  }
});

// Agents API
app.get('/api/agents', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [agents, total] = await Promise.all([
      prisma.agent.findMany({
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.agent.count()
    ]);

    res.json({
      data: agents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    writeLog(`❌ Agents API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب الوكلاء' });
  }
});

// Users API
app.get('/api/users', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          username: true,
          loginName: true,
          permissions: true,
          device1: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count()
    ]);

    res.json({
      data: users,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    writeLog(`❌ Users API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب المستخدمين' });
  }
});

// Data Records API
app.get('/api/data-records', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [dataRecords, total] = await Promise.all([
      prisma.dataRecord.findMany({
        skip,
        take: limit,
        include: {
          agent: { select: { agentName: true } },
          client: { select: { clientName: true } }
        },
        orderBy: { operationDate: 'desc' }
      }),
      prisma.dataRecord.count()
    ]);

    const formattedRecords = dataRecords.map(record => ({
      ...record,
      agentName: record.agent?.agentName || 'غير محدد',
      clientName: record.client?.clientName || 'غير محدد'
    }));

    res.json({
      dataRecords: formattedRecords,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        total
      }
    });

  } catch (error) {
    writeLog(`❌ Data records API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب سجلات البيانات' });
  }
});

// Security Login Attempts API
app.get('/api/security/login-attempts', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [attempts, total] = await Promise.all([
      prisma.loginAttempt.findMany({
        skip,
        take: limit,
        include: {
          user: { select: { username: true, loginName: true } },
          agent: { select: { agentName: true } }
        },
        orderBy: { timestamp: 'desc' }
      }),
      prisma.loginAttempt.count()
    ]);

    const formattedAttempts = attempts.map(attempt => ({
      id: attempt.id.toString(),
      type: attempt.success ? 'success' : 'failed',
      username: attempt.user?.username || attempt.user?.loginName || `User ${attempt.userId}`,
      ip: attempt.ipAddress,
      timestamp: attempt.timestamp.toISOString(),
      userAgent: 'System Login',
      deviceId: attempt.deviceId || 'unknown',
      reason: attempt.success ? null : 'Authentication failed',
      userType: attempt.userType,
      agentName: attempt.agent?.agentName
    }));

    res.json({
      attempts: formattedAttempts,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        total
      }
    });

  } catch (error) {
    writeLog(`❌ Login attempts API error: ${error.message}`);
    res.status(500).json({ error: 'فشل في جلب محاولات الدخول' });
  }
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API يعمل بشكل صحيح',
    timestamp: new Date().toISOString(),
    server: 'Stable Server',
    database: 'connected'
  });
});

// External API Health Check
app.get('/api/external/health', async (req, res) => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    res.json({
      status: 'success',
      message: 'API is healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        version: '1.0.0'
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'error',
      message: 'API health check failed',
      error_code: 'HEALTH_CHECK_FAILED'
    });
  }
});

// External Agent Authentication
app.post('/api/external/agent/auth', async (req, res) => {
  try {
    const { login_name, login_password } = req.body;

    if (!login_name || !login_password) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: login_name and login_password',
        error_code: 'VALIDATION_ERROR'
      });
    }

    const agent = await prisma.agent.findFirst({
      where: {
        loginName: login_name,
        isActive: true
      }
    });

    if (!agent) {
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    const isPasswordValid = await bcrypt.compare(login_password, agent.loginPassword);

    if (!isPasswordValid) {
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    res.json({
      status: 'success',
      message: 'Agent authenticated successfully',
      data: {
        agent_id: agent.id,
        agent_name: agent.agentName,
        agency_type: agent.agencyType,
        token: 'temp_token_' + Date.now()
      }
    });

  } catch (error) {
    console.error('Agent authentication error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Authentication service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// External Client Verification (with token authentication)
app.post('/api/external/client/verify', async (req, res) => {
  try {
    const { client_code, token } = req.body;
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid agent token',
        error_code: 'INVALID_AGENT_TOKEN'
      });
    }

    // For now, we'll skip token validation and proceed with client verification
    if (!client_code || !token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: client_code and token',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // Verify client
    const client = await prisma.client.findFirst({
      where: {
        clientCode: client_code
      }
    });

    if (!client) {
      return res.status(404).json({
        status: 'error',
        message: 'Client not found',
        error_code: 'CLIENT_NOT_FOUND'
      });
    }

    if (client.password !== token) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid client token',
        error_code: 'TOKEN_MISMATCH'
      });
    }

    // Return client data based on status
    if (client.status === 1) {
      res.json({
        status: 'success',
        message: 'Client verified successfully',
        data: {
          client_code: client.clientCode,
          client_name: client.clientName,
          app_name: client.appName,
          status: client.status,
          ip_address: client.ipAddress,
          created_date: client.createdAt.toISOString().split('T')[0]
        }
      });
    } else {
      res.json({
        status: 'success',
        message: 'Client found but inactive',
        data: {
          client_code: client.clientCode,
          status: client.status
        }
      });
    }

  } catch (error) {
    console.error('Client verification error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// External Agent Statistics
app.get('/api/external/agent/stats', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid agent token',
        error_code: 'INVALID_AGENT_TOKEN'
      });
    }

    // Get statistics
    const [totalClients, activeClients, totalOperations, successfulOperations] = await Promise.all([
      prisma.client.count(),
      prisma.client.count({ where: { status: 1 } }),
      prisma.dataRecord.count(),
      prisma.dataRecord.count({ where: { operationStatus: 'success' } })
    ]);

    const failedOperations = totalOperations - successfulOperations;
    const successRate = totalOperations > 0 ? ((successfulOperations / totalOperations) * 100).toFixed(1) : '0.0';

    res.json({
      status: 'success',
      data: {
        total_clients: totalClients,
        active_clients: activeClients,
        total_operations: totalOperations,
        successful_operations: successfulOperations,
        failed_operations: failedOperations,
        success_rate: `${successRate}%`
      }
    });

  } catch (error) {
    console.error('Agent stats error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Statistics service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// External Agent Operations Log
app.get('/api/external/agent/operations', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid agent token',
        error_code: 'INVALID_AGENT_TOKEN'
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const [operations, total] = await Promise.all([
      prisma.dataRecord.findMany({
        skip,
        take: limit,
        orderBy: { operationDate: 'desc' },
        select: {
          id: true,
          clientCode: true,
          operationStatus: true,
          operationDate: true,
          clientIpAddress: true,
          agentReference: true
        }
      }),
      prisma.dataRecord.count()
    ]);

    const formattedOperations = operations.map(op => ({
      id: op.id,
      client_code: op.clientCode,
      operation_type: 'verify',
      status: op.operationStatus === 'success' ? 1 : 0,
      timestamp: op.operationDate.toISOString(),
      ip_address: op.clientIpAddress,
      agent_reference: op.agentReference
    }));

    res.json({
      status: 'success',
      data: {
        operations: formattedOperations,
        pagination: {
          page,
          limit,
          total,
          total_pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Agent operations error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Operations service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// External Agent Logout
app.post('/api/external/agent/logout', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid agent token',
        error_code: 'INVALID_AGENT_TOKEN'
      });
    }

    // For now, just return success
    res.json({
      status: 'success',
      message: 'Agent logged out successfully'
    });

  } catch (error) {
    console.error('Agent logout error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Logout service error',
      error_code: 'SERVER_ERROR'
    });
  }
});

// External Direct Verification (legacy support)
app.post('/api/external/verify-direct', async (req, res) => {
  try {
    const {
      agent_login_name,
      agent_login_password,
      client_code,
      client_token
    } = req.body;

    if (!agent_login_name || !agent_login_password || !client_code || !client_token) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields',
        error_code: 'VALIDATION_ERROR'
      });
    }

    // Verify agent
    const agent = await prisma.agent.findFirst({
      where: {
        loginName: agent_login_name,
        isActive: true
      }
    });

    if (!agent) {
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    const isPasswordValid = await bcrypt.compare(agent_login_password, agent.loginPassword);

    if (!isPasswordValid) {
      return res.status(401).json({
        status: 'agent_error'
      });
    }

    // Verify client
    const client = await prisma.client.findFirst({
      where: {
        clientCode: client_code,
        status: 1
      }
    });

    if (!client) {
      return res.status(404).json({
        status: 'client_error'
      });
    }

    // Check if client_token matches password or token field
    let isTokenValid = false;

    // First try direct comparison with token field
    if (client.token === client_token) {
      isTokenValid = true;
    }
    // Then try bcrypt comparison with password field
    else if (client.password && client.password.startsWith('$2b$')) {
      try {
        isTokenValid = await bcrypt.compare(client_token, client.password);
      } catch (error) {
        console.error('Bcrypt comparison error:', error);
      }
    }
    // Finally try direct comparison with password field
    else if (client.password === client_token) {
      isTokenValid = true;
    }

    if (!isTokenValid) {
      return res.status(401).json({
        status: 'client_error'
      });
    }

    // Log successful operation
    await prisma.dataRecord.create({
      data: {
        agentId: agent.id,
        clientId: client.id,
        clientCode: client_code.toString(),
        clientPassword: client_token,
        operationDate: new Date(),
        operationStatus: 1,
        agentReference: Math.floor(Date.now() / 1000),
        clientIpAddress: req.ip || 'unknown'
      }
    });

    res.json({
      status: 'success'
    });

  } catch (error) {
    console.error('Direct verification error:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      status: 'error',
      message: 'Verification service error',
      error_code: 'SERVER_ERROR',
      debug: error.message
    });
  }
});

// Static files AFTER API routes
app.use(express.static(path.join(__dirname, '../client/dist')));

// React app fallback
app.get('*', (req, res) => {
  if (req.path.startsWith('/api/')) {
    return res.status(404).json({ error: 'API endpoint not found', path: req.path });
  }
  res.sendFile(path.join(__dirname, '../client/dist/index.html'));
});

// Error handler
app.use((err, req, res, next) => {
  writeLog(`Server Error: ${err.message}`);
  console.error('Server Error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// Graceful shutdown
process.on('SIGINT', async () => {
  writeLog('Shutting down server...');
  await prisma.$disconnect();
  process.exit(0);
});

// Start server
const server = app.listen(PORT, '0.0.0.0', async () => {
  writeLog(`Stable Server starting on port ${PORT}...`);

  try {
    await prisma.$connect();
    writeLog('✅ Database connected successfully');

    const userCount = await prisma.user.count();
    writeLog(`📊 Users in database: ${userCount}`);

    console.log(`✅ Stable Server running on http://0.0.0.0:${PORT}`);
    console.log(`🌐 External: http://***********:${PORT}`);
    console.log(`🏠 Local: http://localhost:${PORT}`);
    console.log(`🗄️ Database: Connected (${userCount} users)`);

  } catch (error) {
    writeLog(`❌ Database connection failed: ${error.message}`);
    console.error('❌ Database connection failed:', error.message);
  }
});

server.on('error', (err) => {
  writeLog(`Server startup error: ${err.message}`);
  console.error('Server startup error:', err);
});

module.exports = app;
