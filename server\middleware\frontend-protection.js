const fs = require('fs');
const path = require('path');
const { 
  obfuscateHTML, 
  obfuscateJS, 
  obfuscateCSS,
  addMisleadingHeaders,
  removeDevInfo,
  addFakeFingerprints
} = require('../utils/obfuscation');

/**
 * middleware لحماية وإخفاء معلومات Frontend
 */
const protectFrontend = (req, res, next) => {
  const originalSend = res.send;
  const originalSendFile = res.sendFile;
  
  // تعديل res.send لإخفاء HTML
  res.send = function(body) {
    if (typeof body === 'string' && body.includes('<!DOCTYPE html>')) {
      // إخفاء HTML
      body = obfuscateHTML(body);
      body = removeDevInfo(body);
      body = addFakeFingerprints(body);
      
      // إضافة معلومات مضللة
      body = body.replace(
        '<head>',
        `<head>
        <meta name="generator" content="WordPress 5.8.1" />
        <meta name="theme" content="Twenty Twenty-One" />
        <meta name="framework" content="Bootstrap 4.6.0" />
        <meta name="server" content="Apache/2.4.41" />`
      );
    }
    
    return originalSend.call(this, body);
  };
  
  // تعديل res.sendFile لإخفاء الملفات
  res.sendFile = function(filePath, options, callback) {
    // إضافة headers مضللة
    addMisleadingHeaders(res, filePath);
    
    // إذا كان ملف JS أو CSS، قم بإخفائه
    if (filePath.endsWith('.js') || filePath.endsWith('.css')) {
      fs.readFile(filePath, 'utf8', (err, data) => {
        if (err) {
          return originalSendFile.call(this, filePath, options, callback);
        }
        
        let processedData = data;
        
        if (filePath.endsWith('.js')) {
          processedData = obfuscateJS(data);
          processedData = removeDevInfo(processedData);
          processedData = addFakeFingerprints(processedData);
          res.setHeader('Content-Type', 'application/javascript; charset=utf-8');
        } else if (filePath.endsWith('.css')) {
          processedData = obfuscateCSS(data);
          processedData = removeDevInfo(processedData);
          processedData = addFakeFingerprints(processedData);
          res.setHeader('Content-Type', 'text/css; charset=utf-8');
        }
        
        res.send(processedData);
      });
    } else {
      return originalSendFile.call(this, filePath, options, callback);
    }
  };
  
  next();
};

/**
 * middleware لإنشاء ملفات وهمية
 */
const createDecoyRoutes = (app) => {
  // ملفات WordPress وهمية
  app.get('/wp-admin/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ WordPress من IP: ${req.ip}`);
    res.status(404).send(`
      <!DOCTYPE html>
      <html>
      <head><title>404 Not Found</title></head>
      <body>
        <h1>Not Found</h1>
        <p>The requested URL was not found on this server.</p>
        <hr>
        <address>Apache/2.4.41 (Ubuntu) Server at ${req.get('host')} Port 80</address>
      </body>
      </html>
    `);
  });
  
  app.get('/wp-config.php', (req, res) => {
    console.log(`🚨 محاولة وصول لـ wp-config.php من IP: ${req.ip}`);
    res.status(403).send('Forbidden');
  });
  
  app.get('/phpmyadmin/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ phpMyAdmin من IP: ${req.ip}`);
    res.status(404).send('Not Found');
  });
  
  // ملفات إدارة وهمية
  app.get('/admin/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ admin من IP: ${req.ip}`);
    res.status(401).send('Unauthorized');
  });
  
  app.get('/administrator/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ administrator من IP: ${req.ip}`);
    res.status(401).send('Unauthorized');
  });
  
  // ملفات قاعدة البيانات وهمية
  app.get('/database/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ database من IP: ${req.ip}`);
    res.status(403).send('Access Denied');
  });
  
  app.get('/backup/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ backup من IP: ${req.ip}`);
    res.status(403).send('Access Denied');
  });
  
  // ملفات تكوين وهمية
  app.get('/config/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ config من IP: ${req.ip}`);
    res.status(403).send('Forbidden');
  });
  
  app.get('/.env', (req, res) => {
    console.log(`🚨 محاولة وصول لـ .env من IP: ${req.ip}`);
    res.status(404).send('Not Found');
  });
  
  // ملفات تطوير وهمية
  app.get('/src/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ src من IP: ${req.ip}`);
    res.status(404).send('Not Found');
  });
  
  app.get('/node_modules/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ node_modules من IP: ${req.ip}`);
    res.status(404).send('Not Found');
  });
  
  // ملفات Git وهمية
  app.get('/.git/*', (req, res) => {
    console.log(`🚨 محاولة وصول لـ .git من IP: ${req.ip}`);
    res.status(404).send('Not Found');
  });
  
  // robots.txt مضلل
  app.get('/robots.txt', (req, res) => {
    res.setHeader('Content-Type', 'text/plain');
    res.send(`
User-agent: *
Disallow: /wp-admin/
Disallow: /wp-content/
Disallow: /wp-includes/
Disallow: /admin/
Disallow: /administrator/
Allow: /

Sitemap: http://${req.get('host')}/sitemap.xml
    `.trim());
  });
  
  // sitemap.xml مضلل
  app.get('/sitemap.xml', (req, res) => {
    res.setHeader('Content-Type', 'application/xml');
    res.send(`
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>http://${req.get('host')}/</loc>
    <lastmod>2024-01-01</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>http://${req.get('host')}/about</loc>
    <lastmod>2024-01-01</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>
    `.trim());
  });
};

/**
 * middleware لإخفاء معلومات الخادم في الاستجابات
 */
const hideServerDetails = (req, res, next) => {
  // إزالة headers تكشف معلومات
  res.removeHeader('X-Powered-By');
  res.removeHeader('Server');
  res.removeHeader('X-AspNet-Version');
  res.removeHeader('X-AspNetMvc-Version');
  res.removeHeader('X-SourceFiles');
  res.removeHeader('X-Runtime');
  
  // إضافة headers مضللة
  res.setHeader('Server', 'Apache/2.4.41 (Ubuntu)');
  res.setHeader('X-Powered-By', 'PHP/7.4.3');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // إضافة معلومات WordPress مضللة
  if (req.path === '/' || req.path.endsWith('.html')) {
    res.setHeader('X-Generator', 'WordPress 5.8.1');
    res.setHeader('X-Pingback', `http://${req.get('host')}/xmlrpc.php`);
  }
  
  next();
};

/**
 * middleware لمنع fingerprinting
 */
const preventFingerprinting = (req, res, next) => {
  // إضافة تأخير عشوائي صغير
  const delay = Math.floor(Math.random() * 50) + 10; // 10-60ms
  
  setTimeout(() => {
    // تغيير ترتيب headers
    const headers = res.getHeaders();
    Object.keys(headers).forEach(key => {
      res.removeHeader(key);
    });
    
    // إعادة إضافة headers بترتيب عشوائي
    const headerKeys = Object.keys(headers).sort(() => Math.random() - 0.5);
    headerKeys.forEach(key => {
      res.setHeader(key, headers[key]);
    });
    
    next();
  }, delay);
};

module.exports = {
  protectFrontend,
  createDecoyRoutes,
  hideServerDetails,
  preventFingerprinting
};
