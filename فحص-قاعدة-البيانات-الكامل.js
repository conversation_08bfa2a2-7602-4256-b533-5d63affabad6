/**
 * فحص شامل لقاعدة البيانات والخادم
 * Complete Database and Server Check
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function checkDatabase() {
  console.log('🔍 فحص شامل لقاعدة البيانات والخادم...\n');

  try {
    // 1. فحص الاتصال بقاعدة البيانات
    console.log('1️⃣ فحص الاتصال بقاعدة البيانات:');
    await prisma.$connect();
    console.log('   ✅ الاتصال بقاعدة البيانات نجح\n');

    // 2. فحص جدول المستخدمين
    console.log('2️⃣ فحص جدول المستخدمين:');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        password: true,
        device1: true,
        isActive: true,
        permissions: true,
        createdAt: true
      }
    });
    
    console.log(`   📊 عدد المستخدمين: ${users.length}`);
    
    if (users.length > 0) {
      console.log('   👥 قائمة المستخدمين:');
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ID: ${user.id}`);
        console.log(`      👤 اسم المستخدم: ${user.username}`);
        console.log(`      🔑 اسم الدخول: ${user.loginName}`);
        console.log(`      🔒 كلمة المرور (مشفرة): ${user.password ? user.password.substring(0, 20) + '...' : 'غير موجودة'}`);
        console.log(`      📱 الجهاز: ${user.device1 || 'غير محدد'}`);
        console.log(`      ✅ نشط: ${user.isActive ? 'نعم' : 'لا'}`);
        console.log(`      🔐 الصلاحيات: ${JSON.stringify(user.permissions)}`);
        console.log(`      📅 تاريخ الإنشاء: ${user.createdAt}`);
        console.log('');
      });
    } else {
      console.log('   ❌ لا توجد مستخدمين في قاعدة البيانات!');
    }

    // 3. فحص جدول العملاء
    console.log('3️⃣ فحص جدول العملاء:');
    const clients = await prisma.client.findMany({
      take: 5,
      select: {
        id: true,
        clientName: true,
        clientCode: true,
        appName: true,
        ipAddress: true,
        status: true,
        createdAt: true
      }
    });
    
    console.log(`   📊 عدد العملاء: ${clients.length}`);
    if (clients.length > 0) {
      console.log('   👥 عينة من العملاء:');
      clients.forEach((client, index) => {
        console.log(`   ${index + 1}. ${client.clientName} (${client.clientCode}) - ${client.appName}`);
      });
    }
    console.log('');

    // 4. فحص جدول الوكلاء
    console.log('4️⃣ فحص جدول الوكلاء:');
    const agents = await prisma.agent.findMany({
      take: 5,
      select: {
        id: true,
        agentName: true,
        agencyName: true,
        loginName: true,
        isActive: true,
        createdAt: true
      }
    });
    
    console.log(`   📊 عدد الوكلاء: ${agents.length}`);
    if (agents.length > 0) {
      console.log('   🏢 عينة من الوكلاء:');
      agents.forEach((agent, index) => {
        console.log(`   ${index + 1}. ${agent.agentName} - ${agent.agencyName} (${agent.loginName})`);
      });
    }
    console.log('');

    // 5. فحص جدول سجلات البيانات
    console.log('5️⃣ فحص جدول سجلات البيانات:');
    const dataRecords = await prisma.dataRecord.findMany({
      take: 5,
      select: {
        id: true,
        agentId: true,
        clientId: true,
        clientCode: true,
        agentReference: true,
        operationDate: true,
        operationStatus: true
      }
    });
    
    console.log(`   📊 عدد سجلات البيانات: ${dataRecords.length}`);
    if (dataRecords.length > 0) {
      console.log('   📋 عينة من سجلات البيانات:');
      dataRecords.forEach((record, index) => {
        console.log(`   ${index + 1}. Agent: ${record.agentId}, Client: ${record.clientCode}, Ref: ${record.agentReference}`);
      });
    }
    console.log('');

    // 6. فحص جدول محاولات الدخول
    console.log('6️⃣ فحص جدول محاولات الدخول:');
    const loginAttempts = await prisma.loginAttempt.findMany({
      take: 5,
      select: {
        id: true,
        userId: true,
        agentId: true,
        success: true,
        ipAddress: true,
        deviceId: true,
        timestamp: true,
        userType: true
      },
      orderBy: { timestamp: 'desc' }
    });
    
    console.log(`   📊 عدد محاولات الدخول: ${loginAttempts.length}`);
    if (loginAttempts.length > 0) {
      console.log('   🔒 آخر محاولات الدخول:');
      loginAttempts.forEach((attempt, index) => {
        console.log(`   ${index + 1}. ${attempt.success ? '✅' : '❌'} ${attempt.userType} - ${attempt.ipAddress} (${attempt.timestamp})`);
      });
    }
    console.log('');

    // 7. اختبار تسجيل الدخول مع المستخدم الأول
    if (users.length > 0) {
      console.log('7️⃣ اختبار تسجيل الدخول:');
      const testUser = users[0];
      console.log(`   🧪 اختبار مع المستخدم: ${testUser.username} (${testUser.loginName})`);
      
      // اختبار كلمات مرور مختلفة
      const passwordsToTest = ['admin123', 'hash8080', 'yemen123', '123456', 'admin'];
      
      for (const testPassword of passwordsToTest) {
        try {
          const isValid = await bcrypt.compare(testPassword, testUser.password);
          console.log(`   🔑 كلمة المرور "${testPassword}": ${isValid ? '✅ صحيحة' : '❌ خاطئة'}`);
          
          if (isValid) {
            console.log(`   🎉 كلمة المرور الصحيحة هي: ${testPassword}`);
            break;
          }
        } catch (error) {
          console.log(`   ❌ خطأ في اختبار كلمة المرور "${testPassword}": ${error.message}`);
        }
      }
    }

    // 8. اختبار الخادم
    console.log('\n8️⃣ اختبار الخادم:');
    try {
      const response = await fetch('http://localhost:8080/health');
      if (response.ok) {
        const data = await response.json();
        console.log('   ✅ الخادم يعمل بشكل صحيح');
        console.log(`   📊 حالة الخادم: ${data.status}`);
      } else {
        console.log(`   ⚠️ الخادم يستجيب لكن بحالة: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ الخادم لا يستجيب: ${error.message}`);
    }

    // 9. اختبار APIs
    console.log('\n9️⃣ اختبار APIs:');
    const apisToTest = [
      '/api/dashboard/stats',
      '/api/clients',
      '/api/agents',
      '/api/users',
      '/api/data-records',
      '/api/security/stats'
    ];

    for (const api of apisToTest) {
      try {
        const response = await fetch(`http://localhost:8080${api}`);
        if (response.ok) {
          const data = await response.json();
          console.log(`   ✅ ${api}: يعمل`);
          
          // عرض بعض التفاصيل
          if (api === '/api/dashboard/stats') {
            console.log(`      📊 العملاء: ${data.totalClients || 0}, الوكلاء: ${data.totalAgents || 0}, البيانات: ${data.totalDataRecords || 0}`);
          } else if (Array.isArray(data)) {
            console.log(`      📋 عدد السجلات: ${data.length}`);
          } else if (data.data && Array.isArray(data.data)) {
            console.log(`      📋 عدد السجلات: ${data.data.length}`);
          } else if (data.dataRecords && Array.isArray(data.dataRecords)) {
            console.log(`      📋 عدد السجلات: ${data.dataRecords.length}`);
          }
        } else {
          console.log(`   ❌ ${api}: خطأ ${response.status}`);
        }
      } catch (error) {
        console.log(`   ❌ ${api}: فشل في الاتصال`);
      }
    }

    console.log('\n✅ انتهى الفحص الشامل!');

  } catch (error) {
    console.error('❌ خطأ في فحص قاعدة البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الفحص
checkDatabase().catch(console.error);
