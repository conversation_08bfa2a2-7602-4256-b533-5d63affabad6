const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function fixHashdiDevices() {
  try {
    console.log('🔧 إصلاح أجهزة المستخدم محمد الحاشدي')
    console.log('=====================================')

    // البحث عن المستخدم محمد الحاشدي
    const user = await prisma.user.findUnique({
      where: { loginName: 'hash8080' },
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true,
        isActive: true
      }
    })

    if (!user) {
      console.log('❌ لم يتم العثور على المستخدم hash8080')
      return
    }

    console.log(`👤 المستخدم: ${user.username}`)
    console.log(`🆔 معرف المستخدم: ${user.id}`)
    console.log(`✅ نشط: ${user.isActive ? 'نعم' : 'لا'}`)
    console.log(`📱 الأجهزة الحالية: ${user.deviceId || 'غير محدد'}`)

    // الأجهزة المطلوبة
    const device1 = 'honbi5nms_1751046183491'  // الجهاز القديم
    const device2 = 'b78dex9jv_1751070014503'  // الجهاز الجديد

    // إنشاء قائمة الأجهزة الصحيحة
    const correctDeviceList = `${device1},${device2}`

    console.log(`📱 الأجهزة المطلوبة: ${correctDeviceList}`)

    // تحديث المستخدم
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: {
        deviceId: correctDeviceList,
        isActive: true  // التأكد من أن المستخدم نشط
      }
    })

    console.log('✅ تم تحديث المستخدم بنجاح!')

    // حذف جميع محاولات تسجيل الدخول القديمة للمستخدم
    await prisma.loginAttempt.deleteMany({
      where: { userId: user.id }
    })

    console.log('🗑️ تم حذف محاولات تسجيل الدخول القديمة')

    // إنشاء محاولة تسجيل دخول ناجحة لكل جهاز
    await prisma.loginAttempt.createMany({
      data: [
        {
          userId: user.id,
          deviceId: device1,
          ipAddress: '*************',
          success: true,
          userType: 'user',
          timestamp: new Date()
        },
        {
          userId: user.id,
          deviceId: device2,
          ipAddress: '*************',
          success: true,
          userType: 'user',
          timestamp: new Date()
        }
      ]
    })

    console.log('📝 تم إنشاء محاولات تسجيل دخول ناجحة للجهازين')

    // عرض النتيجة النهائية
    const finalDevices = updatedUser.deviceId.split(',').map(id => id.trim())

    console.log('')
    console.log('🎉 النتيجة النهائية:')
    console.log(`👤 المستخدم: ${updatedUser.username}`)
    console.log(`🆔 معرف المستخدم: ${updatedUser.id}`)
    console.log(`✅ نشط: ${updatedUser.isActive ? 'نعم' : 'لا'}`)
    console.log(`📱 عدد الأجهزة: ${finalDevices.length}`)
    console.log(`📱 قائمة الأجهزة:`)
    finalDevices.forEach((device, index) => {
      console.log(`   ${index + 1}. ${device}`)
    })

    console.log('')
    console.log('🔐 الآن يمكن للمستخدم تسجيل الدخول من أي من الجهازين!')
    console.log('')
    console.log('🧪 اختبار تسجيل الدخول:')
    console.log(`curl -X POST http://localhost:8080/api/auth/login \\`)
    console.log(`  -H "Content-Type: application/json" \\`)
    console.log(`  -d '{"loginName":"hash8080","password":"hash8080","deviceId":"${device1}"}'`)
    console.log('')
    console.log(`curl -X POST http://localhost:8080/api/auth/login \\`)
    console.log(`  -H "Content-Type: application/json" \\`)
    console.log(`  -d '{"loginName":"hash8080","password":"hash8080","deviceId":"${device2}"}'`)

  } catch (error) {
    console.error('❌ خطأ في إصلاح الأجهزة:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixHashdiDevices()
