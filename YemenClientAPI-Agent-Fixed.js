/**
 * Yemen Client Management System - Agent API SDK (Fixed Version)
 * مكتبة الربط للوكلاء - النسخة المُصلحة
 * 
 * @version 2.0.0
 * <AUTHOR> Client Management Team
 * @description مكتبة JavaScript للوصول إلى نظام إدارة العملاء اليمني
 */

class YemenClientAgentAPI {
    /**
     * إنشاء مثيل جديد من مكتبة الوكيل
     * @param {string} serverUrl - عنوان الخادم (مثال: http://***********:8080)
     * @param {string} agentLoginName - اسم تسجيل الدخول للوكيل
     * @param {string} agentLoginPassword - كلمة مرور الوكيل
     */
    constructor(serverUrl, agentLoginName, agentLoginPassword) {
        this.serverUrl = serverUrl.replace(/\/$/, ''); // إزالة / من النهاية
        this.agentLoginName = agentLoginName;
        this.agentLoginPassword = agentLoginPassword;
        this.isAuthenticated = false;
        this.agentInfo = null;
        this.lastError = null;
    }

    /**
     * فحص حالة الخادم
     * @returns {Promise<Object>} نتيجة فحص الحالة
     */
    async checkHealth() {
        try {
            const response = await fetch(`${this.serverUrl}/api/external/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                return {
                    success: true,
                    status: 'healthy',
                    database: data.data?.database,
                    timestamp: data.data?.timestamp,
                    version: data.data?.version
                };
            } else {
                return {
                    success: false,
                    status: 'unhealthy',
                    error: data.message || 'Health check failed'
                };
            }

        } catch (error) {
            this.lastError = error.message;
            return {
                success: false,
                status: 'error',
                error: error.message
            };
        }
    }

    /**
     * التحقق من صحة بيانات الوكيل (اختياري - للاختبار)
     * @returns {Promise<Object>} نتيجة التحقق من الوكيل
     */
    async authenticate() {
        try {
            // استخدام verify-direct للتحقق من الوكيل مع عميل وهمي
            const testResult = await this.verifyClient('1004', 'UNdZqPVxrxAX');
            
            if (testResult.success) {
                this.isAuthenticated = true;
                this.agentInfo = {
                    loginName: this.agentLoginName,
                    status: 'authenticated'
                };
                
                return {
                    success: true,
                    message: 'Agent authenticated successfully',
                    agent_name: this.agentLoginName,
                    status: 'authenticated'
                };
            } else if (testResult.error === 'agent_error') {
                this.isAuthenticated = false;
                return {
                    success: false,
                    error: 'Invalid agent credentials',
                    error_code: 'AGENT_AUTH_FAILED'
                };
            } else {
                // إذا كان الخطأ في العميل، فالوكيل صحيح
                this.isAuthenticated = true;
                this.agentInfo = {
                    loginName: this.agentLoginName,
                    status: 'authenticated'
                };
                
                return {
                    success: true,
                    message: 'Agent authenticated successfully',
                    agent_name: this.agentLoginName,
                    status: 'authenticated'
                };
            }

        } catch (error) {
            this.lastError = error.message;
            this.isAuthenticated = false;
            return {
                success: false,
                error: error.message,
                error_code: 'NETWORK_ERROR'
            };
        }
    }

    /**
     * التحقق من العميل (الطريقة المباشرة)
     * @param {string|number} clientCode - رمز العميل
     * @param {string} clientToken - رمز العميل (كلمة المرور أو التوكن)
     * @returns {Promise<Object>} نتيجة التحقق من العميل
     */
    async verifyClient(clientCode, clientToken) {
        try {
            // التأكد من أن clientCode رقم
            const numericClientCode = parseInt(clientCode);
            if (isNaN(numericClientCode)) {
                return {
                    success: false,
                    error: 'client_error',
                    message: 'Client code must be a valid number',
                    error_code: 'INVALID_CLIENT_CODE'
                };
            }

            const requestData = {
                agent_login_name: this.agentLoginName,
                agent_login_password: this.agentLoginPassword,
                client_code: numericClientCode,
                client_token: clientToken
            };

            const response = await fetch(`${this.serverUrl}/api/external/verify-direct`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            const data = await response.json();

            // معالجة النتائج
            if (response.ok && data.status === 'success') {
                return {
                    success: true,
                    status: 'success',
                    client_status: data.client_status,
                    client_active: data.client_status === 1,
                    message: data.client_status === 1 ? 'Client is active' : 'Client is inactive',
                    client_code: numericClientCode,
                    verification_time: new Date().toISOString()
                };
            } else {
                // معالجة الأخطاء
                let errorType = 'unknown_error';
                let errorMessage = 'Verification failed';

                switch (data.status) {
                    case 'agent_error':
                        errorType = 'agent_error';
                        errorMessage = 'Invalid agent credentials';
                        break;
                    case 'client_error':
                        errorType = 'client_error';
                        errorMessage = 'Client not found or invalid token';
                        break;
                    case 'error':
                        errorType = 'server_error';
                        errorMessage = 'Server error occurred';
                        break;
                    default:
                        errorType = 'unknown_error';
                        errorMessage = data.message || 'Unknown error';
                }

                this.lastError = errorMessage;

                return {
                    success: false,
                    error: errorType,
                    message: errorMessage,
                    error_code: data.error_code || 'VERIFICATION_FAILED',
                    client_code: numericClientCode,
                    http_status: response.status
                };
            }

        } catch (error) {
            this.lastError = error.message;
            return {
                success: false,
                error: 'network_error',
                message: error.message,
                error_code: 'NETWORK_ERROR'
            };
        }
    }

    /**
     * التحقق من عدة عملاء دفعة واحدة
     * @param {Array} clients - مصفوفة من العملاء [{code, token}, ...]
     * @returns {Promise<Array>} نتائج التحقق من جميع العملاء
     */
    async verifyMultipleClients(clients) {
        const results = [];

        for (const client of clients) {
            const result = await this.verifyClient(client.code, client.token);
            results.push({
                client_code: client.code,
                ...result
            });

            // تأخير قصير بين الطلبات لتجنب إرهاق الخادم
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        return results;
    }

    /**
     * الحصول على معلومات الوكيل
     * @returns {Object} معلومات الوكيل
     */
    getAgentInfo() {
        return {
            loginName: this.agentLoginName,
            isAuthenticated: this.isAuthenticated,
            agentInfo: this.agentInfo,
            serverUrl: this.serverUrl
        };
    }

    /**
     * الحصول على آخر خطأ
     * @returns {string|null} آخر خطأ حدث
     */
    getLastError() {
        return this.lastError;
    }

    /**
     * مسح آخر خطأ
     */
    clearLastError() {
        this.lastError = null;
    }

    /**
     * إنشاء تقرير شامل عن حالة النظام
     * @returns {Promise<Object>} تقرير شامل
     */
    async generateSystemReport() {
        const report = {
            timestamp: new Date().toISOString(),
            agent: this.getAgentInfo(),
            server: null,
            test_results: []
        };

        try {
            // فحص حالة الخادم
            report.server = await this.checkHealth();

            // اختبار التحقق من الوكيل
            const authResult = await this.authenticate();
            report.test_results.push({
                test: 'agent_authentication',
                result: authResult
            });

            // اختبار عملاء تجريبيين
            const testClients = [
                { code: 1000, token: 'ABC12345', name: 'محمد علي الحاشدي' },
                { code: 1004, token: 'UNdZqPVxrxAX', name: 'تجربة جديدة' }
            ];

            for (const client of testClients) {
                const clientResult = await this.verifyClient(client.code, client.token);
                report.test_results.push({
                    test: `client_verification_${client.code}`,
                    client_name: client.name,
                    result: clientResult
                });
            }

            // حساب الإحصائيات
            const totalTests = report.test_results.length;
            const passedTests = report.test_results.filter(t => t.result.success).length;
            
            report.summary = {
                total_tests: totalTests,
                passed_tests: passedTests,
                failed_tests: totalTests - passedTests,
                success_rate: Math.round((passedTests / totalTests) * 100),
                overall_status: passedTests === totalTests ? 'all_passed' : 'some_failed'
            };

        } catch (error) {
            report.error = error.message;
            report.summary = {
                overall_status: 'error',
                error: error.message
            };
        }

        return report;
    }
}

// تصدير الكلاس للاستخدام في Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = YemenClientAgentAPI;
}

// تصدير الكلاس للاستخدام في المتصفح
if (typeof window !== 'undefined') {
    window.YemenClientAgentAPI = YemenClientAgentAPI;
}

/**
 * مثال على الاستخدام:
 * 
 * // في Node.js:
 * const YemenClientAgentAPI = require('./YemenClientAPI-Agent-Fixed.js');
 * 
 * // في المتصفح:
 * // <script src="YemenClientAPI-Agent-Fixed.js"></script>
 * 
 * // إنشاء مثيل جديد
 * const agent = new YemenClientAgentAPI(
 *   'http://***********:8080',
 *   'agent001',
 *   'agent123'
 * );
 * 
 * // فحص حالة الخادم
 * const health = await agent.checkHealth();
 * console.log('Server Health:', health);
 * 
 * // التحقق من الوكيل
 * const auth = await agent.authenticate();
 * console.log('Agent Auth:', auth);
 * 
 * // التحقق من عميل
 * const client = await agent.verifyClient(1004, 'UNdZqPVxrxAX');
 * console.log('Client Verification:', client);
 * 
 * // إنشاء تقرير شامل
 * const report = await agent.generateSystemReport();
 * console.log('System Report:', report);
 */
