/**
 * مكتبة ربط نظام إدارة العملاء اليمني - للعملاء
 * Yemen Client Management System - Client SDK
 *
 * @version 1.0.0
 * <AUTHOR> Client Management Team
 * @license MIT
 */

class YemenClientAPI {
  /**
   * إنشاء مثيل جديد من API العميل
   * @param {string} baseUrl - عنوان الخادم الأساسي
   * @param {string|number} clientCode - رمز العميل
   * @param {string} clientToken - توكن العميل
   */
  constructor(baseUrl, clientCode, clientToken) {
    this.baseUrl = baseUrl.replace(/\/$/, '') // إزالة / من النهاية
    this.apiUrl = `${this.baseUrl}/api/external`
    this.clientCode = clientCode.toString()
    this.clientToken = clientToken
    this.clientInfo = null
  }

  /**
   * التحقق من صحة بيانات العميل عبر وكيل
   * @param {string} agentLoginName - اسم تسجيل دخول الوكيل
   * @param {string} agentPassword - كلمة مرور الوكيل
   * @returns {Promise<Object>} نتيجة التحقق
   */
  async verifyThroughAgent(agentLoginName, agentPassword) {
    try {
      // 1. مصادقة الوكيل أولاً
      console.log('🔐 Authenticating agent...')
      const authResponse = await this._makeRequest('POST', '/agent/auth', {
        login_name: agentLoginName,
        login_password: agentPassword
      })

      if (authResponse.status !== 'success') {
        throw new Error('Agent authentication failed: ' + authResponse.message)
      }

      const agentToken = authResponse.data.token
      console.log('✅ Agent authenticated:', authResponse.data.agent_name)

      // 2. التحقق من العميل
      console.log('👤 Verifying client...')
      const verifyResponse = await this._makeRequest('POST', '/client/verify', {
        client_code: this.clientCode,
        token: this.clientToken
      }, agentToken)

      if (verifyResponse.status === 'success') {
        this.clientInfo = verifyResponse.data
        console.log('✅ Client verified:', this.clientInfo.client_name)

        return {
          success: true,
          client: this.clientInfo,
          agent: {
            id: authResponse.data.agent_id,
            name: authResponse.data.agent_name,
            type: authResponse.data.agency_type
          }
        }
      } else {
        console.log('❌ Client verification failed:', verifyResponse.message)
        return {
          success: false,
          error: verifyResponse.message,
          errorCode: verifyResponse.error_code
        }
      }

    } catch (error) {
      console.error('❌ Verification error:', error.message)
      return {
        success: false,
        error: error.message,
        errorCode: 'VERIFICATION_ERROR'
      }
    }
  }

  /**
   * فحص حالة النظام
   * @returns {Promise<Object>} حالة النظام
   */
  async checkSystemHealth() {
    try {
      const response = await this._makeRequest('GET', '/health')
      return {
        success: true,
        health: response.data,
        message: response.message
      }
    } catch (error) {
      console.error('❌ Health check error:', error.message)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * التحقق من صحة بيانات العميل (بدون وكيل - للاختبار فقط)
   * ملاحظة: هذه الطريقة تتطلب توكن وكيل صالح مسبقاً
   * @param {string} agentToken - توكن الوكيل
   * @returns {Promise<Object>} نتيجة التحقق
   */
  async verifyWithToken(agentToken) {
    try {
      const response = await this._makeRequest('POST', '/client/verify', {
        client_code: this.clientCode,
        token: this.clientToken
      }, agentToken)

      if (response.status === 'success') {
        this.clientInfo = response.data
        return {
          success: true,
          client: this.clientInfo
        }
      } else {
        return {
          success: false,
          error: response.message,
          errorCode: response.error_code
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        errorCode: 'VERIFICATION_ERROR'
      }
    }
  }

  /**
   * إرسال طلب HTTP
   * @private
   * @param {string} method - نوع الطلب
   * @param {string} endpoint - نقطة النهاية
   * @param {Object} data - البيانات المرسلة
   * @param {string} authToken - توكن المصادقة
   * @returns {Promise<Object>} الاستجابة
   */
  async _makeRequest(method, endpoint, data = null, authToken = null) {
    const url = `${this.apiUrl}${endpoint}`
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'YemenClientAPI-Client/1.0.0'
      }
    }

    if (authToken) {
      options.headers['Authorization'] = `Bearer ${authToken}`
    }

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data)
    }

    // استخدام fetch في Node.js أو المتصفح
    let fetch
    if (typeof window !== 'undefined') {
      // في المتصفح
      fetch = window.fetch
    } else {
      // في Node.js
      try {
        const nodeFetch = require('node-fetch')
        fetch = nodeFetch.default || nodeFetch
      } catch (e) {
        throw new Error('node-fetch is required for Node.js. Install it with: npm install node-fetch')
      }
    }

    const response = await fetch(url, options)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    return await response.json()
  }

  /**
   * الحصول على معلومات العميل المحفوظة
   * @returns {Object|null} معلومات العميل
   */
  getClientInfo() {
    return this.clientInfo
  }

  /**
   * تحديث بيانات العميل
   * @param {string|number} newClientCode - رمز العميل الجديد
   * @param {string} newClientToken - توكن العميل الجديد
   */
  updateCredentials(newClientCode, newClientToken) {
    this.clientCode = newClientCode.toString()
    this.clientToken = newClientToken
    this.clientInfo = null // إعادة تعيين المعلومات المحفوظة
  }

  /**
   * التحقق من صحة بيانات الاعتماد
   * @returns {boolean} صحيحة أم لا
   */
  validateCredentials() {
    return !!(this.clientCode && this.clientToken &&
             this.clientCode.length > 0 && this.clientToken.length >= 8)
  }
}

// تصدير للاستخدام في Node.js والمتصفح
if (typeof module !== 'undefined' && module.exports) {
  module.exports = YemenClientAPI
} else if (typeof window !== 'undefined') {
  window.YemenClientAPI = YemenClientAPI
}

/**
 * مثال على الاستخدام:
 *
 * // إنشاء مثيل جديد للعميل
 * const client = new YemenClientAPI(
 *   'http://185.11.8.26:8080',
 *   '1000',
 *   'ABC12345'
 * )
 *
 * // التحقق من العميل عبر وكيل
 * const result = await client.verifyThroughAgent('agent001', 'agent123')
 *
 * if (result.success) {
 *   console.log('Client verified:', result.client.client_name)
 *   console.log('Through agent:', result.agent.name)
 * } else {
 *   console.log('Verification failed:', result.error)
 * }
 *
 * // فحص حالة النظام
 * const health = await client.checkSystemHealth()
 * console.log('System health:', health)
 */
