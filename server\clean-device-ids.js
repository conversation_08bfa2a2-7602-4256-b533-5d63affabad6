const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function cleanDeviceIds() {
  try {
    console.log('🧹 تنظيف معرفات الأجهزة من البادئة device_')
    console.log('===============================================')
    
    // الحصول على جميع المستخدمين
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        loginName: true,
        deviceId: true
      }
    })

    let updatedCount = 0

    for (const user of users) {
      if (user.deviceId) {
        console.log(`👤 المستخدم: ${user.username}`)
        console.log(`   - معرف الجهاز الحالي: ${user.deviceId}`)
        
        // تنظيف معرفات الأجهزة
        let cleanedDeviceIds = user.deviceId
        
        // إزالة البادئة device_ من جميع المعرفات
        if (cleanedDeviceIds.includes('device_')) {
          cleanedDeviceIds = cleanedDeviceIds
            .split(',')  // تقسيم المعرفات المتعددة
            .map(id => id.trim().replace(/^device_/, ''))  // إزالة البادئة من كل معرف
            .join(',')  // دمج المعرفات مرة أخرى
          
          // تحديث قاعدة البيانات
          await prisma.user.update({
            where: { id: user.id },
            data: { deviceId: cleanedDeviceIds }
          })
          
          console.log(`   - معرف الجهاز الجديد: ${cleanedDeviceIds}`)
          console.log(`   ✅ تم التحديث بنجاح`)
          updatedCount++
        } else {
          console.log(`   ℹ️  المعرف نظيف بالفعل`)
        }
        console.log('   ---')
      }
    }

    console.log(`🎉 تم تنظيف معرفات الأجهزة بنجاح!`)
    console.log(`📊 عدد المستخدمين المحدثين: ${updatedCount}`)

  } catch (error) {
    console.error('❌ خطأ في تنظيف معرفات الأجهزة:', error)
  } finally {
    await prisma.$disconnect()
  }
}

cleanDeviceIds()
